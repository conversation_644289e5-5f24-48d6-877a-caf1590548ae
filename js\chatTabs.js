/**
 * Chat Tabs functionality
 * Handles switching between different chat type tabs (world, zone, recruit, guild)
 */

const ChatTabs = {
    activeChatType: 'world', // Default active chat type
    chatTypeButtons: [],

    /**
     * Initialize the chat type tabs
     */
    init: function() {
        console.log('ChatTabs: Initializing chat type tabs...');

        this.chatTypeButtons = document.querySelectorAll('.chat-type-button');

        if (this.chatTypeButtons.length === 0) {
            console.error('ChatTabs: No chat type buttons found with class .chat-type-button');
            return;
        }

        this.chatTypeButtons.forEach(button => {
            button.addEventListener('click', () => {
                const chatType = button.dataset.chatType;
                if (chatType) {
                    this.switchChatType(chatType);
                } else {
                    console.error('ChatTabs: Button is missing data-chat-type attribute', button);
                }
            });
        });

        // Initialize with the default tab active
        this.switchChatType(this.activeChatType);

        console.log('ChatTabs: Chat type tabs initialized with', this.chatTypeButtons.length, 'buttons.');
    },

    /**
     * Switch between chat type tabs
     * @param {string} chatType - The type of the chat to switch to (e.g., 'world', 'zone')
     */
    switchChatType: function(chatType) {
        if (!chatType) {
            console.error('ChatTabs: switchChatType called with no chatType.');
            return;
        }
        console.log('ChatTabs: Switching to chat type:', chatType);

        this.activeChatType = chatType;

        this.chatTypeButtons.forEach(button => {
            if (button.dataset.chatType === chatType) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });

        // Update placeholder of the chat input based on the active tab
        const chatInput = document.getElementById('chat-input');
        if (chatInput) {
            chatInput.placeholder = `Enter ${chatType} message...`;
        }

        // Request chat history for the new chat type
        this.requestChatHistory(chatType);

        // Note: UI.filterAndDisplayChatMessages() will be called automatically
        // when the chat history is received from the server in game.js
    },

    /**
     * Request chat history for a specific chat type
     * @param {string} chatType - The type of chat to request history for
     */
    requestChatHistory: function(chatType) {
        if (typeof Game !== 'undefined' && Game.socket && Game.socket.connected) {
            console.log(`ChatTabs: Requesting chat history for type: ${chatType}`);
            if (chatType === 'zone' && typeof Player !== 'undefined') {
                console.log(`ChatTabs: Player current zone: ${Player.currentZoneId}`);
            }
            Game.socket.emit('request_chat_history', { chatType: chatType });
        } else {
            console.warn('ChatTabs: Cannot request chat history - Game socket not available');
        }
    },

    /**
     * Refresh zone chat when player changes zones
     * This should be called when the player enters a new zone
     */
    refreshZoneChat: function() {
        if (this.activeChatType === 'zone') {
            console.log('ChatTabs: Refreshing zone chat due to zone change');
            this.requestChatHistory('zone');
        }
    }
};

// The ChatTabs.init() is now called from UI.init() in js/ui.js
// No need for a separate DOMContentLoaded listener here.
