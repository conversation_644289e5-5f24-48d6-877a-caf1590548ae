/**
 * UI management for the DawnSword game
 * Handles the game's user interface
 */

// Forward declarations for Game and World if needed, assuming they exist globally or are imported
// declare var Game: any;
// declare var World: any;
// declare var Player: any;
// declare var Utils: any;
// declare var Inventory: any;

const UI = {
    // Store UI elements as properties
    commandInput: null,
    buildModeModal: null,
    buildEditorArea: null,
    buildZoneName: null,
    inputHasFocus: false, // Track input focus state
    chatOutputElement: null,
    chatInput: null, // Assign chat input element

    // Store current category filters
    currentRefineryCategory: 'all',
    currentLumbermillCategory: 'all',
    currentCraftingBenchCategory: 'all',

    // Build mode state
    isDragging: false,
    isResizing: false,
    activeElement: null,
    offsetX: 0,
    offsetY: 0,
    initialWidth: 0,
    initialHeight: 0,
    initialMouseX: 0,
    initialMouseY: 0,
    editorScaleX: 1,
    editorScaleY: 1,
    zoneMinX: 0,
    zoneMinY: 0,

    /**
     * Initialize the UI
     */
    init: function() {
        try {
            // Set up event listeners for the command input
            this.commandInput = document.getElementById('command-input');
            const submitButton = document.getElementById('submit-btn');

  this.chatOutputElement = document.getElementById('chat-output'); // Get the chat output div
        if (!this.chatOutputElement) {
            console.error("UI.init: CRITICAL - Chat output element ('chat-output') not found!");
        }

            if (this.commandInput && submitButton) {
                // Add focus/blur event listeners to track input focus state
                this.commandInput.addEventListener('focus', () => {
                    this.inputHasFocus = true;
                });

                this.commandInput.addEventListener('blur', () => {
                    // Delay checking focus to handle clicks outside the input
                    setTimeout(() => {
                        if (!this.inputHasFocus) { // Check if focus hasn't been regained
                           this.inputHasFocus = false;
                        }
                    }, 100); // Small delay
                });

                // Ensure focus state is reset when clicking outside (useful for keybindings)
                document.addEventListener('click', (event) => {
                    // Check against both command input AND chat input
                    if (event.target !== this.commandInput && event.target !== this.chatInput) {
                        if (this.inputHasFocus) {
                            this.inputHasFocus = false;
                        }
                    }
                });

                // Existing submit logic
                submitButton.addEventListener('click', () => {
                    this.submitCommand(); // Reverted from processCommand
                });

                this.commandInput.addEventListener('keypress', (event) => {
                    if (event.key === 'Enter') {
                        this.submitCommand(); // Reverted from processCommand
                    }
                });
            } else {
                console.error('Command input or submit button not found!');
            }

            // Add direct event listeners to status tab buttons for better responsiveness
            const playerTabBtn = document.getElementById('player-tab-btn');
            const mountTabBtn = document.getElementById('mount-tab-btn');

            if (playerTabBtn && mountTabBtn) {
                playerTabBtn.addEventListener('click', () => {
                    this.switchStatusTab('player');
                });

                mountTabBtn.addEventListener('click', () => {
                    this.switchStatusTab('mount');
                });
            }

            // Initialize build mode elements
            this.buildModeModal = document.getElementById('build-mode-modal');
            this.buildEditorArea = document.getElementById('build-editor-area');
            this.buildZoneName = document.getElementById('build-zone-name');

            // --- Attach build editor mouse down listener ---
            if (this.buildEditorArea) {
                this.boundHandleEditorMouseDown = this.handleEditorMouseDown.bind(this);
                this.boundHandleEditorMouseMove = this.handleEditorMouseMove.bind(this);
                this.boundHandleEditorMouseUp = this.handleEditorMouseUp.bind(this);
                this.buildEditorArea.addEventListener('mousedown', this.boundHandleEditorMouseDown);
            } else {
                console.error('Build editor area not found!');
            }
            // ----------------------------------------------------

            // --- Add event listener for the Build Mode Button ---
            const buildButton = document.getElementById('build-mode-btn');
            if (buildButton) {
                buildButton.addEventListener('click', () => {
                    // Get current zone using the Player object
                    const playerPos = (typeof Player !== 'undefined') ? Player.getCurrentZoneAndPosition() : null;

                    if (playerPos && playerPos.zone && playerPos.zone.id) {
                        this.openBuildMode(playerPos.zone.id); // Use the correct zone ID
                    } else {
                        console.error("Cannot open build mode: Current zone information not found from Player.", playerPos);
                        if (typeof Game !== 'undefined' && typeof Game.addMessage === 'function') {
                            Game.addMessage("Cannot determine current zone for build mode.", "error");
                        }
                    }
                });
            } else {
                console.error('Build mode button not found!');
            }
            // ----------------------------------------------------

            // --- Add event listeners for adding new structures (DELEGATED) ---
            const structureButtonContainer = document.getElementById('add-structure-buttons');
            if (structureButtonContainer) {
                structureButtonContainer.addEventListener('click', (event) => {
                    if (event.target.tagName === 'BUTTON' && event.target.dataset.structureType) {
                        const structureType = event.target.dataset.structureType;
                        this.addNewStructure(structureType);
                    }
                });
            } else {
                console.error('Structure button container (#add-structure-buttons) not found!');
            }
            // -------------------------------------------------------------------

            // --- Add event listener for the Save Layout Button ---
            const saveButton = document.getElementById('save-layout-btn');
            if (saveButton) {
                saveButton.addEventListener('click', () => {
                    console.log("Save layout button clicked."); // Add log here
                    this.saveLayout(); // Call the saveLayout function
                });
            } else {
                console.error('Save layout button (#save-layout-btn) not found!');
            }
            // -----------------------------------------------------

            // --- Add event listeners for modal close buttons ---
            const closeButtons = document.querySelectorAll('.close-modal');
            closeButtons.forEach(button => {
                button.addEventListener('click', (event) => {
                    const modal = event.target.closest('.modal'); // Find the nearest parent modal
                    if (modal) {
                        // Hide the modal ONLY by removing the 'visible' class
                        modal.classList.remove('visible');
                        // modal.style.display = 'none'; // <-- REMOVED this conflicting line
                        console.log(`Closed modal: ${modal.id}`);

                        // --- Clean up build mode state and listeners if it's the build modal ---
                        if (modal.id === 'build-mode-modal') {
                            this.resetBuildModeState();
                            window.removeEventListener('mousemove', this.boundHandleEditorMouseMove);
                            window.removeEventListener('mouseup', this.boundHandleEditorMouseUp);
                            console.log('Build mode state reset and listeners removed on close.');
                        }
                        // --------------------------------------------------------------------
                    } else {
                        console.error('Could not find parent modal for close button:', event.target);
                    }
                });
            });
            // ---------------------------------------------------

            // --- Add event listener for Inventory/Equipment/Skills Tabs (DELEGATED) ---
            const tabsContainer = document.querySelector('.tabs'); // Find the container for the tab buttons
            if (tabsContainer) {
                tabsContainer.addEventListener('click', (event) => {
                    // Check if the clicked element is a button with the 'tab-btn' class
                    if (event.target.tagName === 'BUTTON' && event.target.classList.contains('tab-btn')) {
                        const tabId = event.target.getAttribute('data-tab');
                        if (tabId) {
                            this.switchTab(tabId); // Call the existing switchTab function
                        }
                    }
                });
                console.log('Tab button listener attached to .tabs container');
            } else {
                console.error('Tabs container (.tabs) not found!');
            }

            // --- Add event listener for copy coordinates button ---
            const copyCoordinatesBtn = document.getElementById('copy-coords-btn');
            if (copyCoordinatesBtn) {
                copyCoordinatesBtn.addEventListener('click', () => {
                    this.copyCoordinates();
                });
            } else {
                console.error('Copy coordinates button not found!');
            }
            // ----------------------------------------------------

            // --- Add event listeners for player action buttons ---
            const guildBtn = document.getElementById('guild-btn');
            const leaderboardBtn = document.getElementById('leaderboard-btn');
            const friendsBtn = document.getElementById('friends-btn');
            const profileBtn = document.getElementById('profile-btn');

            if (guildBtn) {
                guildBtn.addEventListener('click', () => {
                    Game.addMessage('Guild feature coming soon!', 'info');
                });
            }

            if (leaderboardBtn) {
                leaderboardBtn.addEventListener('click', () => {
                    this.openLeaderboardPopup();
                });
            }

            if (friendsBtn) {
                friendsBtn.addEventListener('click', () => {
                    Game.addMessage('Friends feature coming soon!', 'info');
                });
            }

            if (profileBtn) {
                profileBtn.addEventListener('click', () => {
                    Game.addMessage('Profile feature coming soon!', 'info');
                });
            }

            // Add event listener for premium button
            const premiumBtn = document.getElementById('premium-btn');
            if (premiumBtn) {
                premiumBtn.addEventListener('click', () => {
                    this.openPremiumPopup();
                });
            }
            // -----------------------------------------------------------------------

            this.initChat(); // Initialize chat listeners

            console.log('UI Initialized');
        } catch (error) {
            console.error('Error initializing UI:', error);
            Game.addMessage('Failed to initialize UI components.', 'error');
        }
    },

filterAndDisplayChatMessages: function() {
    const activeChatType = (typeof ChatTabs !== 'undefined' && ChatTabs.activeChatType) ? ChatTabs.activeChatType : 'world'; // Default to 'world' if undefined
    console.log(`UI.filterAndDisplayChatMessages: Called for tab - ${activeChatType}.`);

    if (this.chatOutputElement) {
        this.chatOutputElement.innerHTML = ''; // Clear current messages
        console.log(`UI.filterAndDisplayChatMessages: Chat display for tab '${activeChatType}' cleared.`);

        // Assuming Game.chatMessages stores all chat messages
        // Each message object should have: { username, message, type, isAdmin, messageId, timestamp }
        if (typeof Game !== 'undefined' && Array.isArray(Game.chatMessages)) {
            console.log(`[DEBUG] UI filtering ${Game.chatMessages.length} messages for type '${activeChatType}'`);
            let displayedCount = 0;
            Game.chatMessages.forEach(msg => {
                console.log(`[DEBUG] Message type: '${msg.type}', active type: '${activeChatType}', match: ${msg.type === activeChatType}`);
                // Check if the message type matches the active tab
                // Or if the active tab is 'all' (though 'all' is not a typical chat type tab, good for future)
                if (msg.type === activeChatType || activeChatType === 'all') {
                    // Ensure all necessary parameters are passed to displayChatMessage
                    // The 'type' of the message (msg.type) is used for filtering here,
                    // and displayChatMessage itself doesn't need it as a direct parameter
                    // based on its current signature.
                    this.displayChatMessage(
                        msg.username,
                        msg.message,
                        msg.isAdmin,
                        msg.messageId,
                        msg.timestamp
                        // msg.type // Not currently a param for displayChatMessage, but msg object has it
                    );
                    displayedCount++;
                }
            });
            console.log(`UI.filterAndDisplayChatMessages: Displayed ${displayedCount} messages for tab '${activeChatType}'.`);
            this.scrollOutputToBottom(); // Scroll to bottom after repopulating
        } else {
            console.warn('UI.filterAndDisplayChatMessages: Game.chatMessages not found or not an array. Cannot filter messages.');
        }
    } else {
        console.error("UI.filterAndDisplayChatMessages: this.chatOutputElement is not available. Cannot clear/repopulate chat.");
    }
},

    /**
     * Adds a single message to the chat display IF it matches the current active tab.
     * @param {object} messageData - The message object from the server.
     *        Expected to have at least: { message: "text", username: "name", type: "world" }
     *        And optionally: { isAdmin, messageId, timestamp, zoneId }
     */
    /**
     * Submit a command
     */
    submitCommand: function() {
        // Use the stored commandInput property
        const command = this.commandInput.value.trim();

        if (command) {
            // If DialogueEngine is active, route input to it
            if (window.DialogueEngine && DialogueEngine.active) {
                DialogueEngine.handleInput(command);
            } else {
                Game.processCommand(command);
            }
            this.commandInput.value = '';
        }

        this.focusCommandInput();
    },

    /**
     * Focus the command input
     */
    focusCommandInput: function() {
        // Use the stored commandInput property
        if (this.commandInput) {
            this.commandInput.focus();
            // Update the focus state explicitly
            this.inputHasFocus = true;
        } else {
            console.error("Command input element not found!");
        }
    },

    /**
     * Update the location display
     * @param {Object} zone - The zone object to display (contains name, description)
     */
    updateLocationDisplay: function(zone) {
        if (!zone) return;

        // Find the existing location display or create it if it doesn't exist
        let locationDiv = document.getElementById('location-display');
        if (!locationDiv) {
            locationDiv = document.createElement('div');
            locationDiv.id = 'location-display';
            // Prepend it to the main content area, or wherever appropriate
            const mainContent = document.querySelector('.main-content'); // Adjust selector if needed
            if (mainContent) {
                 mainContent.insertBefore(locationDiv, mainContent.firstChild);
            } else {
                document.body.insertBefore(locationDiv, document.body.firstChild); // Fallback
            }
        }
        // Revert: Append location info to the main output log instead of a static div.
        const output = document.getElementById('output');
        if (output) {
             const locationHtml = `
                <div class="location-description message message-location">
                    <h3>${zone.name}</h3>
                    <p>${zone.description}</p>
                </div>
            `;
            // Append only if it's different from the last message to avoid duplicates on look
            const lastMessage = output.lastElementChild;
            if (!lastMessage || !lastMessage.classList.contains('message-location') || lastMessage.querySelector('h3')?.textContent !== zone.name) {
                 output.innerHTML += locationHtml; // Append to output log
                 this.scrollOutputToBottom(); // Scroll after adding
            }
        }
    },

    /**
     * Hide the combat UI elements when combat ends
     */
    hideCombatUI: function() {
        // Remove the combat UI box if it exists
        const combatBox = document.getElementById('combat-ui-box');
        if (combatBox) {
            // Immediately remove the combat box to prevent it from interfering with lookAround
            if (combatBox.parentNode) {
                combatBox.remove();
            }
            console.log("Combat UI box removed immediately");

            // Force a scroll update to ensure proper layout
            this.scrollMainOutputToBottom();
        }
    },
    // No need for formatExits function

    /**
     * Scroll to the bottom of the chat output log
     */
    scrollOutputToBottom: function() {
        const chatOutput = document.getElementById('chat-output'); // Target the chat output element
        // Ensure chatOutput exists before trying to scroll
        if (chatOutput) {
            // Use setTimeout to ensure scrolling happens after potential DOM updates
            setTimeout(() => { chatOutput.scrollTop = chatOutput.scrollHeight; }, 0);
        }
    },

    /**
     * Scroll the main game output log to the bottom
     */
    scrollMainOutputToBottom: function() {
        const output = document.getElementById('output'); // Target the main output element
        if (output) {
            setTimeout(() => { output.scrollTop = output.scrollHeight; }, 0);
        }
    },

    /**
     * Show the name change modal (Function kept but might be unused now)
     */
    // showNameChangeModal: function() {
    //     const nameChangeModal = document.getElementById('name-change-modal');
    //     const newNameInput = document.getElementById('new-name-input');
    //     if (!nameChangeModal || !newNameInput) return; // Add checks

    //     // Set the input value to the current name
    //     newNameInput.value = Player.name;

    //     // Show the modal
    //     nameChangeModal.classList.add('visible');

    //     // Focus the input
    //     newNameInput.focus();
    //     newNameInput.select();
    // },

    /**
     * Save the new name (Function kept but might be unused now)
     */
    // saveNewName: function() {
    //     const newNameInput = document.getElementById('new-name-input');
    //     if (!newNameInput) return; // Add check
    //     const newName = newNameInput.value.trim();

    //     if (newName) {
    //         Player.changeName(newName);
    //         Game.addMessage(`Your name has been changed to ${newName}.`, 'success');
    //     }

    //     this.closeModals();
    //     this.focusCommandInput();
    // },

    /**
     * Close all modals
     */
    closeModals: function() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
        // Reset build mode state if closing build modal
        if (this.buildModeModal && this.buildModeModal.style.display === 'none') {
             this.resetBuildModeState();
        }
        this.focusCommandInput();
    },

    /**
     * Copy player coordinates to clipboard
     */
    copyCoordinates: function() {
        const coordsElem = document.getElementById('coordinates-display');
        if (!coordsElem) return;

        const coordsText = coordsElem.textContent;

        // Use the Clipboard API to copy the text
        navigator.clipboard.writeText(coordsText)
            .then(() => {
                // Show a brief confirmation message
                const originalText = coordsElem.textContent;
                coordsElem.textContent = "Copied!";
                coordsElem.style.color = "#4CAF50"; // Green color

                // Reset after a short delay
                setTimeout(() => {
                    coordsElem.textContent = originalText;
                    coordsElem.style.color = "#ccc"; // Reset to original color
                }, 1000);

                // Also add a game message
                if (typeof Game !== 'undefined' && typeof Game.addMessage === 'function') {
                    Game.addMessage(`Coordinates ${coordsText} copied to clipboard.`, 'info');
                }
            })
            .catch(err => {
                console.error('Failed to copy coordinates: ', err);
                if (typeof Game !== 'undefined' && typeof Game.addMessage === 'function') {
                    Game.addMessage('Failed to copy coordinates to clipboard.', 'error');
                }
            });
    },

    /**
     * Update the gold display
     */
    updateGoldDisplay: function() {
        const goldText = document.getElementById('gold-value');
        if (goldText) {
            goldText.textContent = Player.gold;
        }
    },

    /**
     * Update the player stats display (Health, Hunger, XP)
     */
    updatePlayerStats: function() {
        try {
            const healthBar = document.getElementById('health-bar');
            const healthText = document.getElementById('health-value');
            const hungerBar = document.getElementById('hunger-bar');
            const hungerText = document.getElementById('hunger-value');
            const staminaBar = document.getElementById('stamina-bar');
            const staminaText = document.getElementById('stamina-value');
            const xpBar = document.getElementById('xp-bar-fill');
            const levelText = document.getElementById('level-text');
            const goldText = document.getElementById('gold-value');

            if (healthBar && healthText) {
                const healthPercent = (Player.health / Player.maxHealth) * 100;
                healthBar.style.width = `${healthPercent}%`;
                healthText.textContent = `${Player.health} / ${Player.maxHealth}`;
            }
            if (hungerBar && hungerText) {
                const hungerPercent = (Player.hunger / Player.maxHunger) * 100;
                hungerBar.style.width = `${hungerPercent}%`;
                hungerText.textContent = `${Math.round(Player.hunger)} / ${Player.maxHunger}`;
            }
            if (staminaBar && staminaText && typeof Player.stamina !== "undefined" && typeof Player.maxStamina !== "undefined") {
                const staminaPercent = (Player.stamina / Player.maxStamina) * 100;
                staminaBar.style.width = `${staminaPercent}%`;
                staminaText.textContent = `${Math.round(Player.stamina)} / ${Player.maxStamina}`;
            }
            if (xpBar) {
                // Calculate XP percentage and ensure it's between 0 and 100
                const xpPercent = Math.min(100, Math.max(0, (Player.xp / Player.xpNeeded) * 100));
                xpBar.style.width = `${xpPercent}%`;

                // Add XP text to the level display
                if (levelText) {
                    levelText.textContent = `Level: ${Player.level} (${Player.xp}/${Player.xpNeeded} XP)`;
                }

                // Log XP update for debugging
                console.log(`XP Bar updated: ${Player.xp}/${Player.xpNeeded} = ${xpPercent.toFixed(1)}%`);
            }
            // Level text is now updated in the XP bar section
            if (goldText) {
                goldText.textContent = Player.gold;
            }

            // Gold is updated in the updateGoldDisplay function

            // Update mount tab
            this.updateMountTab();

            // Check which tab is currently active
            const playerTabBtn = document.getElementById('player-tab-btn');
            const mountTabBtn = document.getElementById('mount-tab-btn');

            // Only force the player tab to be active if no tab is currently active
            if (playerTabBtn && mountTabBtn) {
                if (!playerTabBtn.classList.contains('active') && !mountTabBtn.classList.contains('active')) {
                    // If no tab is active, make the player tab active
                    this.switchStatusTab('player');
                } else if (playerTabBtn.classList.contains('active')) {
                    // If player tab is active, ensure it's properly displayed
                    this.switchStatusTab('player');
                } else if (mountTabBtn.classList.contains('active')) {
                    // If mount tab is active, ensure it's properly displayed
                    this.switchStatusTab('mount');
                }
            }

        } catch (error) {
            console.error("Error updating player stats UI:", error);
        }
    },

    /**
     * Update the mount tab with horse information
     */
    updateMountTab: function() {
        const mountTab = document.getElementById('mount-status-tab');
        if (!mountTab) return;

        // Make sure we're showing the correct tab content
        // This ensures the mount tab is properly displayed when it's active
        const mountTabBtn = document.getElementById('mount-tab-btn');
        if (mountTabBtn && mountTabBtn.classList.contains('active')) {
            this.switchStatusTab('mount');
        }

        // Check if player has a horse that's not stabled
        if (Player.horse) {
            const horse = Player.horse;
            const isMounted = Player.mountedHorse !== null;

            // Ensure horse stats exist or use defaults
            const health = horse.health !== undefined ? horse.health : 100;
            const maxHealth = horse.max_health !== undefined ? horse.max_health : 100;
            const hunger = horse.hunger !== undefined ? horse.hunger : 100;
            const maxHunger = horse.max_hunger !== undefined ? horse.max_hunger : 100;
            const staminaMultiplier = horse.stamina_multiplier !== undefined ? horse.stamina_multiplier : 2.0;

            // Calculate health and hunger percentages
            const healthPercent = (health / maxHealth) * 100;
            const hungerPercent = (hunger / maxHunger) * 100;

            // Create mount tab content
            let content = `
                <div class="mount-info">
                    <div class="mount-header">
                        <h3 id="horse-name-display" class="clickable-name">${horse.name}</h3>
                        <div id="rename-container" class="rename-container" style="display: none;">
                            <input type="text" id="horse-name-input" placeholder="New name..." maxlength="30">
                            <button id="rename-horse-btn" class="action-btn small">Save</button>
                            <button id="cancel-rename-btn" class="action-btn small">Cancel</button>
                        </div>
                    </div>

                    <!-- Horse Stats Bars -->
                    <div class="status-bar-container">
                        <div class="status-bar-label">
                            <span>Health</span>
                            <span id="horse-health-value">${Math.round(health)}/${maxHealth}</span>
                        </div>
                        <div class="status-bar-outer">
                            <div id="horse-health-bar" class="status-bar-inner" style="width: ${healthPercent}%; background-color: #a82720;"></div>
                        </div>
                    </div>

                    <div class="status-bar-container">
                        <div class="status-bar-label">
                            <span>Hunger</span>
                            <span id="horse-hunger-value">${Math.round(hunger)}/${maxHunger}</span>
                        </div>
                        <div class="status-bar-outer">
                            <div id="horse-hunger-bar" class="status-bar-inner" style="width: ${hungerPercent}%; background-color: #bfa22e;"></div>
                        </div>
                    </div>



                    <div class="horse-info-grid">
                        <div class="info-item">
                            <span class="info-label">Color</span>
                            <span class="info-value">${horse.color}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Speed</span>
                            <span class="info-value">${horse.speed}x</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Stamina Boost</span>
                            <span class="info-value">${staminaMultiplier}x</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Status</span>
                            <span class="info-value">${isMounted ? 'Mounted' : 'Not Mounted'}</span>
                        </div>
                    </div>

                    <button id="pat-horse-btn" class="action-btn centered-btn">Pat Horse</button>
                </div>
            `;

            mountTab.innerHTML = content;

            // Add event listeners
            const nameDisplay = document.getElementById('horse-name-display');
            const renameContainer = document.getElementById('rename-container');
            const renameBtn = document.getElementById('rename-horse-btn');
            const cancelRenameBtn = document.getElementById('cancel-rename-btn');
            const nameInput = document.getElementById('horse-name-input');
            const patBtn = document.getElementById('pat-horse-btn');

            // Add click event to the horse name to show rename input
            if (nameDisplay && renameContainer) {
                nameDisplay.addEventListener('click', () => {
                    // Hide the name display and show the rename container
                    nameDisplay.style.display = 'none';
                    renameContainer.style.display = 'flex';

                    // Set the input value to the current name and focus it
                    if (nameInput) {
                        nameInput.value = horse.name;
                        nameInput.focus();
                    }
                });
            }

            // Add event listener for the cancel rename button
            if (cancelRenameBtn && nameDisplay && renameContainer) {
                cancelRenameBtn.addEventListener('click', () => {
                    // Hide the rename container and show the name display
                    renameContainer.style.display = 'none';
                    nameDisplay.style.display = 'block';
                });
            }

            // Add event listener for the pat horse button
            if (patBtn) {
                patBtn.addEventListener('click', () => {
                    if (typeof HorseSystem !== 'undefined') {
                        HorseSystem.patHorse();
                    }
                });
            }

            if (renameBtn && nameInput) {
                // Prevent movement keys when typing in the name input
                nameInput.addEventListener('focus', () => {
                    // Set a flag to indicate input has focus
                    this.inputHasFocus = true;

                    // Also set a specific flag for the horse name input
                    this.horseNameInputHasFocus = true;
                });

                nameInput.addEventListener('blur', () => {
                    // Clear the focus flags
                    this.inputHasFocus = false;
                    this.horseNameInputHasFocus = false;
                });

                renameBtn.addEventListener('click', () => {
                    const newName = nameInput.value.trim();
                    if (newName && typeof HorseSystem !== 'undefined') {
                        HorseSystem.renameHorse(newName);

                        // Hide the rename container and show the name display
                        if (renameContainer && nameDisplay) {
                            renameContainer.style.display = 'none';
                            nameDisplay.style.display = 'block';
                            // Update the displayed name immediately for better UX
                            nameDisplay.textContent = newName;
                        }
                    }
                });

                // Also allow pressing Enter in the input field
                nameInput.addEventListener('keypress', (event) => {
                    if (event.key === 'Enter') {
                        const newName = nameInput.value.trim();
                        if (newName && typeof HorseSystem !== 'undefined') {
                            HorseSystem.renameHorse(newName);

                            // Hide the rename container and show the name display
                            if (renameContainer && nameDisplay) {
                                renameContainer.style.display = 'none';
                                nameDisplay.style.display = 'block';
                                // Update the displayed name immediately for better UX
                                nameDisplay.textContent = newName;
                            }
                        }
                    }
                });
            }
        } else {
            // No horse available
            mountTab.innerHTML = `
                <div class="status-message">
                    <p>No mount available</p>
                    <p>Visit a stable to purchase a horse.</p>
                </div>
            `;
        }
    },

    /**
     * Switch between Inventory and Equipment tabs
     * @param {string} tabId - The ID of the tab to switch to ('inventory' or 'equipment')
     */
    switchTab: function(tabId) {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.classList.toggle('active', button.dataset.tab === tabId);
        });

        tabContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabId}-tab-content`);
        });

        // Refresh content when switching
        if (tabId === 'inventory') {
            Inventory.updateDisplay();
        } else if (tabId === 'equipment') {
            this.updateEquipment();
        } else if (tabId === 'quests') {
            this.updateQuestsPanel(); // Call the quests update function
        }
    },

    /**
     * Switch between Player and Mount status tabs
     * @param {string} tabId - The ID of the tab to switch to ('player' or 'mount')
     */
    switchStatusTab: function(tabId) {
        // Direct references to the elements we need - using getElementById for better performance
        const playerBtn = document.getElementById('player-tab-btn');
        const mountBtn = document.getElementById('mount-tab-btn');
        const playerContent = document.getElementById('player-status-tab');
        const mountContent = document.getElementById('mount-status-tab');

        if (!playerBtn || !mountBtn || !playerContent || !mountContent) {
            return;
        }

        // First, hide all tab contents and remove active class from all buttons
        playerContent.classList.remove('active');
        mountContent.classList.remove('active');
        playerBtn.classList.remove('active');
        mountBtn.classList.remove('active');

        // Then, show the selected tab content and add active class to the selected button
        if (tabId === 'player') {
            playerBtn.classList.add('active');
            playerContent.classList.add('active');

            // Ensure the mount content is fully hidden
            mountContent.style.display = 'none';
            // Show the player content
            playerContent.style.display = 'flex';

            // After a short delay, reset the display property to let CSS handle it
            setTimeout(() => {
                mountContent.style.display = '';
            }, 50);
        }
        else if (tabId === 'mount') {
            mountBtn.classList.add('active');
            mountContent.classList.add('active');

            // Ensure the player content is fully hidden
            playerContent.style.display = 'none';
            // Show the mount content
            mountContent.style.display = 'flex';

            // After a short delay, reset the display property to let CSS handle it
            setTimeout(() => {
                playerContent.style.display = '';
            }, 50);
        }
    },

    /**
     * Update the equipment display in the Equipment tab
     */
    updateEquipment: function() {
        const equipmentSlotsContainer = document.getElementById('equipment-slots');
        if (!equipmentSlotsContainer) return;

        // Clear existing slots (or update them)
        equipmentSlotsContainer.innerHTML = ''; // Simple clear for now

        // Add player stats at the top of equipment tab
        const statsDiv = document.createElement('div');
        statsDiv.className = 'equipment-stats';
        statsDiv.innerHTML = `
            <div class="equipment-stat-row">
                <span class="equipment-stat-label">Attack:</span>
                <span class="equipment-stat-value">${Player.attack}</span>
            </div>
            <div class="equipment-stat-row">
                <span class="equipment-stat-label">Defense:</span>
                <span class="equipment-stat-value">${Player.defense}</span>
            </div>
        `;
        equipmentSlotsContainer.appendChild(statsDiv);

        // Define slots to display
        const slots = [
            'head',
            'shirt',
            'chest_armor',
            'legs',
            'leg_armor',
            'feet',
            'weapon',
            'shield',
            'arrows',
            'cloak',
            'pickaxe',
            'axe',
            'skinning_knife'
        ];

        slots.forEach(slot => {
            const equippedItem = Player.equipment[slot];
            const slotDiv = document.createElement('div');
            slotDiv.className = 'equipment-slot';
            slotDiv.dataset.slot = slot;

            const labelSpan = document.createElement('span');
            labelSpan.className = 'slot-label';

            // Format the slot label for better display
            let slotLabel = slot;
            if (slot === 'skinning_knife') {
                slotLabel = 'Skinning';
            }
            labelSpan.textContent = `${Utils.capitalize(slotLabel)}:`;

            const itemSpan = document.createElement('span');
            itemSpan.className = 'slot-item-name';
            itemSpan.style.color = '#f0f0f0'; // Reset color explicitly
            slotDiv.style.borderColor = '#333'; // Reset border color
            slotDiv.style.borderWidth = '1px'; // Reset border width

            const dequipBtn = document.createElement('button');
            dequipBtn.className = 'dequip-btn';
            dequipBtn.dataset.slot = slot;
            dequipBtn.textContent = 'Dequip';

            if (equippedItem) {
                itemSpan.textContent = equippedItem.name;
                dequipBtn.style.display = 'inline-block'; // Show button

                // Apply level styling if the item has a level
                if (typeof equippedItem.level !== 'undefined') {
                    const levelColor = Utils.getItemLevelColor(equippedItem.level);
                    slotDiv.style.borderColor = levelColor; // Set border color
                    slotDiv.style.borderWidth = '2px'; // Make border more visible

                    // Add level bubble
                    const levelBubble = document.createElement('span');
                    levelBubble.className = 'item-level-bubble';
                    levelBubble.textContent = `Lvl ${equippedItem.level}`;
                    itemSpan.appendChild(levelBubble); // Append bubble after name

                    // Update tooltip for the slot div
                    slotDiv.title = `${equippedItem.description} (Level ${equippedItem.level})`;
                } else {
                    slotDiv.title = equippedItem.description || ''; // Default tooltip
                }

                // Add event listener for dequip button
                dequipBtn.addEventListener('click', () => {
                    Player.unequipItem(slot); // Call Player's unequip function
                });
            } else {
                itemSpan.textContent = 'None';
                dequipBtn.style.display = 'none'; // Hide button
                slotDiv.title = ''; // Clear tooltip
            }

            slotDiv.appendChild(labelSpan);
            slotDiv.appendChild(itemSpan);
            slotDiv.appendChild(dequipBtn);
            equipmentSlotsContainer.appendChild(slotDiv);
        });
    },

    // --- Build Mode Functions ---

    /**
     * Opens the Build Mode modal and populates it with structures.
     */
    openBuildMode: function(zoneId) {
        if (!zoneId) {
            console.error('Build mode requires a zone ID.');
            Game.addMessage('Cannot open build mode: Zone ID missing.', 'error');
            return;
        }

        const zone = World.getZone(zoneId); // Fetch the full zone object using the correct function
        if (!zone) {
            console.error(`Build mode failed: Zone with ID ${zoneId} not found.`);
            Game.addMessage('Failed to load zone data for build mode.', 'error');
            return;
        }

        if (!this.buildModeModal || !this.buildEditorArea || !this.buildZoneName) {
            console.error('Build mode UI elements not found!');
            Game.addMessage('Build mode UI is not properly initialized.', 'error');
            return;
        }

        console.log(`Opening build mode for zone: ${zone.name || zoneId}`);
        this.buildZoneName.textContent = zone.name || zoneId; // Update zone name display
        this.buildEditorArea.innerHTML = ''; // Clear previous content

        // --- Populate Structure Buttons ---
        const buttonContainer = document.getElementById('add-structure-buttons');
        if (buttonContainer) {
            buttonContainer.innerHTML = ''; // Clear previous buttons
            // Assuming World.buildingTypes holds the structure definitions directly
            // Format: { typeId: { name: '...', buildable: true/false, ... } }
            const structureTypes = (typeof World !== 'undefined' && World.buildingTypes)
                                     ? World.buildingTypes // Use the buildingTypes object directly
                                     : {};

            console.log('Available Structure Types:', structureTypes); // Log fetched types

            let hasBuildableStructures = false;
            for (const typeId in structureTypes) {
                // Ensure it's a direct property
                if (structureTypes.hasOwnProperty(typeId)) {
                    hasBuildableStructures = true;
                    const typeInfo = structureTypes[typeId];
                    const button = document.createElement('button');
                    // Use a more descriptive name if available, otherwise use the ID capitalized
                    button.textContent = `Add ${typeInfo.name || Utils.capitalizeFirstLetter(typeId.replace('_', ' '))}`;
                    button.dataset.structureType = typeId; // Store type ID in data attribute
                    button.classList.add('build-add-btn'); // Add a class for potential styling
                    buttonContainer.appendChild(button);
                    console.log(`Added button for: ${typeId}`); // Log button addition
                }
            }

            if (!hasBuildableStructures) {
                buttonContainer.innerHTML = '<p>No buildable structures available.</p>';
                console.log('No buildable structures found to add buttons for.');
            }

        } else {
             console.error('Add structure buttons container (#add-structure-buttons) not found in build modal.');
        }
        // --------------------------------

        // Show the modal *first* to allow rendering
        this.buildModeModal.style.display = 'flex';

        // Use requestAnimationFrame to wait for the browser to paint the modal
        // before calculating dimensions in initializeBuildEditor.
        requestAnimationFrame(() => {
            // Double requestAnimationFrame can sometimes be safer for complex layouts
             requestAnimationFrame(() => {
                 this.initializeBuildEditor(zone); // Initialize the editor with existing structures
                 console.log('Build mode opened and editor initialized.');
             });
        });
    },

    /**
     * Initialize the build editor with structures from the zone
     * @param {Object} zone - The zone object
     */
    initializeBuildEditor: function(zone) {
        // Calculate scaling factors based on editor size and zone boundaries
        const editorWidth = this.buildEditorArea.offsetWidth;
        const editorHeight = this.buildEditorArea.offsetHeight;
        console.log("Editor dimensions:", editorWidth, editorHeight);

        console.log("Zone boundaries:", zone.boundaries);
        const zoneWidth = zone.boundaries.maxX - zone.boundaries.minX;
        const zoneHeight = zone.boundaries.maxY - zone.boundaries.minY; // Y increases downwards in editor
        console.log("Zone dimensions:", zoneWidth, zoneHeight);

        if (zoneWidth <= 0 || zoneHeight <= 0 || editorWidth <= 0 || editorHeight <= 0) {
             console.error("Invalid dimensions for scaling calculation.");
             console.error("Zone width:", zoneWidth, "Zone height:", zoneHeight);
             console.error("Editor width:", editorWidth, "Editor height:", editorHeight);
             if (typeof Game !== 'undefined' && typeof Game.addMessage === 'function') {
                Game.addMessage("Error calculating editor scale.", "error");
             }
             return;
        }

        this.editorScaleX = editorWidth / zoneWidth;
        // Invert Y scale because game Y increases going up, editor Y increases going down
        this.editorScaleY = editorHeight / zoneHeight;
        this.zoneMinX = zone.boundaries.minX;
        // Adjust minY for coordinate inversion
        this.zoneMinY = zone.boundaries.maxY; // Use maxY as the top edge in editor coordinates

        // Populate editor with structures
        zone.objects.structures.forEach(structure => {
            const structElement = document.createElement('div');
            structElement.className = 'build-structure';
            structElement.dataset.id = structure.id;
            structElement.dataset.typeId = structure.typeId;

            // Calculate position and size in editor pixels
            // Center coordinates (x, y) need conversion to top-left
            const gameX = structure.x - structure.width / 2;
            const gameY = structure.y + structure.height / 2; // Top edge in game coords

            const editorX = (gameX - this.zoneMinX) * this.editorScaleX;
            // Invert Y calculation: (TopGameY - MaxZoneY) * ScaleY -> results in negative, use absolute or adjust logic
            // Alternative: Calculate bottom edge in game coords and convert
            // const gameBottomY = structure.y - structure.height / 2;
            // const editorY = (this.zoneMinY - gameY) * this.editorScaleY; // zoneMinY is maxY here
             const editorY = (this.zoneMinY - gameY) * this.editorScaleY;


            const editorWidth = structure.width * this.editorScaleX;
            const editorHeight = structure.height * this.editorScaleY;

            structElement.style.left = `${editorX}px`;
            structElement.style.top = `${editorY}px`;
            structElement.style.width = `${editorWidth}px`;
            structElement.style.height = `${editorHeight}px`;

            structElement.textContent = `${structure.name || structure.typeId} (${structure.id.replace('avelia_', '')})`; // Display name/type and short ID

            // Add resize handle
            const resizeHandle = document.createElement('div');
            resizeHandle.className = 'resize-handle';
            structElement.appendChild(resizeHandle);

            // Add delete button
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-structure-btn';
            deleteBtn.innerHTML = '&times;'; // 'X' symbol
            deleteBtn.title = 'Delete Structure';
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent triggering drag/resize
                if (confirm(`Are you sure you want to delete ${structElement.textContent}?`)) {
                    structElement.remove();
                    console.log(`Removed structure element ${structElement.dataset.id} from editor.`);
                }
            });
            structElement.appendChild(deleteBtn);


            this.buildEditorArea.appendChild(structElement);
        });

    },

    /**
     * Saves the current layout from the build editor.
     * (Placeholder - needs implementation to update World data and save)
     */
    saveLayout: function() {
        if (!this.buildEditorArea) return;

        const updatedStructures = [];
        const structureElements = this.buildEditorArea.querySelectorAll('.build-structure');

        structureElements.forEach(element => {
            const id = element.dataset.id;
            if (!id) {
                console.warn("Found build element without data-id:", element);
                return; // Skip elements without an ID
            }

            // Use offsetLeft/Top for position relative to the editor area
            const editorX = element.offsetLeft;
            const editorY = element.offsetTop;
            const editorWidth = element.offsetWidth;
            const editorHeight = element.offsetHeight;

            // Ensure scaling factors are valid
            if (!this.editorScaleX || !this.editorScaleY || this.editorScaleX === 0 || this.editorScaleY === 0) {
                console.error("Cannot save layout: Editor scaling factors are invalid or zero.", this.editorScaleX, this.editorScaleY);
                Game.addMessage("Error saving: Invalid editor scale.", "error");
                // Set a flag or return early to prevent saving incomplete data
                // For simplicity, we might just log and continue, but ideally handle this better.
                return; // Stop processing this element, potentially leaving updatedStructures incomplete
            }

            // Convert back to game coordinates
            const gameWidth = editorWidth / this.editorScaleX;
            const gameHeight = editorHeight / this.editorScaleY;
            const gameX_topLeft = (editorX / this.editorScaleX) + this.zoneMinX;
            const gameY_topLeft = this.zoneMinY - (editorY / this.editorScaleY);
            const gameX_center = gameX_topLeft + gameWidth / 2;
            const gameY_center = gameY_topLeft - gameHeight / 2;
            const typeId = element.dataset.typeId || 'unknown';

            updatedStructures.push({
                id: id,
                typeId: typeId,
                x: Math.round(gameX_center),
                y: Math.round(gameY_center),
                width: Math.round(gameWidth),
                height: Math.round(gameHeight)
            });
        });

        // Check if any elements failed conversion - if so, maybe don't save?
        // For now, proceed even if some failed (logged above)
        console.log("Updated Structures Data (to be sent via API):", updatedStructures);

        // 1. Get the current zone ID
        const currentZoneId = Player.currentZoneId; // Assuming Player object has this
        if (!currentZoneId) {
            console.error("Cannot save layout: Current zone ID not found.");
            Game.addMessage("Error saving layout: Could not determine current zone.", "error");
            return;
        }

        console.log("Saving layout for zone:", currentZoneId);

         // 2. Call World.updateZoneStructures to update in-memory representation
         const changesMade = World.updateZoneStructures(currentZoneId, updatedStructures);

         if (changesMade) {
             // 3. Save ONLY the updated structures for the current zone via a new API endpoint
             console.log(`Sending updated structures for zone ${currentZoneId} to server...`);
             fetch('/api/update_zone_structures', { // NEW API endpoint
                 method: 'POST',
                 headers: { 'Content-Type': 'application/json' },
                 // Send only the zone ID and the updated structures array
                 body: JSON.stringify({
                     zoneId: currentZoneId,
                     structures: updatedStructures
                 })
             })
             .then(response => {
                 if (!response.ok) {
                    // Handle HTTP errors (e.g., 404, 500)
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json(); // Parse JSON only if response is OK
             })
             .then(data => {
                 if (data.success) {
                     Game.addMessage("Layout saved successfully.", "success");
                     if (typeof Minimap !== 'undefined' && Minimap.update) {
                        Minimap.update(); // Refresh minimap if available
                     }
                     // Optionally call Game.saveGame() here if player state also needs saving
                 } else {
                     console.error("Error saving zone definitions via API:", data.error);
                     Game.addMessage(`Error saving layout: ${data.error || 'Unknown server error'}`, "error");
                 }
             })
             .catch(error => {
                 console.error("Network or processing error saving zone definitions:", error);
                 Game.addMessage("Network error saving layout. Is the server running correctly?", "error");
             });

         } else {
             Game.addMessage("No changes detected in layout.", "system");
         }

         // Close the build modal after attempting to save
         const buildModal = document.getElementById('build-mode-modal');
         if (buildModal) {
            buildModal.style.display = 'none';
         }
         this.resetBuildModeState(); // Ensure state is reset
    },

     /**
     * Resets the state variables used for dragging and resizing.
     */
     resetBuildModeState: function() {
         this.isDragging = false;
         this.isResizing = false;
         this.activeElement = null;
         this.offsetX = 0;
         this.offsetY = 0;
         this.initialWidth = 0;
         this.initialHeight = 0;
         this.initialMouseX = 0;
         this.initialMouseY = 0;
         if (this.buildEditorArea) {
             this.buildEditorArea.style.cursor = 'default';
         }
         console.log("Build mode state reset");
     },

    /**
     * Handles the mousedown event within the build editor area.
     * Initiates dragging or resizing.
     */
    handleEditorMouseDown: function(event) {
        this.resetBuildModeState(); // Reset just in case

        const target = event.target;
        const structureElement = target.closest('.build-structure');

        if (!structureElement) return; // Clicked on empty area

        this.activeElement = structureElement;
        this.initialMouseX = event.clientX;
        this.initialMouseY = event.clientY;

        if (target.classList.contains('resize-handle')) {
            // --- Start Resizing ---
            this.isResizing = true;
            this.initialWidth = this.activeElement.offsetWidth;
            this.initialHeight = this.activeElement.offsetHeight;
            console.log('Start resizing:', this.activeElement.dataset.id);

        } else {
            // --- Start Dragging ---
            this.isDragging = true;
            // Calculate offset from the element's top-left corner
            const rect = this.activeElement.getBoundingClientRect();
            this.offsetX = event.clientX - rect.left;
            this.offsetY = event.clientY - rect.top;
            console.log('Start dragging:', this.activeElement.dataset.id);
        }

        // Prevent default drag behavior (like text selection)
        event.preventDefault();

        // --- Attach mousemove and mouseup listeners to window ---
        window.addEventListener('mousemove', this.boundHandleEditorMouseMove);
        window.addEventListener('mouseup', this.boundHandleEditorMouseUp);
        // -------------------------------------------------------
    },

    /**
     * Handles the mousemove event (attached to window).
     * Performs dragging or resizing if active.
     */
    handleEditorMouseMove: function(event) {
        if (!this.activeElement) return;

        if (this.isDragging) {
            // --- Dragging Logic ---
            const editorRect = this.buildEditorArea.getBoundingClientRect();

            // Calculate new position relative to the editor area's top-left corner
            let newX = event.clientX - editorRect.left - this.offsetX;
            let newY = event.clientY - editorRect.top - this.offsetY;

            // Constrain within editor bounds (optional, but good practice)
            newX = Math.max(0, Math.min(newX, editorRect.width - this.activeElement.offsetWidth));
            newY = Math.max(0, Math.min(newY, editorRect.height - this.activeElement.offsetHeight));


            this.activeElement.style.left = `${newX}px`;
            this.activeElement.style.top = `${newY}px`;

        } else if (this.isResizing) {
            // --- Resizing Logic ---
            const dx = event.clientX - this.initialMouseX;
            const dy = event.clientY - this.initialMouseY;

            const newWidth = Math.max(10, this.initialWidth + dx); // Minimum width 10px
            const newHeight = Math.max(10, this.initialHeight + dy); // Minimum height 10px

             // Constrain resize within bounds if needed
             const editorRect = this.buildEditorArea.getBoundingClientRect();
             const currentLeft = this.activeElement.offsetLeft;
             const currentTop = this.activeElement.offsetTop;

             const maxWidth = editorRect.width - currentLeft;
             const maxHeight = editorRect.height - currentTop;


            this.activeElement.style.width = `${Math.min(newWidth, maxWidth)}px`;
            this.activeElement.style.height = `${Math.min(newHeight, maxHeight)}px`;
        }
    },

    /**
     * Handles the mouseup event (attached to window).
     * Stops dragging or resizing.
     */
    handleEditorMouseUp: function(event) {
        if (this.isDragging) {
            console.log('Stop dragging:', this.activeElement ? this.activeElement.dataset.id : 'unknown');
        } else if (this.isResizing) {
            console.log('Stop resizing:', this.activeElement ? this.activeElement.dataset.id : 'unknown');
        }

        // --- Remove window listeners and reset state ---
        window.removeEventListener('mousemove', this.boundHandleEditorMouseMove);
        window.removeEventListener('mouseup', this.boundHandleEditorMouseUp);
        this.resetBuildModeState();
        // ---------------------------------------------
    },

    /**
     * Adds a new structure of a given type to the build editor area.
     * The structure will be placed near the center of the editor area.
     * @param {string} typeId - The type ID of the structure (e.g., 'wall', 'refinery').
     */
    addNewStructure: function(typeId) {
        if (!this.buildEditorArea) return;

        // Get the editor dimensions
        const editorRect = this.buildEditorArea.getBoundingClientRect();

        // Default dimensions (can be customized per typeId if needed)
        let defaultWidth = 100;
        let defaultHeight = 50;
        let bgColor = '#666'; // Default background
        let structureName = Utils.capitalize(typeId); // Default name

        // Customize defaults based on typeId
        if (typeId === 'wall') {
            defaultWidth = 100;
            defaultHeight = 20;
            bgColor = '#8B4513'; // SaddleBrown
            structureName = 'Wall';
        } else if (typeId === 'refinery') {
            defaultWidth = 80;
            defaultHeight = 80;
            bgColor = '#4b948f'; // Use color from building_types.json
            structureName = 'Refinery';
        }
        // Add more else if blocks for other structure types

        // Calculate position to center the structure in the editor
        const editorCenterX = editorRect.width / 2;
        const editorCenterY = editorRect.height / 2;

        // Calculate top-left position
        const editorX = editorCenterX - (defaultWidth / 2);
        const editorY = editorCenterY - (defaultHeight / 2);

        // Create a unique ID for the new structure
        const timestamp = Date.now();
        const newStructureId = `${typeId}_${timestamp}`;

        // Create the structure element
        const structureElement = document.createElement('div');
        structureElement.className = 'build-structure';
        structureElement.dataset.id = newStructureId;
        structureElement.dataset.typeId = typeId; // Store the typeId

        // Set position and size
        structureElement.style.left = `${editorX}px`;
        structureElement.style.top = `${editorY}px`;
        structureElement.style.width = `${defaultWidth}px`;
        structureElement.style.height = `${defaultHeight}px`;

        // Set background color
        structureElement.style.backgroundColor = bgColor;

        // Add label text (using the determined structureName)
        structureElement.textContent = `${structureName} (${newStructureId})`;

        // Add resize handle
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'resize-handle';
        structureElement.appendChild(resizeHandle);

        // Add delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'delete-structure-btn';
        deleteBtn.innerHTML = '&times;'; // 'X' symbol
        deleteBtn.title = 'Delete Structure';
        deleteBtn.addEventListener('click', () => {
            structureElement.remove();
            console.log(`Removed structure element ${structureElement.dataset.id} from editor.`);
        });
        structureElement.appendChild(deleteBtn);

        // Add to editor
        this.buildEditorArea.appendChild(structureElement);

        // Show confirmation message
        Game.addMessage(`New ${structureName} added. Drag to position and resize as needed.`, "info");
    },


    /**
     * Update the skills panel display in the Skills tab
     */
    updateSkillsPanel: function() {
        const skillsPanel = document.getElementById('skills-panel');
        if (!skillsPanel) {
            console.error("Skills panel element not found!");
            return;
        }

        skillsPanel.innerHTML = ''; // Clear previous content

        const skills = [
            "Daggers", "Swords and axes", "Archery", "Magic", "Thieving",
            "Hunting", "Looting", "Crafting", "Fishing", "Trading",
            "Stealth", "Reading"
        ];

        // Placeholder data (replace with actual Player.skills later)
        const placeholderLevel = 1;
        const placeholderCurrentXP = 0;
        const placeholderNeededXP = 100;
        const placeholderXPPercent = 0;

        skills.forEach(skillName => {
            const skillBox = document.createElement('div');
            skillBox.className = 'skill-box';

            skillBox.innerHTML = `
                <div class="skill-name">${skillName}</div>
                <div class="skill-level">Level: ${placeholderLevel}</div>
                <div class="skill-xp-bar-outer">
                    <div class="skill-xp-bar-inner" style="width: ${placeholderXPPercent}%;"></div>
                </div>
                <div class="skill-xp-label">${placeholderCurrentXP} / ${placeholderNeededXP} XP</div>
            `;

            skillsPanel.appendChild(skillBox);
        });
    },

    /**
     * Update the quests panel display in the Quests tab
     */
    updateQuestsPanel: function() {
        const activeQuestsList = document.getElementById('active-quests-list');
        const completedQuestsList = document.getElementById('completed-quests-list');

        if (!activeQuestsList || !completedQuestsList) {
            console.error("Quests panel elements not found!");
            return;
        }

        // Clear previous content
        activeQuestsList.innerHTML = '';
        completedQuestsList.innerHTML = '';

        // Check if QuestManager is initialized
        if (typeof QuestManager === 'undefined' || !QuestManager.getQuestsForUI) {
            activeQuestsList.innerHTML = '<p class="empty-message">Quest system not initialized.</p>';
            completedQuestsList.innerHTML = '<p class="empty-message">No completed quests.</p>';
            return;
        }

        // Get quest data from QuestManager
        const questData = QuestManager.getQuestsForUI();
        const activeQuests = questData.activeQuests;
        const completedQuests = questData.completedQuests;

        // Display active quests
        if (!activeQuests || activeQuests.length === 0) {
            activeQuestsList.innerHTML = '<p class="empty-message">No active quests.</p>';
        } else {
            activeQuests.forEach(quest => {
                const questElement = this.createQuestElement(quest, false);
                activeQuestsList.appendChild(questElement);
            });
        }

        // Display completed quests
        if (!completedQuests || completedQuests.length === 0) {
            completedQuestsList.innerHTML = '<p class="empty-message">No completed quests.</p>';
        } else {
            completedQuests.forEach(quest => {
                const questElement = this.createQuestElement(quest, true);
                completedQuestsList.appendChild(questElement);
            });
        }
    },

    /**
     * Create a quest element for display in the quests panel
     * @param {Object} quest - The quest object
     * @param {boolean} isCompleted - Whether the quest is completed
     * @returns {HTMLElement} - The quest element
     */
    createQuestElement: function(quest, isCompleted) {
        const questElement = document.createElement('div');
        questElement.className = 'quest-item';
        if (isCompleted) {
            questElement.classList.add('completed');
        }

        // Create quest header with title and status
        const questHeader = document.createElement('div');
        questHeader.className = 'quest-header';

        const questTitle = document.createElement('h5');
        questTitle.className = 'quest-title';
        questTitle.textContent = quest.title;
        questHeader.appendChild(questTitle);

        const questStatus = document.createElement('span');
        questStatus.className = 'quest-status';
        questStatus.textContent = isCompleted ? 'Completed' : 'In Progress';
        questHeader.appendChild(questStatus);

        questElement.appendChild(questHeader);

        // Add description
        const questDescription = document.createElement('p');
        questDescription.className = 'quest-description';
        questDescription.textContent = quest.description;
        questElement.appendChild(questDescription);

        // Add progress for active quests
        if (!isCompleted) {
            const progressDiv = document.createElement('div');
            progressDiv.className = 'quest-progress';

            // Create progress bar container
            const progressBarOuter = document.createElement('div');
            progressBarOuter.className = 'quest-progress-container';

            const progressPercent = Math.min(100, Math.max(0, (quest.progress / quest.amount) * 100));

            // Create the progress bar itself
            const progressBarInner = document.createElement('div');
            progressBarInner.className = 'quest-progress-bar';
            progressBarInner.style.width = `${progressPercent}%`;

            // Add progress text inside the progress bar container
            const progressBarText = document.createElement('div');
            progressBarText.className = 'quest-progress-text-overlay';
            progressBarText.textContent = `${quest.progress}/${quest.amount}`;
            progressBarOuter.appendChild(progressBarText);

            console.log(`Setting progress bar width to ${progressPercent}% for quest ${quest.id}`); // Debug

            progressBarOuter.appendChild(progressBarInner);
            progressDiv.appendChild(progressBarOuter);

            // Add progress text
            const progressText = document.createElement('div');
            progressText.className = 'quest-progress-text';

            // Format the resource name to be more readable
            let resourceName = quest.resourceName || '';
            if (resourceName) {
                // Convert snake_case or kebab-case to Title Case
                resourceName = resourceName
                    .replace(/_/g, ' ')
                    .replace(/-/g, ' ')
                    .split(' ')
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(' ');
            }

            progressText.textContent = `Progress: ${quest.progress}/${quest.amount} ${resourceName}`;
            progressDiv.appendChild(progressText);

            questElement.appendChild(progressDiv);
        }

        // Add rewards section
        if (quest.reward) {
            const rewardsDiv = document.createElement('div');
            rewardsDiv.className = 'quest-rewards';

            const rewardsTitle = document.createElement('h6');
            rewardsTitle.textContent = 'Rewards:';
            rewardsDiv.appendChild(rewardsTitle);

            const rewardsList = document.createElement('ul');
            rewardsList.className = 'rewards-list';

            if (quest.reward.xp) {
                const xpItem = document.createElement('li');
                xpItem.innerHTML = `<span class="reward-value">${quest.reward.xp}</span> XP`;
                rewardsList.appendChild(xpItem);
            }

            if (quest.reward.gold) {
                const goldItem = document.createElement('li');
                goldItem.innerHTML = `<span class="reward-value">${quest.reward.gold}</span> Gold`;
                rewardsList.appendChild(goldItem);
            }

            if (quest.reward.items && quest.reward.items.length > 0) {
                quest.reward.items.forEach(item => {
                    // Try to get the item name from World.items
                    let itemName = item.id;
                    if (typeof World !== 'undefined' && World.items && World.items[item.id]) {
                        itemName = World.items[item.id].name;
                    } else {
                        // Format the item ID to be more readable
                        itemName = itemName
                            .replace(/_/g, ' ')
                            .replace(/-/g, ' ')
                            .split(' ')
                            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                            .join(' ');
                    }

                    const itemElement = document.createElement('li');
                    itemElement.innerHTML = `<span class="reward-value">${item.quantity || 1}x</span> ${itemName}`;
                    rewardsList.appendChild(itemElement);
                });
            }

            rewardsDiv.appendChild(rewardsList);
            questElement.appendChild(rewardsDiv);
        }

        // Add completion date for completed quests
        if (isCompleted && quest.completedDate) {
            const completionDiv = document.createElement('div');
            completionDiv.className = 'quest-completion-date';
            completionDiv.textContent = `Completed on: ${quest.completedDate}`;
            questElement.appendChild(completionDiv);
        }

        return questElement;
    },

    /**
     * Initialize chat input listeners
     */
       initChat: function() {
        console.log('UI: initChat() is starting!'); // Good to have for debugging

        // --- START: Initialize Chat Tabs ---
        if (typeof ChatTabs !== 'undefined' && typeof ChatTabs.init === 'function') {
            console.log('UI.initChat: About to call ChatTabs.init()');
            ChatTabs.init(); // <<< --- ADD THIS LINE TO INITIALIZE YOUR TABS
        } else {
            console.error('UI.initChat: ChatTabs object or ChatTabs.init function not found! Cannot initialize chat tabs.');
        }
        // --- END: Initialize Chat Tabs ---

        // Your existing code for chat input and send button
        this.chatInput = document.getElementById('chat-input');
        const chatSendBtn = document.getElementById('chat-send-btn');

        if (this.chatInput && chatSendBtn) {
            this.chatInput.addEventListener('keydown', (event) => {
                if (event.key === 'Enter') {
                    this.sendChatMessage();
                }
                event.stopPropagation();
            }, true);

            chatSendBtn.addEventListener('click', () => {
                this.sendChatMessage();
            });

            this.chatInput.addEventListener('focus', () => {
                this.inputHasFocus = true;
            });
            this.chatInput.addEventListener('blur', () => {
                this.inputHasFocus = false;
            });
        } else {
            console.warn("UI.initChat: Chat input or send button not found!");
        }

        console.log('UI: initChat() finished.'); // Good to have
    },

    /**
     * Send chat message from input
     */
    sendChatMessage: function() {
        if (!this.chatInput) {
            console.error("UI.sendChatMessage: Chat input not initialized.");
            return;
        }
        const messageText = this.chatInput.value.trim();
        if (messageText === '') {
            return; // Don't send empty messages
        }

        // --- Get the active chat type ---
        let currentChatType = 'world'; // Default if ChatTabs is not available
        if (typeof ChatTabs !== 'undefined' && ChatTabs.activeChatType) {
            currentChatType = ChatTabs.activeChatType;
        } else {
            console.warn('UI.sendChatMessage: ChatTabs.activeChatType not found. Defaulting to "world".');
        }
        // --- End Get the active chat type ---

        if (typeof Game !== 'undefined' && typeof Game.sendChatMessage === 'function') {
            // --- Pass both messageText and currentChatType to Game.sendChatMessage ---
            Game.sendChatMessage(messageText, currentChatType);
        } else {
            console.error('UI.sendChatMessage: Game.sendChatMessage function not found.');
        }

        this.chatInput.value = ''; // Clear input
        // this.chatInput.focus(); // Optional: keep focus
    },

    /**
     * Display a received chat message
     * @param {string} username - The sender's username
     * @param {string} message - The chat message text
     * @param {boolean} isAdmin - Whether the sender is an admin
     * @param {string} messageId - Optional message ID for database reference
     * @param {string} timestamp - Optional timestamp for the message
     */
    displayChatMessage: function(username, message, isAdmin = false, messageId = null, timestamp = null) {
        const chatOutput = document.getElementById('chat-output');
        if (!chatOutput) return;

        // Generate a client-side ID if none provided
        const msgId = messageId || 'msg_' + Date.now() + '_' + Math.floor(Math.random() * 1000);

        const messageElement = document.createElement('div');
        messageElement.classList.add('chat-message'); // Add a general class for chat messages
        messageElement.dataset.messageId = msgId; // Store message ID for potential deletion
        messageElement.dataset.username = username; // Store username for reference

        // Make messages selectable with a click
        messageElement.addEventListener('click', (e) => {
            // Only allow admins to select messages
            if (Player.isAdmin) {
                // Toggle selection
                if (messageElement.classList.contains('selected-message')) {
                    messageElement.classList.remove('selected-message');
                    // Hide delete button if it exists
                    const deleteBtn = messageElement.querySelector('.delete-message-btn');
                    if (deleteBtn) deleteBtn.remove();
                } else {
                    // Deselect any previously selected message
                    document.querySelectorAll('.selected-message').forEach(el => {
                        el.classList.remove('selected-message');
                        const btn = el.querySelector('.delete-message-btn');
                        if (btn) btn.remove();
                    });

                    // Select this message
                    messageElement.classList.add('selected-message');

                    // Add delete button for admins
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'delete-message-btn';
                    deleteBtn.textContent = 'Delete';
                    deleteBtn.addEventListener('click', (event) => {
                        event.stopPropagation(); // Prevent triggering the message click event

                        // Confirm deletion
                        if (confirm('Delete this message?')) {
                            // Send delete request to server
                            if (Game.socket && Game.socket.connected) {
                                Game.socket.emit('delete_chat_message', {
                                    messageId: msgId
                                });
                            }
                        }
                    });

                    messageElement.appendChild(deleteBtn);
                }
            }
        });

        let userPrefix = '';
        if (isAdmin) {
            userPrefix = '<span class="admin-tag">[Admin]</span> ';
        }

        // Basic sanitization to prevent trivial HTML injection
        const safeUsername = this.escapeHTML(username);
        const safeMessage = this.escapeHTML(message);

        // Create message content container
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = `${userPrefix}<span class="chat-username">${safeUsername}</span>: ${safeMessage}`;

        // Add timestamp if provided
        if (timestamp) {
            const timeElement = document.createElement('span');
            timeElement.className = 'message-timestamp';
            timeElement.textContent = new Date(timestamp).toLocaleTimeString();
            messageContent.appendChild(timeElement);
        }

        messageElement.appendChild(messageContent);
        chatOutput.appendChild(messageElement);
        this.scrollOutputToBottom();
    },

    /**
     * Escape HTML special characters in a string.
     * @param {string} str - The string to escape.
     * @returns {string} The escaped string.
     */
    escapeHTML: function(str) {
        if (typeof str !== 'string') return str; // Handle non-string inputs
        return str.replace(/[&<>"']/g, function(match) {
            switch (match) {
                case '&': return '&amp;';
                case '<': return '&lt;';
                case '>': return '&gt;';
                case '"': return '&quot;';
                case "'": return '&#39;'; // or &apos;
                default: return match;
            }
        });
    },

    /**
     * Show a mining UI box with progress bar and time left
     * @param {Object} options
     *   oreName: string - name of the ore
     *   miningTime: number - ms to mine
     *   onComplete: function - callback when done
     */
    showMiningBox: function({ oreName, miningTime, onComplete }) {
        // Remove any existing mining box
        let oldBox = document.getElementById('mining-ui-box');
        if (oldBox) oldBox.remove();

        // Find the output/console area
        const output = document.getElementById('output');
        if (!output) {
            console.error('Output console not found for mining UI!');
            return;
        }

        // Create mining UI box
        const box = document.createElement('div');
        box.id = 'mining-ui-box';
        box.className = 'mining-ui-box';
        box.innerHTML = `
            <div class="mining-title">Mining ${oreName}...</div>
            <div class="mining-progress-outer">
                <div class="mining-progress-inner" style="width:0%"></div>
            </div>
            <div class="mining-time-left">Time left: <span class="mining-seconds">0.0</span>s</div>
        `;
        output.appendChild(box);
        this.scrollOutputToBottom();

        // Animate progress bar
        const progressBar = box.querySelector('.mining-progress-inner');
        const timeLabel = box.querySelector('.mining-seconds');
        let start = null;
        let frameId = null;

        function animate(ts) {
            if (!start) start = ts;
            const elapsed = ts - start;
            const percent = Math.min(1, elapsed / miningTime);
            progressBar.style.width = (percent * 100).toFixed(1) + '%';
            const timeLeft = Math.max(0, (miningTime - elapsed) / 1000);
            timeLabel.textContent = timeLeft.toFixed(1);
            if (elapsed < miningTime) {
                frameId = requestAnimationFrame(animate);
            } else {
                progressBar.style.width = '100%';
                timeLabel.textContent = '0.0';
                setTimeout(() => {
                    box.remove();
                    if (typeof onComplete === 'function') onComplete();
                }, 400);
            }
        }
        requestAnimationFrame(animate);
    },

    /**
     * Show a chopping UI box with progress bar and time left
     * @param {Object} options
     *   treeName: string - name of the tree
     *   choppingTime: number - ms to chop
     *   onComplete: function - callback when done
     */
    showChoppingBox: function({ treeName, choppingTime, onComplete }) {
        // Remove any existing chopping box
        let oldBox = document.getElementById('chopping-ui-box');
        if (oldBox) oldBox.remove();

        // Find the output/console area
        const output = document.getElementById('output');
        if (!output) {
            console.error('Output console not found for chopping UI!');
            return;
        }

        // Create chopping UI box
        const box = document.createElement('div');
        box.id = 'chopping-ui-box'; // Distinct ID
        box.className = 'chopping-ui-box'; // Distinct class
        box.innerHTML = `
            <div class="chopping-title">Chopping ${treeName}...</div>
            <div class="chopping-progress-outer">
                <div class="chopping-progress-inner" style="width:0%"></div>
            </div>
            <div class="chopping-time-left">Time left: <span class="chopping-seconds">0.0</span>s</div>
        `;
        output.appendChild(box);
        this.scrollOutputToBottom();

        // Animate progress bar
        const progressBar = box.querySelector('.chopping-progress-inner');
        const timeLabel = box.querySelector('.chopping-seconds');
        let start = null;
        let frameId = null;

        function animate(ts) {
            if (!start) start = ts;
            const elapsed = ts - start;
            const percent = Math.min(1, elapsed / choppingTime);
            progressBar.style.width = (percent * 100).toFixed(1) + '%';
            const timeLeft = Math.max(0, (choppingTime - elapsed) / 1000);
            timeLabel.textContent = timeLeft.toFixed(1);
            if (elapsed < choppingTime) {
                frameId = requestAnimationFrame(animate);
            } else {
                progressBar.style.width = '100%';
                timeLabel.textContent = '0.0';
                setTimeout(() => {
                    box.remove();
                    if (typeof onComplete === 'function') onComplete();
                }, 400); // Short delay before removing and calling callback
            }
        }
        requestAnimationFrame(animate);
    },

    // --- Refinery Functions ---

    /**
     * Fetches item data and populates the refinery bars list (left column)
     * @param {string} categoryFilter - Optional category filter ('all', 'bars', 'leather')
     */
    populateRefineryList: async function(categoryFilter = 'all') {
        const recipeItemsDiv = document.getElementById('refinery-recipe-items');
        if (!recipeItemsDiv) {
            console.error("Refinery recipe items container not found!");
            return;
        }
        recipeItemsDiv.innerHTML = '<p>Loading refinery recipes...</p>';

        try {
            // Access item data from World.items (loaded during World.init)
            const allItems = World.items;
            if (!allItems || Object.keys(allItems).length === 0) {
                recipeItemsDiv.innerHTML = '<p>No refinery recipes found.</p>';
                return;
            }

            recipeItemsDiv.innerHTML = ''; // Clear previous content

            let foundItems = false; // Track if we found any items to display
            for (const itemId in allItems) {
                const item = allItems[itemId];
                // Check for both bar and leather categories
                if ((item.category === 'bar' || item.category === 'leather') && item.recipe && item.craftableAt === 'refinery') {
                    // Apply category filtering
                    if (categoryFilter !== 'all') {
                        // For bars category, check if the item is a metal bar
                        const isMetalBar = item.category === 'bar';

                        // For leather category, check if the item is leather
                        const isLeatherItem = item.category === 'leather';

                        // Skip if category doesn't match
                        if ((categoryFilter === 'bars' && !isMetalBar) ||
                            (categoryFilter === 'leather' && !isLeatherItem)) {
                            continue;
                        }
                    }
                    foundItems = true;
                    // Restore styled refinery item box
                    const itemDiv = document.createElement('div');
                    itemDiv.classList.add('refinery-item');
                    itemDiv.setAttribute('data-item-id', itemId);
                    itemDiv.addEventListener('click', () => {
                        // Remove 'selected' class from previously selected item
                        const previouslySelected = recipeItemsDiv.querySelector('.refinery-item.selected');
                        if (previouslySelected) {
                            previouslySelected.classList.remove('selected');
                        }
                        // Add 'selected' class to the clicked item
                        itemDiv.classList.add('selected');
                        UI.showRefineryItemDetails(itemId);
                    }); // Show details on click

                    // Left side should be simpler - just name and level
                    const nameDiv = document.createElement('div');
                    nameDiv.className = 'bar-name';
                    nameDiv.textContent = item.name || 'Unknown Bar';
                    itemDiv.appendChild(nameDiv);

                    // Add level if available
                    if (item.level) {
                        const levelDiv = document.createElement('div');
                        levelDiv.className = 'bar-level';
                        levelDiv.textContent = `Level ${item.level}`;
                        itemDiv.appendChild(levelDiv);
                    }

                    // Add recipe components in small boxes
                    const recipePreview = document.createElement('div');
                    recipePreview.className = 'recipe-preview';

                    // Special handling for leather items to show "Level X Pelts" instead of specific pelt IDs
                    if (item.category === 'leather') {
                        const leatherLevel = item.level || 1;

                        const reqBox = document.createElement('div');
                        reqBox.className = 'recipe-component';

                        const reqQty = document.createElement('span');
                        reqQty.className = 'component-qty';
                        reqQty.textContent = '1';

                        const reqName = document.createElement('span');
                        reqName.className = 'component-name';
                        reqName.textContent = `Level ${leatherLevel} Pelt`;

                        reqBox.appendChild(reqQty);
                        reqBox.appendChild(document.createTextNode(' × '));
                        reqBox.appendChild(reqName);
                        recipePreview.appendChild(reqBox);
                    } else {
                        // Standard recipe display for non-leather items
                        for (const reqId in item.recipe) {
                            const reqItem = allItems[reqId];
                            const reqAmount = item.recipe[reqId];

                            const reqBox = document.createElement('div');
                            reqBox.className = 'recipe-component';

                            const reqQty = document.createElement('span');
                            reqQty.className = 'component-qty';
                            reqQty.textContent = reqAmount;

                            const reqName = document.createElement('span');
                            reqName.className = 'component-name';
                            reqName.textContent = reqItem ? reqItem.name : reqId;

                            reqBox.appendChild(reqQty);
                            reqBox.appendChild(document.createTextNode(' × '));
                            reqBox.appendChild(reqName);
                            recipePreview.appendChild(reqBox);
                        }
                    }

                    itemDiv.appendChild(recipePreview);

                    recipeItemsDiv.appendChild(itemDiv);
                }
            }
            if (!foundItems) {
                if (categoryFilter === 'bars') {
                    recipeItemsDiv.innerHTML = '<p>No refinery recipes found for bars.</p>';
                } else if (categoryFilter === 'leather') {
                    recipeItemsDiv.innerHTML = '<p>No refinery recipes found for leather.</p>';
                } else {
                    recipeItemsDiv.innerHTML = '<p>No refinery recipes found.</p>';
                }
            }
        } catch (error) {
            console.error('Error loading or processing refinery bars:', error);
            recipeItemsDiv.innerHTML = '<p>Error loading refinery recipes. Please try again later.</p>';
        }
    },

    // --- Premium Functions ---

    /**
     * Opens the premium subscription popup.
     */
    openPremiumPopup: function() {
        const modal = document.getElementById('premium-modal');
        if (!modal) {
            console.error('Premium modal element not found!');
            Game.addMessage('Premium interface failed to load.', 'error');
            return;
        }

        // Show the modal
        modal.classList.add('visible');

        // Add event listener for the premium buy button
        const buyBtn = document.getElementById('premium-buy-btn');
        if (buyBtn) {
            buyBtn.addEventListener('click', () => {
                Game.addMessage('Premium subscriptions coming soon!', 'info');
            });
        }

        // Add event listener for the close button
        const closeBtn = modal.querySelector('.close-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closePremiumPopup();
            });
        }

        // Add event listener to close when clicking outside the modal content
        modal.addEventListener('click', (event) => {
            if (event.target === modal) {
                this.closePremiumPopup();
            }
        });
    },

    /**
     * Closes the premium subscription popup.
     */
    closePremiumPopup: function() {
        const modal = document.getElementById('premium-modal');
        if (modal) {
            modal.classList.remove('visible');
        }
    },

    // --- Leaderboard Functions ---

    /**
     * Opens the leaderboard popup and fetches user data.
     */
    openLeaderboardPopup: function() {
        const modal = document.getElementById('leaderboard-modal');
        if (!modal) {
            console.error('Leaderboard modal element not found!');
            Game.addMessage('Leaderboard interface failed to load.', 'error');
            return;
        }

        // Show the modal
        modal.classList.add('visible');

        // Fetch leaderboard data
        this.fetchLeaderboardData();
    },

    /**
     * Fetches leaderboard data from the server.
     */
    fetchLeaderboardData: function() {
        const leaderboardList = document.getElementById('leaderboard-list');
        if (!leaderboardList) {
            console.error('Leaderboard list element not found!');
            return;
        }

        // Show loading message
        leaderboardList.innerHTML = '<p>Loading leaderboard data...</p>';

        // Get session ID from localStorage
        const sessionId = localStorage.getItem('sessionId');
        if (!sessionId) {
            leaderboardList.innerHTML = '<p>You must be logged in to view the leaderboard.</p>';
            return;
        }

        // Fetch leaderboard data from the server
        fetch('/api/get_leaderboard', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ sessionId })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                this.populateLeaderboard(data.users, data.currentUsername);
            } else {
                leaderboardList.innerHTML = `<p>Error: ${data.error || 'Failed to load leaderboard data'}</p>`;
            }
        })
        .catch(error => {
            console.error('Error fetching leaderboard data:', error);
            leaderboardList.innerHTML = `<p>Error: ${error.message || 'Failed to load leaderboard data'}</p>`;
        });
    },

    /**
     * Populates the leaderboard with user data.
     * @param {Array} users - Array of user objects with username and level
     * @param {string} currentUsername - The current user's username
     */
    populateLeaderboard: function(users, currentUsername) {
        const leaderboardList = document.getElementById('leaderboard-list');
        if (!leaderboardList) {
            console.error('Leaderboard list element not found!');
            return;
        }

        if (!users || users.length === 0) {
            leaderboardList.innerHTML = '<p>No users found.</p>';
            return;
        }

        // Create table for leaderboard
        let html = `
            <table class="leaderboard-table">
                <thead>
                    <tr>
                        <th>Rank</th>
                        <th>Username</th>
                        <th>Level</th>
                    </tr>
                </thead>
                <tbody>
        `;

        // Add rows for each user
        users.forEach((user, index) => {
            const isCurrentUser = user.isCurrentUser;
            html += `
                <tr class="${isCurrentUser ? 'current-user' : ''}">
                    <td class="leaderboard-rank">${index + 1}</td>
                    <td class="leaderboard-username">${user.username}${isCurrentUser ? ' (You)' : ''}</td>
                    <td class="leaderboard-level">${user.level}</td>
                </tr>
            `;
        });

        html += `
                </tbody>
            </table>
        `;

        leaderboardList.innerHTML = html;
    },

    // --- Refinery Functions ---

    /**
     * Opens the refinery modal and populates the list of craftable items.
     */
    openRefineryPopup: function() {
        const modal = document.getElementById('refinery-modal'); // Use refinery ID
        if (!modal) {
            console.error('Refinery modal element not found!');
            return;
        }

        // Get the details div for clearing when category changes
        const detailsDiv = document.getElementById('refinery-bar-details');
        if (!detailsDiv) {
            console.error('Refinery details element not found!');
            return;
        }

        // Initial population with current category or 'all' if not set
        this.populateRefineryList(this.currentRefineryCategory);
        detailsDiv.innerHTML = '<p>Select an item to see details.</p>'; // Clear details on open

        // Add event listeners for category buttons
        const categoryButtons = modal.querySelectorAll('.refinery-category-btn');
        categoryButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove 'active' from currently active button
                const currentActive = modal.querySelector('.refinery-category-btn.active');
                if (currentActive) {
                    currentActive.classList.remove('active');
                }
                // Add 'active' to clicked button
                button.classList.add('active');

                // Get the category and repopulate
                const category = button.dataset.category;
                // Store the current category
                this.currentRefineryCategory = category;
                this.populateRefineryList(category);

                // Clear the details pane when category changes
                detailsDiv.innerHTML = '<p>Select an item to see details.</p>';
            });
        });

        // Show the modal
        modal.classList.add('visible'); // Show the modal using the 'visible' class
        this.focusCommandInput(); // Keep focus logic if needed
    },

    /**
     * Displays the details for a selected refinery item (right column).
     * @param {string} itemId - The ID of the item to display details for.
     */
    showRefineryItemDetails: function(itemId) {
        const detailsDiv = document.getElementById('refinery-bar-details'); // Corrected ID to match HTML
        const allItems = World.items;
        const item = allItems[itemId];

        if (!detailsDiv || !item || !item.recipe) { // Removed craftableAt check
            detailsDiv.innerHTML = '<p>Select an item to see details.</p>';
            return;
        }

        detailsDiv.innerHTML = ''; // Clear previous details

        const container = document.createElement('div');
        container.classList.add('refinery-details-container'); // Use refinery class

        // Item Name
        const nameHeader = document.createElement('h3');
        nameHeader.textContent = item.name;
        container.appendChild(nameHeader);

        // Item Description
        const descriptionP = document.createElement('p');
        descriptionP.textContent = item.description;
        container.appendChild(descriptionP);

        // Recipe
        const recipeDiv = document.createElement('div');
        recipeDiv.classList.add('refinery-recipe'); // Use refinery class
        const recipeTitle = document.createElement('strong');

        // Special handling for leather items
        let maxCraft = Infinity;
        if (item.category === 'leather') {
            const leatherLevel = item.level || 1;
            recipeTitle.textContent = `Requires: Level ${leatherLevel} Pelts`;
            recipeDiv.appendChild(recipeTitle);

            // Find all pelts in player inventory that match the required level
            const availablePelts = Player.inventory.filter(invItem => {
                const itemDef = World.items[invItem.id];
                return itemDef && itemDef.category === 'pelt' && itemDef.level === leatherLevel;
            });

            // Calculate max craft based on total available pelts
            const totalPelts = availablePelts.reduce((total, pelt) => total + pelt.quantity, 0);
            maxCraft = totalPelts;

            const ul = document.createElement('ul');
            if (availablePelts.length === 0) {
                const li = document.createElement('li');
                li.textContent = `No level ${leatherLevel} pelts available.`;
                ul.appendChild(li);
                maxCraft = 0;
            } else {
                // List all available pelts of the right level
                availablePelts.forEach(pelt => {
                    const peltDef = World.items[pelt.id];
                    const li = document.createElement('li');
                    const nameSpan = document.createElement('span');
                    nameSpan.className = 'req-name';
                    nameSpan.textContent = `1 × ${peltDef ? peltDef.name : pelt.id}`;
                    li.appendChild(nameSpan);

                    const stockSpan = document.createElement('span');
                    stockSpan.className = 'req-stock';
                    stockSpan.textContent = `You have ${pelt.quantity}`;
                    li.appendChild(stockSpan);
                    ul.appendChild(li);
                });

                // Add total count
                const totalLi = document.createElement('li');
                totalLi.innerHTML = `<strong>Total level ${leatherLevel} pelts: ${totalPelts}</strong>`;
                ul.appendChild(totalLi);
            }
            recipeDiv.appendChild(ul);
        } else {
            // Standard recipe display for non-leather items
            recipeTitle.textContent = 'Requires:';
            recipeDiv.appendChild(recipeTitle);

            const ul = document.createElement('ul');
            for (const reqId in item.recipe) {
                const need = item.recipe[reqId];
                const reqItem = allItems[reqId];
                const inv = Player.inventory.find(i => i.id === reqId) || {quantity:0};
                const canMake = Math.floor(inv.quantity / need);
                maxCraft = Math.min(maxCraft, canMake);
                const li = document.createElement('li');
                const nameSpan = document.createElement('span');
                nameSpan.className = 'req-name';
                nameSpan.textContent = `${need} × ${reqItem ? reqItem.name : reqId}`;
                li.appendChild(nameSpan);

                const stockSpan = document.createElement('span');
                stockSpan.className = 'req-stock';
                stockSpan.textContent = `You have ${inv.quantity}`;
                li.appendChild(stockSpan);
                ul.appendChild(li);
            }
            recipeDiv.appendChild(ul);
        }
        container.appendChild(recipeDiv);

        // Quantity slider
        const lbl = document.createElement('label');
        lbl.setAttribute('for', 'refinery-quantity-slider'); // Use refinery ID
        lbl.textContent = 'Quantity: ';
        container.appendChild(lbl);
        const sliderWrapper = document.createElement('div');
        sliderWrapper.classList.add('slider-wrapper');
        const slider = document.createElement('input');
        slider.type = 'range'; slider.id = 'refinery-quantity-slider'; // Use refinery ID
        slider.min = 1; slider.max = (maxCraft > 0 ? maxCraft : 1); slider.value = 1;
        if (maxCraft === 0) slider.disabled = true;
        const valSpan = document.createElement('span');
        valSpan.id = 'refinery-quantity-value'; // Use refinery ID
        valSpan.textContent = slider.value;
        slider.oninput = () => valSpan.textContent = slider.value;
        sliderWrapper.appendChild(slider);
        sliderWrapper.appendChild(valSpan);
        container.appendChild(sliderWrapper);

        // Craft button
        const btn = document.createElement('button');
        btn.id = 'refinery-craft-btn'; // Use refinery ID
        btn.textContent = 'Craft';
        if (maxCraft === 0) btn.disabled = true;
        btn.onclick = () => UI.craftRefineryItem(itemId, parseInt(slider.value)); // Call refinery craft func
        container.appendChild(btn);

        // Render
        detailsDiv.innerHTML = '';
        detailsDiv.appendChild(container);
    },

    /**
     * Handle crafting of a refinery item
     */
    craftRefineryItem: function(itemId, quantity = 1) {
        const def = World.items[itemId];
        if (!def || !def.recipe || def.craftableAt !== 'refinery') {
            Game.addMessage('Invalid refinery recipe.', 'error'); return;
        }

        // Special handling for leather crafting
        if (def.category === 'leather') {
            // Get the level of leather we're trying to craft
            const leatherLevel = def.level || 1;

            // Find all pelts in player inventory that match the required level
            const availablePelts = Player.inventory.filter(item => {
                const itemDef = World.items[item.id];
                return itemDef && itemDef.category === 'pelt' && itemDef.level === leatherLevel;
            });

            // Check if we have enough pelts of the right level
            if (availablePelts.length === 0 || availablePelts.reduce((total, pelt) => total + pelt.quantity, 0) < quantity) {
                Game.addMessage(`Not enough level ${leatherLevel} pelts.`, 'error');
                return;
            }

            // Consume pelts (starting with the first available one)
            let remainingToConsume = quantity;
            for (let i = 0; i < availablePelts.length && remainingToConsume > 0; i++) {
                const pelt = availablePelts[i];
                const amountToConsume = Math.min(pelt.quantity, remainingToConsume);
                Player.removeItem(pelt.id, amountToConsume);
                remainingToConsume -= amountToConsume;
            }

            // Produce leather
            Player.addItem(def, quantity);
            Game.addMessage(`Crafted ${quantity} x ${def.name} from level ${leatherLevel} pelts.`, 'success');
        } else {
            // Standard recipe handling for non-leather items
            // verify materials
            for (const reqId in def.recipe) {
                const needTotal = def.recipe[reqId] * quantity;
                if (!Player.hasItem(reqId, needTotal)) {
                    Game.addMessage(`Not enough ${World.items[reqId]?.name || reqId}.`, 'error'); return;
                }
            }
            // consume
            for (const reqId in def.recipe) {
                const need = def.recipe[reqId] * quantity;
                Player.removeItem(reqId, need);
            }
            // produce
            Player.addItem(def, quantity);
            Game.addMessage(`Crafted ${quantity} x ${def.name}.`, 'success');
        }

        // refresh UI with the current category
        UI.populateRefineryList(UI.currentRefineryCategory); // Refresh refinery list with current category
        UI.showRefineryItemDetails(itemId); // Refresh refinery details
        if (typeof Inventory !== 'undefined' && typeof Inventory.updateDisplay === 'function') Inventory.updateDisplay(); // Update Inventory UI
        Player.save(); // Save player data after crafting
    },

    // --- Lumbermill Functions ---

    /**
     * Opens the Lumbermill popup window and populates the list.
     */
    openLumbermillPopup: function() {
        const popup = document.getElementById('lumbermill-popup');
        if (popup) {
            this.populateLumbermillList();
            const detailsDiv = document.getElementById('lumbermill-details');
            if (detailsDiv) {
                detailsDiv.innerHTML = '<p>Select an item to craft.</p>'; // Clear details
            }
            // popup.style.display = 'flex'; // Show the modal using flex <-- OLD WAY
            popup.classList.add('visible'); // Show the modal using the 'visible' class
            this.focusCommandInput(); // Keep focus on command input if needed, or remove
        } else {
            console.error('Lumbermill popup element not found!');
            Game.addMessage('Lumbermill interface failed to load.', 'error');
        }
    },

    /**
     * Fetches item data and populates the lumbermill items list (left column).
     * Looks for items with `craftableAt: 'lumbermill'`.
     */
    populateLumbermillList: function() {
        const listDiv = document.getElementById('lumbermill-list');
        if (!listDiv) {
            console.error('Lumbermill list element not found!');
            return;
        }
        listDiv.innerHTML = ''; // Clear previous list

        const allItems = World.items; // Assuming World.items holds all item definitions
        let foundItems = false;

        // TODO: Ensure item definitions in world.js have a 'craftableAt' property.
        for (const itemId in allItems) {
            const item = allItems[itemId];
            // Check if the item is specifically craftable at a lumbermill
            if (item.recipe && item.craftableAt === 'lumbermill') { // Filter condition changed
                foundItems = true;
                const listItem = document.createElement('div');
                listItem.classList.add('refinery-item'); // Use refinery-item for black box consistency
                listItem.dataset.itemId = itemId;
                // listItem.textContent = item.name || itemId; // *** REMOVE THIS SIMPLE TEXT LINE ***

                // *** START: Replicate structure from populateRefineryList ***

                // Left side: name and level (if applicable)
                const nameDiv = document.createElement('div');
                nameDiv.className = 'bar-name'; // Use refinery's class for consistency
                nameDiv.textContent = item.name || 'Unknown Item';
                listItem.appendChild(nameDiv);

                // Add level if available
                if (item.level) {
                    const levelDiv = document.createElement('div');
                    levelDiv.className = 'bar-level'; // Use refinery's class
                    levelDiv.textContent = `Level ${item.level}`;
                    listItem.appendChild(levelDiv);
                }

                // Add recipe components in small boxes
                const recipePreview = document.createElement('div');
                recipePreview.className = 'recipe-preview'; // Use refinery's class

                for (const reqId in item.recipe) {
                    const reqItem = allItems[reqId];
                    const reqAmount = item.recipe[reqId];

                    const reqBox = document.createElement('div');
                    reqBox.className = 'recipe-component'; // Use refinery's class

                    const reqQty = document.createElement('span');
                    reqQty.className = 'component-qty'; // Use refinery's class
                    reqQty.textContent = reqAmount;

                    const reqName = document.createElement('span');
                    reqName.className = 'component-name'; // Use refinery's class
                    reqName.textContent = reqItem ? reqItem.name : reqId;

                    reqBox.appendChild(reqQty);
                    reqBox.appendChild(document.createTextNode(' × ')); // Add separator
                    reqBox.appendChild(reqName);
                    recipePreview.appendChild(reqBox);
                }

                listItem.appendChild(recipePreview);

                // *** END REPLICATED STRUCTURE ***

                // Click event listener
                listItem.addEventListener('click', () => {
                    // Find selected item within the PARENT list div to handle deselection correctly
                    const listDiv = document.getElementById('lumbermill-list');
                    const currentlySelected = listDiv.querySelector('.refinery-item.selected');
                    if (currentlySelected) {
                        currentlySelected.classList.remove('selected');
                    }
                    // Select current
                    listItem.classList.add('selected');
                    // Show details
                    this.showLumbermillItemDetails(itemId);
                });

                // Append the item to the new div
                listDiv.appendChild(listItem);
            }
        }

        if (!foundItems) {
            listDiv.innerHTML = '<p>No lumbermill recipes available.</p>';
        }
    },

    /**
     * Shows the details for a selected lumbermill item in the right column.
     * @param {string} itemId - The ID of the item to show details for.
     */
    showLumbermillItemDetails: function(itemId) {
        const detailsDiv = document.getElementById('lumbermill-details');
        if (!detailsDiv) {
            console.error('Lumbermill details element not found!');
            return;
        }
        const allItems = World.items;
        const item = allItems[itemId];

        if (!item || !item.recipe) { // Removed craftableAt check
            detailsDiv.innerHTML = '<p>Could not load item details.</p>';
            return;
        }

        // Build detailed UI: recipe list, quantity slider, craft button
        const container = document.createElement('div');
        container.classList.add('lumbermill-details-container'); // Use lumbermill class
        // Title
        const title = document.createElement('h3');
        title.textContent = `${item.name}`;
        container.appendChild(title);

        // Item Description
        const descriptionP = document.createElement('p');
        descriptionP.textContent = item.description;
        container.appendChild(descriptionP);

        // Recipe
        const recipeDiv = document.createElement('div');
        recipeDiv.classList.add('lumbermill-recipe'); // Use lumbermill class
        const recipeTitle = document.createElement('strong');
        recipeTitle.textContent = 'Requires:';
        recipeDiv.appendChild(recipeTitle);

        const ul = document.createElement('ul');
        let maxCraft = Infinity;
        for (const reqId in item.recipe) {
            const need = item.recipe[reqId];
            const reqItem = allItems[reqId];
            const inv = Player.inventory.find(i => i.id === reqId) || {quantity:0};
            const canMake = Math.floor(inv.quantity/need);
            maxCraft = Math.min(maxCraft, canMake);
            const li = document.createElement('li');
            const nameSpan = document.createElement('span');
            nameSpan.className = 'req-name';
            nameSpan.textContent = `${need} × ${reqItem ? reqItem.name : reqId}`;
            li.appendChild(nameSpan);

            const stockSpan = document.createElement('span');
            stockSpan.className = 'req-stock';
            stockSpan.textContent = `You have ${inv.quantity}`;
            li.appendChild(stockSpan);
            ul.appendChild(li);
        }
        recipeDiv.appendChild(ul);
        container.appendChild(recipeDiv);

        // Quantity slider
        const lbl = document.createElement('label');
        lbl.setAttribute('for','lumbermill-quantity-slider'); // Use lumbermill ID
        lbl.textContent = 'Quantity: ';
        container.appendChild(lbl);
        const sliderWrapper = document.createElement('div');
        sliderWrapper.classList.add('slider-wrapper');
        const slider = document.createElement('input');
        slider.type='range'; slider.id='lumbermill-quantity-slider'; // Use lumbermill ID
        slider.min = 1; slider.max = (maxCraft > 0 ? maxCraft : 1); slider.value = 1;
        if (maxCraft === 0) slider.disabled = true;
        const valSpan = document.createElement('span');
        valSpan.id = 'lumbermill-quantity-value'; // Use lumbermill ID
        valSpan.textContent = slider.value;
        slider.oninput = () => valSpan.textContent = slider.value;
        sliderWrapper.appendChild(slider);
        sliderWrapper.appendChild(valSpan);
        container.appendChild(sliderWrapper);

        // Craft button
        const btn = document.createElement('button');
        btn.id='lumbermill-craft-btn'; // Use lumbermill ID
        btn.textContent='Craft';
        if (maxCraft === 0) btn.disabled = true;
        btn.onclick = () => UI.craftLumbermillItem(itemId, parseInt(slider.value)); // Call lumbermill craft func
        container.appendChild(btn);

        // Render
        detailsDiv.innerHTML='';
        detailsDiv.appendChild(container);
    },

    /**
     * Handle crafting of a lumbermill item
     */
    craftLumbermillItem: function(itemId, quantity = 1) {
        const def = World.items[itemId];
        if (!def || !def.recipe || def.craftableAt !== 'lumbermill') {
            Game.addMessage('Invalid lumbermill recipe.', 'error'); return;
        }
        // verify materials
        for (const reqId in def.recipe) {
            const needTotal = def.recipe[reqId]*quantity;
            if (!Player.hasItem(reqId, needTotal)) {
                Game.addMessage(`Not enough ${World.items[reqId]?.name || reqId}.`, 'error'); return;
            }
        }
        // consume
        for (const reqId in def.recipe) {
            const need = def.recipe[reqId]*quantity;
            Player.removeItem(reqId, need);
        }
        // produce
        Player.addItem(def, quantity);
        Game.addMessage(`Crafted ${quantity} x ${def.name}.`, 'success');
        // refresh UI with the current category
        UI.populateLumbermillList(UI.currentLumbermillCategory); // Refresh lumbermill list with current category
        UI.showLumbermillItemDetails(itemId); // Refresh lumbermill details
        if (typeof Inventory !== 'undefined' && typeof Inventory.updateDisplay === 'function') Inventory.updateDisplay(); // Update Inventory UI
        Player.save(); // Save player data after crafting
    },

    // --- Crafting Bench Functions ---

    /**
     * Opens the Crafting Bench popup window and populates the list.
     */
    openCraftingBenchPopup: function(structureId) {
        console.log(`Opening crafting bench popup for structure: ${structureId}`);
        const modal = document.getElementById('crafting-bench-popup');
        if (!modal) {
            console.error('Crafting bench modal element not found!');
            Game.addMessage('Crafting bench interface failed to load.', 'error');
            return;
        }
        this.populateCraftingBenchList(); // Populate the list
        modal.style.display = 'flex'; // Show the modal using flex for centering
        this.focusCommandInput(); // Keep focus logic if needed
    },

    /**
     * Fetches item data and populates the crafting bench items list (left column).
     * Displays all items with recipes, excluding bars and planks.
     */
    populateCraftingBenchList: function(categoryFilter = 'all') { // ADD categoryFilter argument
        const recipeItemsDiv = document.getElementById('crafting-recipe-items');
        if (!recipeItemsDiv) {
            console.error('Crafting recipe items container element not found!');
            return;
        }
        recipeItemsDiv.innerHTML = '<p>Loading crafting recipes...</p>';

        try {
            // Access item data from World.items (loaded during World.init)
            const allItems = World.items;
            if (!allItems || Object.keys(allItems).length === 0) {
                recipeItemsDiv.innerHTML = '<p>No crafting recipes found.</p>';
                return;
            }

            recipeItemsDiv.innerHTML = '';
            let foundItems = false;

            for (const itemId in allItems) {
                const item = allItems[itemId];
                // Check if the item has a recipe but is not a bar or plank
                // and not already assigned to refinery or lumbermill
                if (item.recipe &&
                    item.category !== 'bar' &&
                    item.category !== 'plank' &&
                    item.craftableAt !== 'refinery' &&
                    item.craftableAt !== 'lumbermill') {

                    // ADD category filtering logic
                    if (categoryFilter !== 'all' && item.category !== categoryFilter) {
                        continue; // Skip this item if its category doesn't match the filter
                    }

                    foundItems = true;
                    // Create item box with same styling as refinery
                    const itemDiv = document.createElement('div');
                    itemDiv.classList.add('refinery-item');
                    itemDiv.dataset.itemId = itemId;

                    // Left side: name and level (if applicable)
                    const nameDiv = document.createElement('div');
                    nameDiv.className = 'bar-name';
                    nameDiv.textContent = item.name || 'Unknown Item';
                    itemDiv.appendChild(nameDiv);

                    if (item.level) {
                        const levelDiv = document.createElement('div');
                        levelDiv.className = 'bar-level';
                        levelDiv.textContent = `Level ${item.level}`;
                        itemDiv.appendChild(levelDiv);
                    }

                    // Add recipe components in small boxes
                    const recipePreview = document.createElement('div');
                    recipePreview.className = 'recipe-preview';

                    for (const reqId in item.recipe) {
                        const reqItem = allItems[reqId];
                        const reqAmount = item.recipe[reqId];

                        const reqBox = document.createElement('div');
                        reqBox.className = 'recipe-component';

                        const reqQty = document.createElement('span');
                        reqQty.className = 'component-qty';
                        reqQty.textContent = reqAmount;

                        const reqName = document.createElement('span');
                        reqName.className = 'component-name';
                        reqName.textContent = reqItem ? reqItem.name : reqId;

                        reqBox.appendChild(reqQty);
                        reqBox.appendChild(document.createTextNode(' × '));
                        reqBox.appendChild(reqName);
                        recipePreview.appendChild(reqBox);
                    }

                    itemDiv.appendChild(recipePreview);
                    // *** END REPLICATED STRUCTURE ***

                    // Click event listener
                    itemDiv.addEventListener('click', () => {
                        // Find selected item within the PARENT list div to handle deselection correctly
                        const listDiv = document.getElementById('crafting-bench-list');
                        const currentlySelected = listDiv.querySelector('.refinery-item.selected');
                        if (currentlySelected) {
                            currentlySelected.classList.remove('selected');
                        }
                        // Select current
                        itemDiv.classList.add('selected');
                        // Show details
                        this.showCraftingBenchItemDetails(itemId);
                    });

                    // Append the item to the new div
                    recipeItemsDiv.appendChild(itemDiv);
                }
            }

            if (!foundItems) {
                recipeItemsDiv.innerHTML = '<p>No items craftable at the bench.</p>';
            }
        } catch (error) {
            console.error('Error loading or processing crafting recipes:', error);
            recipeItemsDiv.innerHTML = '<p>Error loading crafting recipes. Please try again later.</p>';
        }
    },

    /**
     * Opens the Crafting Bench popup window and populates the list.
     */
    openCraftingBenchPopup: function(structureId) {
        console.log(`Opening crafting bench popup for structure: ${structureId}`);
        const modal = document.getElementById('crafting-bench-popup');
        if (!modal) {
            console.error('Crafting bench modal element not found!');
            Game.addMessage('Crafting bench interface failed to load.', 'error');
            return;
        }
        const detailsDiv = document.getElementById('crafting-bench-details'); // Get details div
        if (!modal || !detailsDiv) { // Check details div too
            console.error('Crafting bench modal or details element not found!');
            Game.addMessage('Crafting bench interface failed to load.', 'error');
            return;
        }

        // Initial population with current category or 'all' if not set
        this.populateCraftingBenchList(this.currentCraftingBenchCategory); // Use stored category
        detailsDiv.innerHTML = '<p>Select an item to see details.</p>'; // Clear details on open
        // modal.style.display = 'flex'; <-- OLD WAY
        modal.classList.add('visible'); // Show the modal using the 'visible' class

        // --- ADD Event Listeners for Category Buttons ---
        const categoryButtons = modal.querySelectorAll('.crafting-category-btn');
        categoryButtons.forEach(button => {
            // Remove any old listeners first if popup might reopen without page refresh (optional but safer)
            // button.replaceWith(button.cloneNode(true)); // Simple way to remove all listeners

            button.addEventListener('click', () => {
                // Remove 'active' from currently active button
                const currentActive = modal.querySelector('.crafting-category-btn.active');
                if (currentActive) {
                    currentActive.classList.remove('active');
                }
                // Add 'active' to clicked button
                button.classList.add('active');

                // Get the category and repopulate
                const category = button.dataset.category;
                // Store the current category
                this.currentCraftingBenchCategory = category;
                this.populateCraftingBenchList(category);

                // Clear the details pane when category changes
                detailsDiv.innerHTML = '<p>Select an item to see details.</p>';
            });
        });
        // Set the 'All' button as active initially (if not already done by default HTML)
        const allButton = modal.querySelector('.crafting-category-btn[data-category="all"]');
        if (allButton && !allButton.classList.contains('active')) {
             // Ensure 'active' is removed from others first
            modal.querySelectorAll('.crafting-category-btn.active').forEach(b => b.classList.remove('active'));
            allButton.classList.add('active');
        }
        // --- END Event Listeners ---

        this.focusCommandInput(); // Keep focus logic if needed
    },

    /**
     * Shows the details for a selected crafting bench item in the right column.
     * @param {string} itemId - The ID of the item to show details for.
     */
    showCraftingBenchItemDetails: function(itemId) {
        const detailsDiv = document.getElementById('crafting-bench-details');
        if (!detailsDiv) {
            console.error('Crafting bench details element not found!');
            return;
        }
        const allItems = World.items;
        const item = allItems[itemId];

        if (!detailsDiv || !item || !item.recipe) {
            detailsDiv.innerHTML = '<p>Select an item to see details.</p>';
            return;
        }

        // Skip items that are specifically for refinery or lumbermill
        if (item.craftableAt === 'refinery' || item.craftableAt === 'lumbermill') {
            detailsDiv.innerHTML = `<p>${item.name} must be crafted at a ${item.craftableAt}.</p>`;
            return;
        }

        // Skip items that are bars or planks
        if (item.category === 'bar' || item.category === 'plank') {
            detailsDiv.innerHTML = `<p>${item.name} cannot be crafted at a crafting bench.</p>`;
            return;
        }

        detailsDiv.innerHTML = ''; // Clear previous details

        // Create a container with the correct class for styling
        const container = document.createElement('div');
        container.classList.add('lumbermill-details-container');

        // Item Name
        const nameHeader = document.createElement('h3');
        nameHeader.textContent = item.name;
        container.appendChild(nameHeader);

        // Item Description
        const descriptionP = document.createElement('p');
        descriptionP.textContent = item.description;
        container.appendChild(descriptionP);

        // Recipe
        const recipeDiv = document.createElement('div');
        recipeDiv.classList.add('lumbermill-recipe');
        const recipeTitle = document.createElement('strong');
        recipeTitle.textContent = 'Requires:';
        recipeDiv.appendChild(recipeTitle);

        const ul = document.createElement('ul');
        let maxCraft = Infinity;
        for (const reqId in item.recipe) {
            const need = item.recipe[reqId];
            const reqItem = allItems[reqId];
            const inv = Player.inventory.find(i => i.id === reqId) || {quantity:0};
            const canMake = Math.floor(inv.quantity/need);
            maxCraft = Math.min(maxCraft, canMake);
            const li = document.createElement('li');
            const nameSpan = document.createElement('span');
            nameSpan.className = 'req-name';
            nameSpan.textContent = `${need} × ${reqItem ? reqItem.name : reqId}`;
            li.appendChild(nameSpan);

            const stockSpan = document.createElement('span');
            stockSpan.className = 'req-stock';
            stockSpan.textContent = `You have ${inv.quantity}`;
            li.appendChild(stockSpan);
            ul.appendChild(li);
        }
        recipeDiv.appendChild(ul);
        container.appendChild(recipeDiv);

        // Quantity slider
        const lbl = document.createElement('label');
        lbl.setAttribute('for', 'crafting-bench-quantity-slider');
        lbl.textContent = 'Quantity: ';
        container.appendChild(lbl);
        const sliderWrapper = document.createElement('div');
        sliderWrapper.classList.add('slider-wrapper');
        const slider = document.createElement('input');
        slider.type = 'range'; slider.id = 'crafting-bench-quantity-slider';
        slider.min = 1; slider.max = (maxCraft > 0 ? maxCraft : 1); slider.value = 1;
        if (maxCraft === 0) slider.disabled = true;
        const valSpan = document.createElement('span');
        valSpan.id = 'crafting-bench-quantity-value';
        valSpan.textContent = slider.value;
        slider.oninput = () => valSpan.textContent = slider.value;
        sliderWrapper.appendChild(slider);
        sliderWrapper.appendChild(valSpan);
        container.appendChild(sliderWrapper);

        // Craft button
        const btn = document.createElement('button');
        btn.id = 'crafting-bench-craft-btn';
        btn.textContent = 'Craft';
        if (maxCraft === 0) btn.disabled = true;
        btn.onclick = () => UI.craftCraftingBenchItem(itemId, parseInt(slider.value));
        container.appendChild(btn);

        // Append the styled container to the main details div
        detailsDiv.appendChild(container);
    },

    /**
     * Handle crafting of a crafting bench item
     */
    craftCraftingBenchItem: function(itemId, quantity = 1) {
        const def = World.items[itemId];
        if (!def || !def.recipe) {
            Game.addMessage('Invalid crafting recipe.', 'error'); return;
        }
        // Skip items that are specifically for refinery or lumbermill
        if (def.craftableAt === 'refinery' || def.craftableAt === 'lumbermill') {
            Game.addMessage(`${def.name} must be crafted at a ${def.craftableAt}.`, 'error'); return;
        }
        // Skip items that are bars or planks
        if (def.category === 'bar' || def.category === 'plank') {
            Game.addMessage(`${def.name} cannot be crafted at a crafting bench.`, 'error'); return;
        }
        // verify materials
        for (const reqId in def.recipe) {
            const needTotal = def.recipe[reqId]*quantity;
            if (!Player.hasItem(reqId, needTotal)) {
                Game.addMessage(`Not enough ${World.items[reqId]?.name || reqId}.`, 'error'); return;
            }
        }
        // consume
        for (const reqId in def.recipe) {
            const need = def.recipe[reqId]*quantity;
            Player.removeItem(reqId, need);
        }
        // produce
        Player.addItem(def, quantity);
        Game.addMessage(`Crafted ${quantity} x ${def.name}.`, 'success');
        // refresh UI with the current category
        UI.populateCraftingBenchList(UI.currentCraftingBenchCategory); // Refresh crafting bench list with current category
        UI.showCraftingBenchItemDetails(itemId); // Refresh crafting bench details
        if (typeof Inventory !== 'undefined' && typeof Inventory.updateDisplay === 'function') Inventory.updateDisplay(); // Update Inventory UI
        Player.save(); // Save player data after crafting
    },
};
