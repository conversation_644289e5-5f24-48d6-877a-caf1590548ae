/**
 * Server for the DawnSword game
 * Handles serving the game files and saving/loading game data
 */

const http = require('http');
const https = require('https'); // Added for API calls
const fs = require('fs');
const path = require('path');
const url = require('url');
const crypto = require('crypto'); // Added for generating session IDs
const sqlite3 = require('sqlite3').verbose(); // Added for SQLite
const bcrypt = require('bcrypt'); // Added for password hashing
const express = require('express'); // Added for Express routes
const { Server } = require("socket.io"); // Added for Socket.IO
const ServerNpcAI = require('./server-npc-ai'); // Server-side NPC AI system
const marketDb = require('./js/marketDb'); // Market database functions
const migrationManager = require('./migrations/migrationManager'); // Database migration manager
const QuestManager = require('./server/QuestManager'); // Server-side Quest Manager
const setupGuildsDatabase = require('./js/setup_guilds_db'); // Guilds DB Setup
const guildRoutes = require('./routes/guildRoutes'); // Guild API routes

// --- START: Load Admin Usernames ---
let adminUsernames = [];

function loadAdminConfig() {
    // Define potential paths relative to the script's directory (__dirname)
    // Path 1: Assuming script is in httpdocs/dd, config is sibling to httpdocs
    const path1 = path.join(__dirname, '../../config/admin.json');
    // Path 2: Assuming script is in httpdocs, config is sibling to httpdocs
    const path2 = path.join(__dirname, '../config/admin.json');
    // Path 3: Assuming script is in home, config is sibling to script
    const path3 = path.join(__dirname, 'config/admin.json');

    const pathsToTry = [path1, path2, path3];
    let foundPath = null;

    console.log(`[Admin Load] Script directory (__dirname): ${__dirname}`);

    for (const p of pathsToTry) {
        console.log(`[Admin Load] Checking path: ${p}`);
        try {
            if (fs.existsSync(p)) {
                console.log(`[Admin Load] File found at: ${p}`);
                foundPath = p;
                break; // Stop checking once found
            } else {
                 console.log(`[Admin Load] File NOT found at: ${p}`);
            }
        } catch (err) {
            // Log existence check errors but continue trying other paths
            console.error(`[Admin Load] Error checking existence of ${p}:`, err.message);
        }
    }

    if (foundPath) {
        try {
            const adminData = fs.readFileSync(foundPath, 'utf8');
            adminUsernames = JSON.parse(adminData);
            console.log("[Admin Load] Successfully loaded and parsed admin usernames:", adminUsernames);
        } catch (error) {
            console.error(`[Admin Load] Error reading or parsing admin file at ${foundPath}:`, error);
            adminUsernames = []; // Reset on error
        }
    } else {
        console.warn("[Admin Load] Admin file ('admin.json') not found in any expected location. No users will have admin privileges.");
        adminUsernames = [];
    }
}

loadAdminConfig(); // Call the function to load admins on startup
// --- END: Load Admin Usernames ---

// Port to listen on
const PORT = process.env.PORT || 3001; // Use environment variable or fallback
const DB_PATH = path.join(__dirname, 'data', 'dawnsword.db'); // Main DB for users, saves, structures
const DB_DYNAMIC_PATH = path.join(__dirname, 'data', 'dawnsword_dynamic.db'); // DB for generated objects
const SALT_ROUNDS = 10; // Cost factor for bcrypt hashing
const XAI_API_KEY = process.env.XAI_API_KEY; // Read API key from environment
const CHAT_HISTORY_LIMIT = 50; // Number of recent messages to load

// MIME types for different file extensions
const MIME_TYPES = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'text/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
};

// --- Server State ---
let db; // Connection for main DB
let dbDynamic; // Connection for dynamic object DB
let dbActivities; // Connection for activities/quests DB
const activeSessions = {}; // Simple in-memory session store { sessionId: userId }
const connectedPlayers = {}; // Store connected player data { socketId: { userId, username, x, y, zoneId, isAdmin } }
const activeUserSockets = {}; // Track active socket connections by userId { userId: socketId }

// Server messages configuration
let serverMessages = {
    enabled: true,
    intervalMinutes: 10,
    messages: [
        "Welcome to DawnSword! Join our Discord server for updates and community discussions."
    ]
};
let serverMessageInterval = null;



// Rate limiting
const rateLimits = {
    player_move: { maxPerSecond: 10, windowMs: 1000 },
    send_chat_message: { maxPerSecond: 3, windowMs: 1000 },
    request_chat_history: { maxPerSecond: 1, windowMs: 5000 },
    request_player_list: { maxPerSecond: 1, windowMs: 5000 }
};

// Store rate limit data for each socket
const socketRateLimits = {};

/**
 * Broadcast the current player count to all connected clients
 */
function broadcastPlayerCount() {
    const playerCount = Object.keys(connectedPlayers).length;
    io.emit('player_count_update', { count: playerCount });
}

/**
 * Load server messages configuration from file
 */
function loadServerMessages() {
    try {
        const messagesFile = path.join(__dirname, 'config', 'server_messages.json');
        if (fs.existsSync(messagesFile)) {
            const messagesContent = fs.readFileSync(messagesFile, 'utf8');
            serverMessages = JSON.parse(messagesContent);
            console.log('Server messages configuration loaded successfully.');

            // Start or restart the server message interval
            startServerMessageInterval();
        } else {
            console.log('Server messages configuration file not found. Using defaults.');
        }
    } catch (error) {
        console.error('Error loading server messages configuration:', error);
    }
}

/**
 * Start or restart the server message interval
 */
function startServerMessageInterval() {
    // Clear any existing interval
    if (serverMessageInterval) {
        clearInterval(serverMessageInterval);
    }

    // If server messages are disabled, don't start the interval
    if (!serverMessages.enabled || !serverMessages.messages || serverMessages.messages.length === 0) {
        console.log('Server messages are disabled or no messages configured.');
        return;
    }

    // Convert minutes to milliseconds
    const intervalMs = (serverMessages.intervalMinutes || 10) * 60 * 1000;

    // Log to server console that the message system is initialized
    console.log(`Server message system initialized. Messages will be sent every ${serverMessages.intervalMinutes} minutes.`);

    // Set up the interval to send server messages
    serverMessageInterval = setInterval(() => {
        sendRandomServerMessage();
    }, intervalMs);
}

/**
 * Send a specific server message to all connected clients
 * @param {string} message - The message to send
 */
function sendServerMessage(message) {
    if (!serverMessages.enabled) {
        return;
    }

    // Check if the message contains links
    const hasLinks = message.includes('[link:');

    // Log the exact message being sent for debugging
    console.log('Sending server message:', {
        message: message,
        hasLinks: hasLinks,
        containsLinkText: message.includes('[link:')
    });

    // Send the message to all connected clients
    io.emit('server_message', {
        message: message,
        color: '#9370DB', // Medium purple color that fits with your game
        hasLinks: hasLinks
    });

    console.log(`Server message sent: ${message}${hasLinks ? ' (contains links)' : ''}`);
}

/**
 * Send a random server message to all connected clients
 */
function sendRandomServerMessage() {
    if (!serverMessages.enabled || !serverMessages.messages || serverMessages.messages.length === 0) {
        return;
    }

    // Select a random message from the configured messages
    const randomIndex = Math.floor(Math.random() * serverMessages.messages.length);
    const message = serverMessages.messages[randomIndex];

    // Send the message using the common function
    sendServerMessage(message);
}

/**
 * Check if a socket event is rate limited
 * @param {string} socketId - The socket ID
 * @param {string} eventName - The event name
 * @returns {boolean} - True if rate limited, false otherwise
 */
function isRateLimited(socketId, eventName) {
    // If no rate limit defined for this event, allow it
    if (!rateLimits[eventName]) {
        return false;
    }

    // Initialize rate limit data for this socket if needed
    if (!socketRateLimits[socketId]) {
        socketRateLimits[socketId] = {};
    }

    // Initialize rate limit data for this event if needed
    if (!socketRateLimits[socketId][eventName]) {
        socketRateLimits[socketId][eventName] = {
            count: 0,
            resetTime: Date.now() + rateLimits[eventName].windowMs
        };
    }

    const now = Date.now();
    const limit = socketRateLimits[socketId][eventName];

    // Reset count if window has passed
    if (now > limit.resetTime) {
        limit.count = 0;
        limit.resetTime = now + rateLimits[eventName].windowMs;
    }

    // Check if rate limited
    if (limit.count >= rateLimits[eventName].maxPerSecond) {
        return true;
    }

    // Increment count and allow
    limit.count++;
    return false;
}

/**
 * Clean up rate limit data for disconnected sockets
 */
function cleanupRateLimits() {
    for (const socketId in socketRateLimits) {
        if (!connectedPlayers[socketId]) {
            delete socketRateLimits[socketId];
        }
    }

    // Schedule next cleanup
    setTimeout(cleanupRateLimits, 60000); // Clean up every minute
}

// Start the rate limit cleanup process
setTimeout(cleanupRateLimits, 60000);

// --- Load Static Definitions ---
let oreDefinitions = {};
let treeDefinitions = {};
let animalDefinitions = {};
let mobDefinitions = {};

function loadDefinitions() {
    try {
        oreDefinitions = JSON.parse(fs.readFileSync(path.join(__dirname, 'data', 'ores', 'ores.json'), 'utf8'));
        treeDefinitions = JSON.parse(fs.readFileSync(path.join(__dirname, 'data', 'flora', 'trees.json'), 'utf8'));
        animalDefinitions = JSON.parse(fs.readFileSync(path.join(__dirname, 'data', 'fauna', 'animals.json'), 'utf8'));
        mobDefinitions = JSON.parse(fs.readFileSync(path.join(__dirname, 'data', 'mobs', 'mobs.json'), 'utf8'));
    } catch (err) {
        console.error("FATAL: Error loading static definitions:", err);
        // Consider exiting if definitions are critical
        process.exit(1);
    }
}
// --- End Load Static Definitions ---


// --- Database Initialization ---
function initDatabase() {
    const dataDir = path.dirname(DB_PATH);
    if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
    }

    db = new sqlite3.Database(DB_PATH, (err) => {
        if (err) {
            console.error("Error opening database:", err.message);
        } else {
            // Use serialize to ensure tables are created sequentially
            db.serialize(() => {
                // Create users table if it doesn't exist
                db.run(`CREATE TABLE IF NOT EXISTS users (
                    userId INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    passwordHash TEXT NOT NULL,
                    gold INTEGER DEFAULT 0,
                    inventory TEXT DEFAULT '[]',
                    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )`);

                // Create player_data table if it doesn't exist
                db.run(`CREATE TABLE IF NOT EXISTS player_data (
                    userId INTEGER PRIMARY KEY,
                    playerStateJSON TEXT,
                    worldStateJSON TEXT, -- Might store minimal world state relevant to player
                    lastSavedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (userId) REFERENCES users (userId)
                )`);

                // Create chat_messages table
                db.run(`CREATE TABLE IF NOT EXISTS chat_messages (
                    messageId INTEGER PRIMARY KEY AUTOINCREMENT,
                    userId INTEGER NOT NULL,
                    username TEXT NOT NULL,
                    zoneId TEXT NOT NULL,
                    message TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (userId) REFERENCES users (userId)
                )`, (err) => {
                    if (err) {
                        console.error("Error creating chat_messages table:", err.message);
                    } else {
                        console.log("Chat_messages table checked/created.");
                        // --- START: Add message_type column to chat_messages if it doesn't exist ---
                        db.all("PRAGMA table_info(chat_messages)", (pragmaErr, columns) => {
                            if (pragmaErr) {
                                console.error("Error checking chat_messages table schema:", pragmaErr.message);
                                return;
                            }

                            const columnExists = columns.some(column => column.name === 'message_type');

                            if (!columnExists) {
                                console.log("Schema Update: Adding 'message_type' column to 'chat_messages' table...");
                                db.run("ALTER TABLE chat_messages ADD COLUMN message_type TEXT DEFAULT 'world'", (alterErr) => {
                                    if (alterErr) {
                                        console.error("Schema Update Error: Error adding message_type column:", alterErr.message);
                                    } else {
                                        console.log("Schema Update Success: 'message_type' column added to chat_messages. Existing messages defaulted to 'world'.");
                                    }
                                });
                            } else {
                                console.log("Schema Info: 'message_type' column already exists in 'chat_messages' table.");
                            }
                        });
                        // --- END: Add message_type column ---
                    }
                });
            });
        }
    });

    // Connect to the dynamic database
    dbDynamic = new sqlite3.Database(DB_DYNAMIC_PATH, (err) => {
        if (err) {
            console.error(`Error opening dynamic database at ${DB_DYNAMIC_PATH}:`, err.message);
            // Decide if this is fatal. Maybe the server can run without dynamic objects?
            // For now, let's log the error and continue.
        }
    });

    // Connect to the activities database
    const DB_ACTIVITIES_PATH = path.join(__dirname, 'data', 'dawnsword_activities.db');
    dbActivities = new sqlite3.Database(DB_ACTIVITIES_PATH, (err) => {
        if (err) {
            console.error(`Error opening activities database at ${DB_ACTIVITIES_PATH}:`, err.message);
            // Log error but continue - the server can run without activities tracking
        } else {
            // Verify the tables exist
            dbActivities.get("SELECT name FROM sqlite_master WHERE type='table' AND name='player_activities'", (err, table) => {
                if (err) {
                    console.error("Error checking activities tables:", err);
                } else if (!table) {
                    console.error("CRITICAL: player_activities table does not exist! Activities will not be recorded.");
                    console.error("Please run 'node setup_activities_db_new.js' to create the tables.");
                }
            });
        }
    });
}
// --- End Database Initialization ---


// Create the HTTP server
const server = http.createServer((req, res) => {
    // Parse the URL
    const parsedUrl = url.parse(req.url, true);
    let pathname = parsedUrl.pathname;

    // Handle API requests
    if (pathname.startsWith('/api/')) {
        // Delegate to Express app if it's a guild route
        if (pathname.startsWith('/api/guilds')) {
            // Temporarily attach activeSessions to req for guildRoutes if needed for session check
            // This is a workaround. Ideally, session management would be more integrated.
            req.activeSessions = activeSessions; // Make activeSessions available to guildRoutes
            return expressApp(req, res);
        }
        handleApiRequest(req, res, pathname);
        return;
    }

    // Normalize the pathname
    pathname = pathname === '/' ? '/index.html' : pathname;

    // Get the file path
    const filePath = path.join(__dirname, pathname);

    // Get the file extension
    const ext = path.extname(filePath);

    // Check if the file exists
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // File not found
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('404 Not Found');
            return;
        }

        // Read the file
        fs.readFile(filePath, (err, data) => {
            if (err) {
                // Error reading file
                res.writeHead(500, { 'Content-Type': 'text/plain' });
                res.end('500 Internal Server Error');
                return;
            }

            // Set the content type
            const contentType = MIME_TYPES[ext] || 'application/octet-stream';

            // Add Cache-Control header to prevent aggressive caching
            const headers = {
                'Content-Type': contentType,
                'Cache-Control': 'no-cache' // Instructs browser to revalidate before using cache
            };

            // Send the response
            res.writeHead(200, headers);
            res.end(data);
        });
    });
});

/**
 * Handle API requests
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 * @param {string} pathname - The pathname
 */
 function handleApiRequest(req, res, pathname) {
     // Set CORS headers
     res.setHeader('Access-Control-Allow-Origin', req.headers.origin || '*');
     res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
     res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
     res.setHeader('Access-Control-Allow-Credentials', 'true');

    // Handle OPTIONS requests (for CORS)
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // Check for dynamic zone objects route FIRST
    const zoneObjectsMatch = pathname.match(/^\/api\/zone_objects\/([a-zA-Z0-9_]+)$/);
    if (zoneObjectsMatch && req.method === 'GET') {
        const zoneId = zoneObjectsMatch[1];
        handleGetZoneObjectsRequest(req, res, zoneId);
        return; // Exit after handling
    }

    // Check for market listing detail route
    const marketListingMatch = pathname.match(/^\/api\/market\/listing\/([a-zA-Z0-9_-]+)$/);
    if (marketListingMatch && req.method === 'GET') {
        handleMarketListingRequest(req, res);
        return; // Exit after handling
    }

    // Check for horse by ID route - allow more complex IDs with timestamps
    const horseByIdMatch = pathname.match(/^\/api\/horses\/([a-zA-Z0-9_-]+)$/);
    if (horseByIdMatch && req.method === 'GET' && pathname !== '/api/horses/types' && pathname !== '/api/horses/colors') {
        const horseId = horseByIdMatch[1];
        console.log(`[API] Matched horse by ID route: ${horseId}`);
        handleHorseByIdRequest(req, res, horseId);
        return; // Exit after handling
    }

    // Check for stable horses route
    const stableHorsesMatch = pathname.match(/^\/api\/horses\/stable\/([a-zA-Z0-9_-]+)$/);
    if (stableHorsesMatch && req.method === 'GET') {
        const stableId = stableHorsesMatch[1];
        handleStableHorsesRequest(req, res, stableId);
        return; // Exit after handling
    }

    // Handle specific, non-dynamic API endpoints
    switch (pathname) {
        case '/api/save':
            handleSaveRequest(req, res);
            return; // Exit after handling

        case '/api/load':
            handleLoadRequest(req, res);
            return; // Exit after handling

        case '/api/npc-chat':
             handleNpcChatRequest(req, res);
             return; // Exit after handling

        case '/api/quests':
            handleQuestsRequest(req, res);
            return; // Exit after handling

        case '/api/quests/start':
            handleStartQuestRequest(req, res);
            return; // Exit after handling

         // REMOVED: '/api/save_zones' case (No longer used)

         case '/api/update_zone_structures': // <-- ADDED CASE FOR NEW ENDPOINT
             handleUpdateZoneStructuresRequest(req, res);
             return; // Exit after handling

         case '/api/save_npcs':
            handleSaveNpcsRequest(req, res);
            return; // Exit after handling

        case '/api/save_items':
            handleSaveItemsRequest(req, res);
            return; // Exit after handling

        case '/api/save_mobs':
            handleSaveMobsRequest(req, res);
            return; // Exit after handling

        case '/api/save_plants':
            handleSavePlantsRequest(req, res);
            return; // Exit after handling

        case '/api/save_trees':
            handleSaveTreesRequest(req, res);
            return; // Exit after handling

        case '/api/save_animals':
            handleSaveAnimalsRequest(req, res);
            return; // Exit after handling

        case '/api/register':
            handleRegisterRequest(req, res);
            return; // Exit after handling

        case '/api/login':
            handleLoginRequest(req, res);
            return; // Exit after handling

        case '/api/update_object_state':
            handleUpdateObjectStateRequest(req, res);
            return; // Exit after handling

        case '/api/save_zone_definitions': // <-- ADDED CASE FOR DIRECT JSON SAVE
            handleSaveZoneDefinitionsRequest(req, res);
            return; // Exit after handling

        case '/api/check_admin': // <-- ADDED CASE FOR ADMIN CHECK
            handleCheckAdminRequest(req, res);
            return; // Exit after handling

        case '/api/get_leaderboard': // <-- ADDED CASE FOR LEADERBOARD
            handleGetLeaderboardRequest(req, res);
            return; // Exit after handling

        // Market API routes
        case '/api/market/listings':
            handleMarketListingsRequest(req, res);
            return; // Exit after handling

        case '/api/market/my-listings':
            handleMyListingsRequest(req, res);
            return; // Exit after handling

        case '/api/market/create-listing':
            handleCreateListingRequest(req, res);
            return; // Exit after handling

        case '/api/market/buy':
            handleBuyListingRequest(req, res);
            return; // Exit after handling

        case '/api/market/cancel-listing':
            handleCancelListingRequest(req, res);
            return; // Exit after handling

        case '/api/market/quick-sell':
            handleQuickSellRequest(req, res);
            return; // Exit after handling

        case '/api/market/transactions':
            handleMarketTransactionsRequest(req, res);
            return; // Exit after handling

        // Activity tracking API routes
        case '/api/activity/record':
            handleRecordActivityRequest(req, res);
            return; // Exit after handling

        case '/api/activity/status':
            handleActivityStatusRequest(req, res);
            return; // Exit after handling

        case '/api/activity/history':
            handleActivityHistoryRequest(req, res);
            return; // Exit after handling

        // Debug endpoint removed for security reasons

        // Horse API routes
        case '/api/horses':
            handleHorsesRequest(req, res);
            return; // Exit after handling

        case '/api/horses/purchase':
            handleHorsePurchaseRequest(req, res);
            return; // Exit after handling

        case '/api/horses/player/current':
            handlePlayerHorseRequest(req, res);
            return; // Exit after handling

        case '/api/horses/player/rename':
            handleRenameHorseRequest(req, res);
            return; // Exit after handling

        case '/api/horses/player/update-stats':
            handleUpdateHorseStatsRequest(req, res);
            return; // Exit after handling

        case '/api/horses/player/all':
            handlePlayerHorsesRequest(req, res);
            return; // Exit after handling

        case '/api/horses/player/set-active':
            handleSetActiveHorseRequest(req, res);
            return; // Exit after handling

        // Stable API routes
        case '/api/horses/stable/store':
            handleStoreHorseRequest(req, res);
            return; // Exit after handling

        case '/api/horses/stable/activate':
            handleActivateHorseRequest(req, res);
            return; // Exit after handling

        case '/api/horses/stable/sell':
            handleSellHorseRequest(req, res);
            return; // Exit after handling

        case '/api/horses/colors':
            handleHorseColorsRequest(req, res);
            return; // Exit after handling

        case '/api/horses/types':
            handleHorseTypesRequest(req, res);
            return; // Exit after handling

        default:
            // If no route matched, return 404
            res.writeHead(404, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'API endpoint not found' }));
            break; // Break from switch
    }
 }

/**
 * Handle requests to update the state of a dynamic object (ore, plant, mob, etc.)
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleUpdateObjectStateRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { zoneId, objectId, type, newState } = JSON.parse(body);

            if (!zoneId || !objectId || !type || !newState || typeof newState !== 'object') {
                throw new Error("Missing or invalid data for object state update.");
            }

            // Determine the table name based on the object type
            const validTypes = ['ores', 'plants', 'trees', 'animals', 'mobs']; // Add other types as needed
            if (!validTypes.includes(type)) {
                throw new Error(`Invalid object type specified: ${type}`);
            }
            const tableName = `zone_${type}`;

            // Construct the SET part of the SQL query dynamically
            const setClauses = [];
            const params = [];
            for (const key in newState) {
                if (newState.hasOwnProperty(key)) {
                    // Basic validation/sanitization of key names (prevent SQL injection)
                    if (!/^[a-zA-Z0-9_]+$/.test(key)) {
                        continue; // Skip potentially harmful keys
                    }
                    // Convert boolean true/false to 1/0 for SQLite
                    let value = newState[key];
                    if (typeof value === 'boolean') {
                        value = value ? 1 : 0;
                    }
                    // Special handling for field name mappings between client and database
                    if ((type === 'ores' || type === 'trees') && key === 'harvested') {
                        setClauses.push(`last_harvested = ?`);
                    } else if (type === 'mobs' && key === 'defeated') {
                        setClauses.push(`last_killed = ?`);
                    } else {
                        setClauses.push(`${key} = ?`);
                    }
                    params.push(value);
                }
            }

            if (setClauses.length === 0) {
                throw new Error("No valid state updates provided in newState.");
            }

            // Add the objectId to the parameters for the WHERE clause
            params.push(objectId);

            const sql = `UPDATE ${tableName} SET ${setClauses.join(', ')} WHERE id = ?`;

            // Use the dynamic database connection
            if (!dbDynamic) {
                 res.writeHead(500, { 'Content-Type': 'application/json' });
                 res.end(JSON.stringify({ error: 'Dynamic database connection error.' }));
                 return;
            }

            dbDynamic.run(sql, params, function(err) {
                if (err) {
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Database error updating object state.' }));
                } else if (this.changes === 0) {
                    // No rows were updated, likely means the objectId wasn't found
                    res.writeHead(404, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: `Object not found.` }));
                } else {
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: true }));
                }
            });

        } catch (error) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data' }));
        }
    });
}

/**
 * Handle requests to save the entire zone definitions object directly to JSON.
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleSaveZoneDefinitionsRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            // Parse the JSON data (expecting the entire zones object)
            const zonesData = JSON.parse(body);

            // Define the file path
            const zonesFile = path.join(__dirname, 'data', 'places', 'zone_definitions.json');

            // Ensure the directory exists
            const zonesDir = path.dirname(zonesFile);
            if (!fs.existsSync(zonesDir)) {
                fs.mkdirSync(zonesDir, { recursive: true });
            }

            // Save the zones data to the file, overwriting existing content
            fs.writeFileSync(zonesFile, JSON.stringify(zonesData, null, 2)); // Pretty print with 2 spaces
            console.log(`Zone definitions saved directly to ${zonesFile}`);

            // Send the response
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true }));
        } catch (error) {
            // Error parsing JSON or saving data
            res.writeHead(400, { 'Content-Type': 'application/json' });
            console.error("Save Zone Definitions Error:", error.message);
            res.end(JSON.stringify({ error: 'Invalid JSON data or file write error' }));
        }
    });
}

/**
 * Handle save requests
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
 function handleSaveRequest(req, res) { // Saves combined player and world state
     // Only accept POST requests
     if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get the request body
    let body = '';

    req.on('data', (chunk) => {
        body += chunk.toString();
    });

    req.on('end', () => {
        try {
            // Parse the JSON data (expecting { sessionId, player: {...}, world: {...} })
            const gameState = JSON.parse(body);

            const { sessionId, player, world } = gameState;
            if (!sessionId || !player || !world) {
                throw new Error('Missing sessionId, player, or world in save request.');
            }

            const userId = activeSessions[sessionId];
            if (!userId) {
                res.writeHead(401, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Invalid or expired session. Please log in again.' }));
                return;
            }

            // Save player and world state as JSON strings in the database
            const playerStateJSON = JSON.stringify(player);
            const worldStateJSON = JSON.stringify(world);

            db.run(
                `INSERT INTO player_data (userId, playerStateJSON, worldStateJSON, lastSavedAt)
                 VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                 ON CONFLICT(userId) DO UPDATE SET
                    playerStateJSON=excluded.playerStateJSON,
                    worldStateJSON=excluded.worldStateJSON,
                    lastSavedAt=CURRENT_TIMESTAMP`,
                [userId, playerStateJSON, worldStateJSON],
                function (err) {
                    if (err) {
                        console.error("Error saving player data:", err.message);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: 'Database error saving game state.' }));
                    } else {
                        console.log(`Game state saved for userId: ${userId}`);
                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: true }));
                    }
                }
            );
        } catch (error) {
            // Error parsing JSON or saving data
            res.writeHead(400, { 'Content-Type': 'application/json' });
            console.error("Save Error:", error.message);
            res.end(JSON.stringify({ error: 'Invalid JSON data or save error' }));
        }
     });
 }


 /**
  * Handle requests to check if the current user is an admin.
  * @param {http.IncomingMessage} req - The request object
  * @param {http.ServerResponse} res - The response object
  */
 function handleCheckAdminRequest(req, res) {
     if (req.method !== 'POST') {
         res.writeHead(405, { 'Content-Type': 'application/json' });
         res.end(JSON.stringify({ error: 'Method not allowed' }));
         return;
     }

     let body = '';
     req.on('data', chunk => { body += chunk.toString(); });
     req.on('end', () => {
         try {
             const { sessionId } = JSON.parse(body);

             if (!sessionId) {
                 return res.writeHead(400, { 'Content-Type': 'application/json' })
                           .end(JSON.stringify({ error: 'Missing sessionId.' }));
             }

             const userId = activeSessions[sessionId];
             if (!userId) {
                 // Invalid or expired session
                 return res.writeHead(401, { 'Content-Type': 'application/json' })
                           .end(JSON.stringify({ error: 'Invalid session.', isAdmin: false }));
             }

             // Fetch username to check against the admin list
             db.get('SELECT username FROM users WHERE userId = ?', [userId], (err, user) => {
                 if (err) {
                     console.error("Database error checking admin status:", err.message);
                     return res.writeHead(500, { 'Content-Type': 'application/json' })
                               .end(JSON.stringify({ error: 'Database error.', isAdmin: false }));
                 }
                 if (!user) {
                     // Should not happen if session is valid, but handle defensively
                     return res.writeHead(404, { 'Content-Type': 'application/json' })
                               .end(JSON.stringify({ error: 'User not found.', isAdmin: false }));
                 }

                 // Check if the username is in the loaded admin list
                 const isAdmin = adminUsernames.includes(user.username);
                 console.log(`Admin check for user ${user.username} (ID: ${userId}): ${isAdmin}`);

                 res.writeHead(200, { 'Content-Type': 'application/json' });
                 res.end(JSON.stringify({ success: true, isAdmin: isAdmin }));
             });

         } catch (error) {
             console.error("Check Admin Error:", error.message);
             res.writeHead(400, { 'Content-Type': 'application/json' });
             res.end(JSON.stringify({ error: 'Invalid request data.', isAdmin: false }));
         }
     });
 }

/**
 * Handle getting leaderboard data
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleGetLeaderboardRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { sessionId } = JSON.parse(body);

            if (!sessionId) {
                return res.writeHead(400, { 'Content-Type': 'application/json' })
                          .end(JSON.stringify({ error: 'Missing sessionId.' }));
            }

            const userId = activeSessions[sessionId];
            if (!userId) {
                // Invalid or expired session
                return res.writeHead(401, { 'Content-Type': 'application/json' })
                          .end(JSON.stringify({ error: 'Invalid session.' }));
            }

            // Get the current user's username for highlighting in the leaderboard
            db.get('SELECT username FROM users WHERE userId = ?', [userId], (err, currentUser) => {
                if (err) {
                    console.error("Database error fetching current user:", err.message);
                    return res.writeHead(500, { 'Content-Type': 'application/json' })
                              .end(JSON.stringify({ error: 'Database error.' }));
                }

                // Query to get all users with their level information
                const query = `
                    SELECT u.userId, u.username,
                           json_extract(p.playerStateJSON, '$.level') as level
                    FROM users u
                    LEFT JOIN player_data p ON u.userId = p.userId
                    ORDER BY level DESC, u.username ASC
                `;

                db.all(query, [], (err, users) => {
                    if (err) {
                        console.error("Database error fetching leaderboard data:", err.message);
                        return res.writeHead(500, { 'Content-Type': 'application/json' })
                                  .end(JSON.stringify({ error: 'Database error fetching leaderboard data.' }));
                    }

                    // Process the results to handle null levels (users who haven't played yet)
                    const processedUsers = users.map(user => ({
                        userId: user.userId,
                        username: user.username,
                        level: user.level || 1, // Default to level 1 if null
                        isCurrentUser: user.username === currentUser.username
                    }));

                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        success: true,
                        users: processedUsers,
                        currentUsername: currentUser.username
                    }));
                });
            });
        } catch (error) {
            console.error("Leaderboard Error:", error.message);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data.' }));
        }
    });
}


 /**
  * Handle load requests
  * @param {http.IncomingMessage} req - The request object
  * @param {http.ServerResponse} res - The response object
  */
 function handleLoadRequest(req, res) { // Loads combined player and world state
     // Only accept POST requests (so we can send sessionId in body)
     if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', (chunk) => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            // Parse the JSON data (expecting { sessionId })
            const { sessionId } = JSON.parse(body);

            if (!sessionId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Missing sessionId in load request.' }));
                return;
            }

            const userId = activeSessions[sessionId];
            if (!userId) {
                res.writeHead(401, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Invalid or expired session. Please log in again.' }));
                return;
            }

            db.get(
                'SELECT playerStateJSON, worldStateJSON FROM player_data WHERE userId = ?',
                [userId],
                (err, row) => {
                    if (err) {
                        console.error("Error loading player data:", err.message);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: 'Database error loading game state.' }));
                    } else if (!row) {
                        // No save found for this user
                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ player: null, world: null }));
                    } else {
                        // Return the saved player and world state
                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({
                            player: JSON.parse(row.playerStateJSON),
                            world: JSON.parse(row.worldStateJSON)
                        }));
                    }
                }
            );
        } catch (error) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            console.error("Load Error:", error.message);
            res.end(JSON.stringify({ error: 'Invalid JSON data or load error' }));
        }
     });
 }

 // REMOVED: handleSaveZonesRequest function (No longer used)

 /**
  * Handle requests to update only the structures for a specific zone in zone_definitions.json.
  * @param {http.IncomingMessage} req - The request object
  * @param {http.ServerResponse} res - The response object
  */
 function handleUpdateZoneStructuresRequest(req, res) {
     if (req.method !== 'POST') {
         res.writeHead(405, { 'Content-Type': 'application/json' });
         res.end(JSON.stringify({ error: 'Method not allowed' }));
         return;
     }

     let body = '';
     req.on('data', chunk => { body += chunk.toString(); });
     req.on('end', () => {
         try {
             const { zoneId, structures } = JSON.parse(body);

             if (!zoneId || !Array.isArray(structures)) {
                 throw new Error("Missing or invalid zoneId or structures array in request.");
             }

             const zonesFile = path.join(__dirname, 'data', 'places', 'zone_definitions.json');
             let allZoneData = {};

             // Read the existing file
             if (fs.existsSync(zonesFile)) {
                 const fileContent = fs.readFileSync(zonesFile, 'utf8');
                 allZoneData = JSON.parse(fileContent);
             } else {
                 // Should not happen if the game is running, but handle defensively
                 throw new Error("zone_definitions.json not found.");
             }

             // Find the specific zone and update its structures
             if (allZoneData[zoneId] && allZoneData[zoneId].objects) {
                 console.log(`Updating structures for zone: ${zoneId}. Old count: ${allZoneData[zoneId].objects.structures?.length || 0}, New count: ${structures.length}`);
                 // Ensure the objects property exists
                 if (!allZoneData[zoneId].objects) {
                     allZoneData[zoneId].objects = {};
                 }
                 allZoneData[zoneId].objects.structures = structures; // Replace the structures array
             } else {
                 throw new Error(`Zone with ID ${zoneId} not found or missing 'objects' property in zone_definitions.json.`);
             }

             // Write the modified data back to the file
             fs.writeFileSync(zonesFile, JSON.stringify(allZoneData, null, 2)); // Pretty print
             console.log(`Successfully updated structures for zone ${zoneId} in ${zonesFile}`);

             res.writeHead(200, { 'Content-Type': 'application/json' });
             res.end(JSON.stringify({ success: true }));

         } catch (error) {
             console.error("Update Zone Structures Error:", error.message);
             res.writeHead(400, { 'Content-Type': 'application/json' });
             res.end(JSON.stringify({ error: `Invalid request data or processing error: ${error.message}` }));
         }
     });
 }


 /**
  * Handle requests to get all dynamic objects for a specific zone from the database.
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 * @param {string} zoneId - The ID of the zone to fetch objects for
 */
async function handleGetZoneObjectsRequest(_req, res, zoneId) {
    // Method check already done in handleApiRequest for this specific route pattern
    console.log(`Fetching dynamic objects for zone: ${zoneId}`);

    // Define which objects are fetched from the dynamic DB
    const dynamicDbTypes = ['ores', 'trees', 'animals', 'mobs'];
    // Structures are loaded client-side from world_zones.json

    const promises = dynamicDbTypes.map(type => { // Only map dynamic types
        return new Promise((resolve, _reject) => {
            const tableName = `zone_${type}`;
            // Use the dynamic database connection
            const targetDb = dbDynamic;

            // Check if the dynamic database connection is valid
            if (!targetDb) {
                console.error(`Dynamic database connection (dbDynamic) is not available.`);
                return resolve({ type, data: [] }); // Resolve empty if DB connection failed
            }

            const sql = `SELECT * FROM ${tableName} WHERE zone_id = ?`;

            // Add extra debugging for ores
            if (type === 'ores') {
                // console.log(`[DEBUG] SQL query for ores: ${sql} with zoneId = ${zoneId}`);
            }

            // Use the dynamic database connection
            targetDb.all(sql, [zoneId], (err, rows) => {
                if (err) {
                    console.error(`Error fetching ${type} from dynamic DB for zone ${zoneId}:`, err.message);
                    // Resolve with empty array on error for this type, but log it
                    resolve({ type, data: [] });
                } else {
                    // --- DEBUGGING ---
                    if (type === 'ores') {
                        // console.log(`[handleGetZoneObjectsRequest] Fetched ${rows.length} ores for zone ${zoneId}:`, rows);
                    }
                    // --- END DEBUGGING ---
                        // Process rows: Convert booleans and merge static definitions
                        const processedRows = rows.map(row => {
                            const newRow = { ...row }; // Clone row

                            // Convert boolean-like integers back to booleans
                            for (const key in newRow) {
                                if (newRow[key] === 0) newRow[key] = false;
                                else if (newRow[key] === 1) newRow[key] = true;
                            }

                            // Merge static definition based on type and type_id
                            let definition = null;
                            switch (type) {
                                case 'ores':     definition = oreDefinitions[row.type_id]; break;
                                case 'trees':    definition = treeDefinitions[row.type_id]; break;
                                case 'animals':  definition = animalDefinitions[row.type_id]; break;
                                case 'mobs':     definition = mobDefinitions[row.type_id]; break;
                                // Structures don't need merging here as they are loaded client-side or from main DB
                            }

                            if (definition) {
                                // Merge definition properties, giving precedence to DB values (like x, y, id)
                                Object.assign(newRow, { ...definition, ...newRow });
                            } else if (type !== 'structures') { // Don't warn for structures
                                console.warn(`Static definition not found for type '${type}', id '${row.type_id}'`);
                            }

                            // For ores, trees, and mobs, ensure properties are set and handle field mappings
                            if (type === 'ores' || type === 'trees') {
                                // Set mineable/harvestable property if missing
                                if (type === 'ores' && !newRow.hasOwnProperty('mineable')) {
                                    newRow.mineable = true;
                                }
                                if (type === 'trees' && !newRow.hasOwnProperty('harvestable')) {
                                    newRow.harvestable = true;
                                }

                                // Rename last_harvested to harvested for client-side compatibility
                                if (newRow.hasOwnProperty('last_harvested')) {
                                    newRow.harvested = newRow.last_harvested;
                                    delete newRow.last_harvested;
                                }
                            }

                            // For mobs, ensure defeated and looted properties are properly set
                            if (type === 'mobs') {
                                // Set defeated property based on defeatTime or last_killed
                                if (newRow.hasOwnProperty('defeatTime') && newRow.defeatTime > 0) {
                                    newRow.defeated = true;
                                } else if (newRow.hasOwnProperty('last_killed') && newRow.last_killed > 0) {
                                    newRow.defeated = true;
                                    // If last_killed exists but defeatTime doesn't, set defeatTime for client compatibility
                                    if (!newRow.hasOwnProperty('defeatTime')) {
                                        newRow.defeatTime = newRow.last_killed;
                                    }
                                } else {
                                    // Explicitly set defeated to false if neither condition is met
                                    newRow.defeated = false;
                                }

                                // Ensure looted property is properly set
                                if (newRow.hasOwnProperty('looted')) {
                                    // Convert numeric 0/1 to boolean false/true if needed
                                    if (newRow.looted === 0) newRow.looted = false;
                                    else if (newRow.looted === 1) newRow.looted = true;
                                } else {
                                    // Explicitly set looted to false if it doesn't exist
                                    newRow.looted = false;
                                }
                            }

                            // Parse JSON strings if needed (example for future use)
                            // if (type === 'npcs') { // Example if NPCs were in DB
                            //     if (newRow.merchantInventory_json) newRow.merchantInventory = JSON.parse(newRow.merchantInventory_json);
                            //     if (newRow.dialogue_json) newRow.dialogue = JSON.parse(newRow.dialogue_json);
                            //     if (newRow.outloudTalk_json) newRow.outloudTalk = JSON.parse(newRow.outloudTalk_json);
                            //     delete newRow.merchantInventory_json;
                            //     delete newRow.dialogue_json;
                            //     delete newRow.outloudTalk_json;
                            // }
                            return newRow;
                        });
                    resolve({ type, data: processedRows });
                }
            });
        });
    });

    try {
        const results = await Promise.all(promises);
        const responseData = {};
        results.forEach(result => {
            responseData[result.type] = result.data; // Structure as { plants: [...], trees: [...], ... }
        });

        // Log summary of fetched objects
        console.log(`[API /api/zone_objects] Fetched ${responseData.ores?.length || 0} ores, ${responseData.trees?.length || 0} trees, ${responseData.mobs?.length || 0} mobs for zone ${zoneId}.`);

        // Debug the final response data for ores
        // if (responseData.ores) {
        //     console.log(`[handleGetZoneObjectsRequest] Sending ${responseData.ores.length} ores to client for zone ${zoneId}`);
        //     if (responseData.ores.length > 0) {
        //         console.log(`[handleGetZoneObjectsRequest] First ore in response:`, responseData.ores[0]);
        //     }
        // } else {
        //     console.log(`[handleGetZoneObjectsRequest] No ores in response for zone ${zoneId}`)
        // }

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(responseData));

    } catch (error) { // This catch might be less likely now errors in db.all resolve
        console.error(`Error processing object fetch for zone ${zoneId}:`, error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Failed to fetch zone objects.' }));
    }
}

/**
  * Handle saving NPCs data
  * @param {http.IncomingMessage} req - The request object
  * @param {http.ServerResponse} res - The response object
 */
function handleSaveNpcsRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            // Parse the JSON data (expecting the NPCs object)
            const npcsData = JSON.parse(body);

            // Define the file path
            const npcsFile = path.join(__dirname, 'data', 'npcs', 'npcs.json');

            // Ensure the directory exists
            const npcsDir = path.dirname(npcsFile);
            if (!fs.existsSync(npcsDir)) {
                fs.mkdirSync(npcsDir, { recursive: true });
            }

            // Save the NPCs data to the file
            fs.writeFileSync(npcsFile, JSON.stringify(npcsData, null, 2));
            console.log(`NPCs saved to ${npcsFile}`);

            // Send the response
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true }));
        } catch (error) {
            // Error parsing JSON or saving data
            res.writeHead(400, { 'Content-Type': 'application/json' });
            console.error("Save NPCs Error:", error.message);
            res.end(JSON.stringify({ error: 'Invalid JSON data or save error' }));
        }
    });
}

/**
 * Handle saving items data
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleSaveItemsRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            // Parse the JSON data (expecting the items object)
            const itemsData = JSON.parse(body);

            // Define the file path
            const itemsFile = path.join(__dirname, 'data', 'items', 'items.json');

            // Ensure the directory exists
            const itemsDir = path.dirname(itemsFile);
            if (!fs.existsSync(itemsDir)) {
                fs.mkdirSync(itemsDir, { recursive: true });
            }

            // Save the items data to the file
            fs.writeFileSync(itemsFile, JSON.stringify(itemsData, null, 2));
            console.log(`Items saved to ${itemsFile}`);

            // Send the response
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true }));
        } catch (error) {
            // Error parsing JSON or saving data
            res.writeHead(400, { 'Content-Type': 'application/json' });
            console.error("Save Items Error:", error.message);
            res.end(JSON.stringify({ error: 'Invalid JSON data or save error' }));
        }
    });
}

/**
 * Handle saving mobs data
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleSaveMobsRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const mobsData = JSON.parse(body);
            const mobsFile = path.join(__dirname, 'data', 'mobs', 'mobs.json');
            const mobsDir = path.dirname(mobsFile);
            if (!fs.existsSync(mobsDir)) {
                fs.mkdirSync(mobsDir, { recursive: true });
            }
            fs.writeFileSync(mobsFile, JSON.stringify(mobsData, null, 2));
            console.log(`Mobs saved to ${mobsFile}`);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true }));
        } catch (error) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            console.error("Save Mobs Error:", error.message);
            res.end(JSON.stringify({ error: 'Invalid JSON data or save error' }));
        }
    });
}

/**
 * Handle saving plants data
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleSavePlantsRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const plantsData = JSON.parse(body);
            const plantsFile = path.join(__dirname, 'data', 'flora', 'plants.json');
            const plantsDir = path.dirname(plantsFile);
            if (!fs.existsSync(plantsDir)) {
                fs.mkdirSync(plantsDir, { recursive: true });
            }
            fs.writeFileSync(plantsFile, JSON.stringify(plantsData, null, 2));
            console.log(`Plants saved to ${plantsFile}`);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true }));
        } catch (error) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            console.error("Save Plants Error:", error.message);
            res.end(JSON.stringify({ error: 'Invalid JSON data or save error' }));
        }
    });
}

/**
 * Handle saving trees data
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleSaveTreesRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const treesData = JSON.parse(body);
            const treesFile = path.join(__dirname, 'data', 'flora', 'trees.json');
            const treesDir = path.dirname(treesFile);
            if (!fs.existsSync(treesDir)) {
                fs.mkdirSync(treesDir, { recursive: true });
            }
            fs.writeFileSync(treesFile, JSON.stringify(treesData, null, 2));
            console.log(`Trees saved to ${treesFile}`);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true }));
        } catch (error) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            console.error("Save Trees Error:", error.message);
            res.end(JSON.stringify({ error: 'Invalid JSON data or save error' }));
        }
    });
}

/**
 * Handle saving animals data
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleSaveAnimalsRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const animalsData = JSON.parse(body);
            const animalsFile = path.join(__dirname, 'data', 'fauna', 'animals.json');
            const animalsDir = path.dirname(animalsFile);
            if (!fs.existsSync(animalsDir)) {
                fs.mkdirSync(animalsDir, { recursive: true });
            }
            fs.writeFileSync(animalsFile, JSON.stringify(animalsData, null, 2));
            console.log(`Animals saved to ${animalsFile}`);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true }));
        } catch (error) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            console.error("Save Animals Error:", error.message);
            res.end(JSON.stringify({ error: 'Invalid JSON data or save error' }));
        }
    });
}

// --- Authentication Handlers ---

/**
 * Handle user registration requests
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleRegisterRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { username, password } = JSON.parse(body);

            if (!username || !password) {
                return res.writeHead(400, { 'Content-Type': 'application/json' })
                          .end(JSON.stringify({ error: 'Username and password are required.' }));
            }
            if (password.length < 6) {
                 return res.writeHead(400, { 'Content-Type': 'application/json' })
                           .end(JSON.stringify({ error: 'Password must be at least 6 characters long.' }));
            }

            // Check if username already exists
            db.get('SELECT username FROM users WHERE username = ?', [username], (err, row) => {
                if (err) {
                    console.error("Database error during registration check:", err.message);
                    return res.writeHead(500, { 'Content-Type': 'application/json' })
                              .end(JSON.stringify({ error: 'Database error checking username.' }));
                }
                if (row) {
                    return res.writeHead(409, { 'Content-Type': 'application/json' }) // 409 Conflict
                              .end(JSON.stringify({ error: 'Username already taken.' }));
                }

                // Hash password and insert user
                bcrypt.hash(password, SALT_ROUNDS, (hashErr, passwordHash) => {
                    if (hashErr) {
                        console.error("Error hashing password:", hashErr);
                        return res.writeHead(500, { 'Content-Type': 'application/json' })
                                  .end(JSON.stringify({ error: 'Error securing password.' }));
                    }

                    db.run('INSERT INTO users (username, passwordHash) VALUES (?, ?)', [username, passwordHash], function(insertErr) {
                        if (insertErr) {
                            console.error("Database error during user insertion:", insertErr.message);
                            return res.writeHead(500, { 'Content-Type': 'application/json' })
                                      .end(JSON.stringify({ error: 'Database error creating user.' }));
                        }
                        console.log(`User registered: ${username} (ID: ${this.lastID})`);
                        res.writeHead(201, { 'Content-Type': 'application/json' }); // 201 Created
                        res.end(JSON.stringify({ success: true, message: 'Registration successful!' }));
                    });
                });
            });

        } catch (error) {
            console.error("Register Error:", error.message);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data.' }));
        }
    });
}

/**
 * Handle user login requests
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleLoginRequest(req, res) {
     if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { username, password } = JSON.parse(body);

            if (!username || !password) {
                return res.writeHead(400, { 'Content-Type': 'application/json' })
                          .end(JSON.stringify({ error: 'Username and password are required.' }));
            }

            // Find user by username
            db.get('SELECT userId, username, passwordHash FROM users WHERE username = ?', [username], (err, user) => {
                if (err) {
                    console.error("Database error during login:", err.message);
                    return res.writeHead(500, { 'Content-Type': 'application/json' })
                              .end(JSON.stringify({ error: 'Database error during login.' }));
                }
                if (!user) {
                    // User not found - generic error for security
                    return res.writeHead(401, { 'Content-Type': 'application/json' }) // 401 Unauthorized
                              .end(JSON.stringify({ error: 'Invalid username or password.' }));
                }

                // Compare submitted password with stored hash
                bcrypt.compare(password, user.passwordHash, (compareErr, isMatch) => {
                    if (compareErr) {
                        console.error("Error comparing passwords:", compareErr);
                        return res.writeHead(500, { 'Content-Type': 'application/json' })
                                  .end(JSON.stringify({ error: 'Error during authentication.' }));
                    }

                    if (isMatch) {
                        console.log(`User logged in: ${username} (ID: ${user.userId})`);

                        // --- Simple Session Creation ---
                        const sessionId = crypto.randomUUID();
                        activeSessions[sessionId] = user.userId;
                        console.log(`Session created: ${sessionId} for userId: ${user.userId}`);
                        // --- End Session Creation ---

                        // Set the session cookie
                        res.writeHead(200, {
                            'Content-Type': 'application/json',
                            'Set-Cookie': `sessionId=${sessionId}; Path=/; HttpOnly; SameSite=Strict`
                        });

                        // Send back session ID instead of just userId
                        res.end(JSON.stringify({
                            success: true,
                            message: 'Login successful!',
                            sessionId: sessionId // Client needs to store this
                        }));
                    } else {
                        // Passwords don't match - generic error
                        res.writeHead(401, { 'Content-Type': 'application/json' })
                           .end(JSON.stringify({ error: 'Invalid username or password.' }));
                    }
                });
            });

        } catch (error) {
            console.error("Login Error:", error.message);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data.' }));
        }
    });
}

// --- End Authentication Handlers ---


/**
 * Handle NPC chat requests using Grok API
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleNpcChatRequest(req, res) {
    if (!XAI_API_KEY) {
        console.error("XAI_API_KEY environment variable not set.");
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Server configuration error: API key missing.' }));
        return;
    }

    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const requestData = JSON.parse(body);
            const { npc, playerMessage, playerContext } = requestData;

            if (!npc || !playerMessage || !playerContext) {
                throw new Error("Missing required data for NPC chat.");
            }

            // Load items data to get item names
            let itemsData = {};
            try {
                const itemsFile = path.join(__dirname, 'data', 'items', 'items.json');
                if (fs.existsSync(itemsFile)) {
                    const itemsContent = fs.readFileSync(itemsFile, 'utf8');
                    itemsData = JSON.parse(itemsContent);
                }
            } catch (error) {
                console.error("Error loading items for NPC chat:", error);
            }

            // Format inventory items with names from items.json
            const inventoryText = npc.merchantInventory?.map(item => {
                const itemInfo = itemsData[item.id];
                return itemInfo ? `${itemInfo.name} (${item.price} gold)` : `${item.id} (${item.price} gold)`;
            }).join(', ') || 'nothing right now';

            // --- Construct Grok API Request ---
            const systemPrompt = `You are ${npc.name}, a ${npc.description}. You are talking to a player named ${playerContext.playerName}.
You sell the following items: ${inventoryText}.
The player has ${playerContext.playerGold} gold.
Respond naturally to the player's message based on your role and inventory. Keep responses concise and in character. Do not mention buying or selling unless the player asks about it or tries to trade.`;

            const grokPayload = JSON.stringify({
                messages: [
                    { role: "system", content: systemPrompt },
                    { role: "user", content: playerMessage }
                ],
                model: "grok-1.5-flash", // Using flash model for speed/cost
                stream: false,
                temperature: 0.7 // Allow some creativity
            });

            const options = {
                hostname: 'api.x.ai',
                path: '/v1/chat/completions',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${XAI_API_KEY}`,
                    'Content-Length': Buffer.byteLength(grokPayload)
                }
            };

            const grokReq = https.request(options, (grokRes) => {
                let responseBody = '';
                grokRes.on('data', (chunk) => { responseBody += chunk; });
                grokRes.on('end', () => {
                    try {
                        if (grokRes.statusCode >= 200 && grokRes.statusCode < 300) {
                            const grokResponse = JSON.parse(responseBody);
                            const reply = grokResponse.choices?.[0]?.message?.content || "I... don't know what to say.";
                            res.writeHead(200, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ reply: reply }));
                        } else {
                            console.error(`Grok API Error (${grokRes.statusCode}):`, responseBody);
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ error: 'Failed to get response from AI assistant.' }));
                        }
                    } catch (parseError) {
                        console.error("Error parsing Grok response:", parseError, responseBody);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: 'Error processing AI response.' }));
                    }
                });
            });

            grokReq.on('error', (error) => {
                console.error("Error calling Grok API:", error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Failed to connect to AI assistant.' }));
            });

            grokReq.write(grokPayload);
            grokReq.end();

        } catch (error) {
            console.error("NPC Chat Error:", error.message);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data for NPC chat.' }));
        }
    });
}

// Initialize Definitions and Database
loadDefinitions();

// Run database migrations before initializing the database connections
(async () => {
    try {
        console.log('Running database migrations...');
        await migrationManager.initMigrations();
        console.log('Database migrations completed successfully.');

        // Initialize database connections after migrations are complete
        initDatabase();
        marketDb.initMarketDb(); // Initialize the market database
        loadServerMessages(); // Load server messages configuration

        // Initialize the database module
        const db = require('./server/database');
        await db.init();
        console.log('Database module initialized successfully.');

        // Initialize the Quest Manager
        await QuestManager.init();
        console.log('Quest Manager initialized successfully.');

        // Initialize Guilds Database
        console.log('Setting up guilds database...');
        setupGuildsDatabase(); // Call the guilds setup function
        console.log('Guilds database setup initiated.');

    } catch (error) {
        console.error('Error during database migrations or initialization:', error);
        // Continue with database initialization even if migrations fail
        initDatabase();
        marketDb.initMarketDb(); // Initialize the market database
        setupGuildsDatabase(); // Call the guilds setup function if migrations failed before it
        loadServerMessages(); // Load server messages configuration

        // Try to initialize the database module
        try {
            const db = require('./server/database');
            await db.init();
            console.log('Database module initialized successfully.');
        } catch (dbError) {
            console.error('Error initializing database module:', dbError);
        }

        // Try to initialize the Quest Manager even if migrations failed
        try {
            await QuestManager.init();
            console.log('Quest Manager initialized successfully.');
        } catch (questError) {
            console.error('Error initializing Quest Manager:', questError);
        }
    }
})()

/**
 * Handle quests request
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
async function handleQuestsRequest(req, res) {
    // Only allow GET requests
    if (req.method !== 'GET') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get the session ID from the cookie
    const cookies = parseCookies(req);
    const sessionId = cookies.sessionId;

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Unauthorized' }));
        return;
    }

    const userId = activeSessions[sessionId];

    try {
        // Get quests for the player
        const questsData = await QuestManager.getQuestsForUI(userId);

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(questsData));
    } catch (error) {
        console.error('Error getting quests:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Failed to get quests' }));
    }
}

/**
 * Handle start quest request
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
async function handleStartQuestRequest(req, res) {
    // Only allow POST requests
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get the session ID from the cookie
    const cookies = parseCookies(req);
    const sessionId = cookies.sessionId;

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Unauthorized' }));
        return;
    }

    const userId = activeSessions[sessionId];

    // Parse the request body
    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', async () => {
        try {
            const data = JSON.parse(body);
            const questId = data.questId;

            if (!questId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Quest ID is required' }));
                return;
            }

            // Start the quest
            const success = await QuestManager.startQuest(userId, questId);

            if (success) {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: true }));
            } else {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Failed to start quest' }));
            }
        } catch (error) {
            console.error('Error starting quest:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Failed to start quest' }));
        }
    });
}

/**
 * Handle market listings request
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleMarketListingsRequest(req, res) {
    if (req.method !== 'GET') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Parse query parameters
    const parsedUrl = url.parse(req.url, true);
    const location = parsedUrl.query.location || 'avelia';
    const category = parsedUrl.query.category || 'all';

    // Get the session ID from header, query parameter, or cookie
    let sessionId = req.headers['x-session-id'];

    // If not in header, try query parameter
    if (!sessionId && parsedUrl.query.sessionId) {
        sessionId = parsedUrl.query.sessionId;
    }

    // If still not found, try cookie
    if (!sessionId) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not authenticated' }));
        return;
    }

    const userId = activeSessions[sessionId];

    // Get market listings
    marketDb.getMarketListings(location, category)
        .then(listings => {
            // Filter out the user's own listings
            const filteredListings = listings.filter(listing => listing.seller_id != userId);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true, listings: filteredListings }));
        })
        .catch(error => {
            console.error('Error getting market listings:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Error getting market listings' }));
        });
}

/**
 * Handle market transactions request
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleMarketTransactionsRequest(req, res) {
    if (req.method !== 'GET') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get session ID from headers
    const sessionId = req.headers['x-session-id'];
    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not authenticated' }));
        return;
    }

    const userId = activeSessions[sessionId];

    // Get user's transaction history
    marketDb.getUserTransactions(userId)
        .then(transactions => {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                transactions,
                userId: userId // Include the userId in the response
            }));
        })
        .catch(error => {
            console.error('Error getting user transactions:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Error getting transaction history' }));
        });
}

/**
 * Handle market listing request
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleMarketListingRequest(req, res) {
    console.log('Handling market listing request:', req.url);

    // Parse the URL to get the listing ID
    const parsedUrl = url.parse(req.url, true);
    const pathParts = parsedUrl.pathname.split('/');
    const listingId = pathParts[pathParts.length - 1];

    console.log('Parsed listing ID:', listingId);

    if (!listingId || listingId === 'listing') {
        console.log('Missing listing ID');
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Missing listing ID' }));
        return;
    }

    // Get the session ID from header or cookie
    let sessionId = req.headers['x-session-id'];

    // If not in header, try cookie
    if (!sessionId) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not authenticated' }));
        return;
    }

    // Get the listing
    console.log('Getting market listing with ID:', listingId);
    marketDb.getMarketListing(listingId)
        .then(listing => {
            console.log('Listing found:', listing ? 'Yes' : 'No');
            if (!listing) {
                console.log('Listing not found');
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Listing not found' }));
                return;
            }

            console.log('Sending listing details:', listing);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true, listing }));
        })
        .catch(error => {
            console.error('Error getting market listing:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Error getting market listing' }));
        });
}

/**
 * Handle my listings request
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleMyListingsRequest(req, res) {
    if (req.method !== 'GET') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get the session ID from header or cookie
    let sessionId = req.headers['x-session-id'];

    // If not in header, try cookie
    if (!sessionId) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not authenticated' }));
        return;
    }

    const userId = activeSessions[sessionId];

    // Get the user's listings
    marketDb.getSellerListings(userId)
        .then(listings => {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true, listings }));
        })
        .catch(error => {
            console.error('Error getting seller listings:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Error getting your listings' }));
        });
}

/**
 * Handle create listing request
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleCreateListingRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get the session ID from header or cookie
    let sessionId = req.headers['x-session-id'];

    // If not in header, try cookie
    if (!sessionId) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not authenticated' }));
        return;
    }

    const userId = activeSessions[sessionId];

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { itemId, quantity, price, location } = JSON.parse(body);

            // Get user data
            db.get('SELECT username FROM users WHERE userId = ?', [userId], (err, user) => {
                if (err) {
                    console.error('Error getting user data:', err);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Error getting user data' }));
                    return;
                }

                if (!user) {
                    res.writeHead(404, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'User not found' }));
                    return;
                }

                // We'll skip inventory check since we're using the client-side inventory
                // The client has already verified the player has the item

                // Get the full item details from World items
                const itemsFile = path.join(__dirname, 'data', 'items', 'items.json');
                const items = JSON.parse(fs.readFileSync(itemsFile, 'utf8'));
                const itemDetails = items[itemId];

                if (!itemDetails) {
                    res.writeHead(400, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Item not found in items database' }));
                    return;
                }

                // Create the listing
                const itemName = itemDetails.name;
                const itemDescription = itemDetails.description || '';

                const listing = {
                    seller_id: userId,
                    seller_name: user.username,
                    item_id: itemId,
                    item_name: itemName,
                    item_description: itemDescription,
                    quantity: quantity,
                    price: price,
                    location: location
                };

                marketDb.createListing(listing)
                    .then(listingId => {
                        // Update the player's inventory in the database
                        db.get('SELECT playerStateJSON FROM player_data WHERE userId = ?', [userId], (err, playerData) => {
                            if (err) {
                                console.error('Error getting player data:', err);
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ error: 'Error getting player data' }));
                                return;
                            }

                            if (!playerData || !playerData.playerStateJSON) {
                                res.writeHead(404, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ error: 'Player data not found' }));
                                return;
                            }

                            try {
                                // Parse player state
                                const playerState = JSON.parse(playerData.playerStateJSON);

                                // Update inventory
                                if (!Array.isArray(playerState.inventory)) {
                                    playerState.inventory = [];
                                }

                                // Find the item in the inventory
                                const itemIndex = playerState.inventory.findIndex(item => item.id === itemId);

                                if (itemIndex === -1) {
                                    // Item not found in inventory
                                    res.writeHead(400, { 'Content-Type': 'application/json' });
                                    res.end(JSON.stringify({ error: 'Item not found in inventory' }));
                                    return;
                                }

                                // Check if player has enough of the item
                                if (playerState.inventory[itemIndex].quantity < quantity) {
                                    res.writeHead(400, { 'Content-Type': 'application/json' });
                                    res.end(JSON.stringify({ error: 'Not enough items in inventory' }));
                                    return;
                                }

                                // Remove the item from inventory
                                playerState.inventory[itemIndex].quantity -= quantity;

                                // Remove the item completely if quantity is 0
                                if (playerState.inventory[itemIndex].quantity <= 0) {
                                    playerState.inventory.splice(itemIndex, 1);
                                }

                                // Save updated player state
                                const updatedPlayerStateJSON = JSON.stringify(playerState);

                                db.run('UPDATE player_data SET playerStateJSON = ? WHERE userId = ?',
                                    [updatedPlayerStateJSON, userId],
                                    function(updateErr) {
                                        if (updateErr) {
                                            console.error('Error updating player inventory:', updateErr);
                                            res.writeHead(500, { 'Content-Type': 'application/json' });
                                            res.end(JSON.stringify({ error: 'Error updating player inventory' }));
                                            return;
                                        }

                                        console.log(`Removed ${quantity} ${itemName} from player ${userId}'s inventory for market listing`);

                                        // Send success response
                                        res.writeHead(200, { 'Content-Type': 'application/json' });
                                        res.end(JSON.stringify({ success: true, listingId, itemName }));
                                    }
                                );
                            } catch (parseError) {
                                console.error('Error parsing player state:', parseError);
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ error: 'Error parsing player state' }));
                            }
                        });
                    })
                    .catch(error => {
                        console.error('Error creating listing:', error);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: 'Error creating listing' }));
                    });
            });
        } catch (error) {
            console.error('Error parsing request:', error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data' }));
        }
    });
}

/**
 * Handle buy listing request
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleBuyListingRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get the session ID from header or cookie
    let sessionId = req.headers['x-session-id'];

    // If not in header, try cookie
    if (!sessionId) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not authenticated' }));
        return;
    }

    const buyerId = activeSessions[sessionId];

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { listingId, quantity: requestedQuantity } = JSON.parse(body);

            // Get the listing
            marketDb.getMarketListing(listingId)
                .then(listing => {
                    if (!listing || listing.status !== 'active') {
                        res.writeHead(404, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: 'Listing not found or no longer available' }));
                        return;
                    }

                    // Get buyer data from player_data table to access gold from playerStateJSON
                    db.get('SELECT u.username, p.playerStateJSON FROM users u LEFT JOIN player_data p ON u.userId = p.userId WHERE u.userId = ?', [buyerId], (err, buyer) => {
                        if (err) {
                            console.error('Error getting buyer data:', err);
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ error: 'Error getting buyer data' }));
                            return;
                        }

                        if (!buyer) {
                            res.writeHead(404, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ error: 'Buyer not found' }));
                            return;
                        }

                        // Parse player state to get gold
                        let playerState = {};
                        try {
                            if (buyer.playerStateJSON) {
                                playerState = JSON.parse(buyer.playerStateJSON);
                            }
                        } catch (error) {
                            console.error('Error parsing player state:', error);
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ error: 'Error parsing player state' }));
                            return;
                        }

                        // Get gold from player state
                        const buyerGold = playerState.gold || 0;

                        // Get seller data from player_data table
                        db.get('SELECT u.username, p.playerStateJSON FROM users u LEFT JOIN player_data p ON u.userId = p.userId WHERE u.userId = ?', [listing.seller_id], (err, seller) => {
                            if (err) {
                                console.error('Error getting seller data:', err);
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ error: 'Error getting seller data' }));
                                return;
                            }

                            if (!seller) {
                                res.writeHead(404, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ error: 'Seller not found' }));
                                return;
                            }

                            // Parse seller's player state to get gold
                            let sellerState = {};
                            try {
                                if (seller.playerStateJSON) {
                                    sellerState = JSON.parse(seller.playerStateJSON);
                                }
                            } catch (error) {
                                console.error('Error parsing seller state:', error);
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ error: 'Error parsing seller state' }));
                                return;
                            }

                            // Get gold from seller state
                            const sellerGold = sellerState.gold || 0;

                            // Determine quantity to purchase
                            const purchaseQuantity = requestedQuantity ? parseInt(requestedQuantity) : listing.quantity;

                            // Validate quantity
                            if (isNaN(purchaseQuantity) || purchaseQuantity < 1) {
                                res.writeHead(400, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ error: 'Invalid quantity' }));
                                return;
                            }

                            // Check if requested quantity is available
                            if (purchaseQuantity > listing.quantity) {
                                res.writeHead(400, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ error: 'Requested quantity exceeds available quantity' }));
                                return;
                            }

                            // Calculate price for the requested quantity
                            const pricePerItem = listing.price;
                            const totalItemPrice = pricePerItem * purchaseQuantity;

                            // Calculate tax
                            const taxRate = 5; // Default to 5% if not specified
                            const taxAmount = Math.floor(totalItemPrice * (taxRate / 100));
                            const totalPrice = totalItemPrice + taxAmount;

                            // Check if buyer has enough gold
                            if (buyerGold < totalPrice) {
                                res.writeHead(400, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ error: 'Not enough gold' }));
                                return;
                            }

                            // Determine if we're buying all or part of the listing
                            const isFullPurchase = purchaseQuantity === listing.quantity;

                            // Update listing based on purchase type
                            let updatePromise;
                            if (isFullPurchase) {
                                updatePromise = marketDb.updateListingStatus(listingId, 'sold');
                            } else {
                                updatePromise = marketDb.updateListingQuantity(listingId, listing.quantity - purchaseQuantity);
                            }

                            updatePromise.then(() => {
                                    // Record the transaction
                                    const transaction = {
                                        listing_id: listingId,
                                        seller_id: listing.seller_id,
                                        seller_name: listing.seller_name,
                                        buyer_id: buyerId,
                                        buyer_name: buyer.username,
                                        item_id: listing.item_id,
                                        item_name: listing.item_name,
                                        quantity: purchaseQuantity,
                                        price: listing.price,
                                        tax_rate: taxRate,
                                        tax_amount: taxAmount, // This is already the total tax amount for all items
                                        location: listing.location,
                                        buyer_before_gold: buyerGold, // Buyer's gold before purchase
                                        buyer_after_gold: buyerGold - totalPrice, // Buyer's gold after purchase
                                        seller_before_gold: sellerGold, // Seller's gold before sale
                                        seller_after_gold: sellerGold + (listing.price * purchaseQuantity) // Seller's gold after sale
                                    };

                                    return marketDb.recordTransaction(transaction);
                                })
                                .then(() => {
                                    // Update buyer's player state with new gold amount
                                    playerState.gold = buyerGold - totalPrice;

                                    // Add the purchased item to buyer's inventory
                                    if (!Array.isArray(playerState.inventory)) {
                                        playerState.inventory = [];
                                    }

                                    // Check if the item already exists in the inventory
                                    const existingItemIndex = playerState.inventory.findIndex(item => item.id === listing.item_id);

                                    if (existingItemIndex !== -1) {
                                        // Item exists, update quantity
                                        playerState.inventory[existingItemIndex].quantity += listing.quantity;
                                    } else {
                                        // Item doesn't exist, add it
                                        playerState.inventory.push({
                                            id: listing.item_id,
                                            name: listing.item_name,
                                            quantity: listing.quantity
                                        });
                                    }

                                    console.log(`Added ${purchaseQuantity} ${listing.item_name} to player ${buyerId}'s inventory from market purchase`);

                                    // Update seller's player state with new gold amount
                                    const sellerEarnings = listing.price * purchaseQuantity;
                                    sellerState.gold = sellerGold + sellerEarnings;
                                    console.log(`Seller ${listing.seller_id} earned ${sellerEarnings} gold. Before: ${sellerGold}, After: ${sellerState.gold}`);

                                    // Save updated player states
                                    const buyerStateJSON = JSON.stringify(playerState);
                                    const sellerStateJSON = JSON.stringify(sellerState);

                                    // Update buyer's player state in database
                                    db.run(
                                        'UPDATE player_data SET playerStateJSON = ? WHERE userId = ?',
                                        [buyerStateJSON, buyerId],
                                        function(err) {
                                            if (err) {
                                                console.error('Error updating buyer:', err);
                                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                                res.end(JSON.stringify({ error: 'Error updating buyer' }));
                                                return;
                                            }

                                            // Update seller's player state in database
                                            db.run(
                                                'UPDATE player_data SET playerStateJSON = ? WHERE userId = ?',
                                                [sellerStateJSON, listing.seller_id],
                                                function(err) {
                                                    if (err) {
                                                        console.error('Error updating seller:', err);
                                                        res.writeHead(500, { 'Content-Type': 'application/json' });
                                                        res.end(JSON.stringify({ error: 'Error updating seller' }));
                                                        return;
                                                    }

                                                    // Find buyer and seller socket IDs
                                                    let buyerSocketId = null;
                                                    let sellerSocketId = null;

                                                    for (const socketId in connectedPlayers) {
                                                        if (connectedPlayers[socketId].userId == buyerId) {
                                                            buyerSocketId = socketId;
                                                        }
                                                        if (connectedPlayers[socketId].userId == listing.seller_id) {
                                                            sellerSocketId = socketId;
                                                        }
                                                    }

                                                    // Send real-time update to buyer
                                                    if (buyerSocketId) {
                                                        io.to(buyerSocketId).emit('gold_update', {
                                                            gold: buyerGold - totalPrice,
                                                            transaction: 'purchase'
                                                        });
                                                    }

                                                    // Send real-time update to seller
                                                    if (sellerSocketId) {
                                                        io.to(sellerSocketId).emit('gold_update', {
                                                            gold: sellerGold + listing.price * purchaseQuantity,
                                                            transaction: 'sale'
                                                        });
                                                    }

                                                    res.writeHead(200, { 'Content-Type': 'application/json' });
                                                    res.end(JSON.stringify({
                                                        success: true,
                                                        itemId: listing.item_id,
                                                        itemName: listing.item_name,
                                                        quantity: purchaseQuantity,
                                                        totalPrice
                                                    }));
                                                }
                                            );
                                        }
                                    );
                                })
                                .catch(error => {
                                    console.error('Error processing purchase:', error);
                                    res.writeHead(500, { 'Content-Type': 'application/json' });
                                    res.end(JSON.stringify({ error: 'Error processing purchase' }));
                                });
                        });
                    });
                })
                .catch(error => {
                    console.error('Error getting listing:', error);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Error getting listing' }));
                });
        } catch (error) {
            console.error('Error parsing request:', error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data' }));
        }
    });
}

/**
 * Handle cancel listing request
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleCancelListingRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get the session ID from header or cookie
    let sessionId = req.headers['x-session-id'];

    // If not in header, try cookie
    if (!sessionId) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not authenticated' }));
        return;
    }

    const userId = activeSessions[sessionId];

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { listingId } = JSON.parse(body);

            // Get the listing
            marketDb.getMarketListing(listingId)
                .then(listing => {
                    if (!listing || listing.status !== 'active') {
                        res.writeHead(404, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: 'Listing not found or no longer available' }));
                        return;
                    }

                    // Check if user owns the listing
                    if (listing.seller_id != userId) {
                        res.writeHead(403, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: 'You don\'t own this listing' }));
                        return;
                    }

                    // Update listing status
                    marketDb.updateListingStatus(listingId, 'cancelled')
                        .then(() => {
                            // Return the item to the player's inventory
                            db.get('SELECT playerStateJSON FROM player_data WHERE userId = ?', [userId], (err, playerData) => {
                                if (err) {
                                    console.error('Error getting player data:', err);
                                    res.writeHead(500, { 'Content-Type': 'application/json' });
                                    res.end(JSON.stringify({ error: 'Error getting player data' }));
                                    return;
                                }

                                if (!playerData || !playerData.playerStateJSON) {
                                    res.writeHead(404, { 'Content-Type': 'application/json' });
                                    res.end(JSON.stringify({ error: 'Player data not found' }));
                                    return;
                                }

                                try {
                                    // Parse player state
                                    const playerState = JSON.parse(playerData.playerStateJSON);

                                    // Update inventory
                                    if (!Array.isArray(playerState.inventory)) {
                                        playerState.inventory = [];
                                    }

                                    // Find the item in the inventory
                                    const existingItemIndex = playerState.inventory.findIndex(item => item.id === listing.item_id);

                                    if (existingItemIndex !== -1) {
                                        // Item exists, update quantity
                                        playerState.inventory[existingItemIndex].quantity += listing.quantity;
                                    } else {
                                        // Item doesn't exist, add it
                                        playerState.inventory.push({
                                            id: listing.item_id,
                                            name: listing.item_name,
                                            quantity: listing.quantity
                                        });
                                    }

                                    // Save updated player state
                                    const updatedPlayerStateJSON = JSON.stringify(playerState);

                                    db.run('UPDATE player_data SET playerStateJSON = ? WHERE userId = ?',
                                        [updatedPlayerStateJSON, userId],
                                        function(updateErr) {
                                            if (updateErr) {
                                                console.error('Error updating player inventory:', updateErr);
                                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                                res.end(JSON.stringify({ error: 'Error updating player inventory' }));
                                                return;
                                            }

                                            console.log(`Returned ${listing.quantity} ${listing.item_name} to player ${userId}'s inventory from cancelled listing`);

                                            // Send success response
                                            res.writeHead(200, { 'Content-Type': 'application/json' });
                                            res.end(JSON.stringify({
                                                success: true,
                                                itemId: listing.item_id,
                                                itemName: listing.item_name,
                                                quantity: listing.quantity
                                            }));
                                        }
                                    );
                                } catch (parseError) {
                                    console.error('Error parsing player state:', parseError);
                                    res.writeHead(500, { 'Content-Type': 'application/json' });
                                    res.end(JSON.stringify({ error: 'Error parsing player state' }));
                                }
                            });
                        })
                        .catch(error => {
                            console.error('Error cancelling listing:', error);
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ error: 'Error cancelling listing' }));
                        });
                })
                .catch(error => {
                    console.error('Error getting listing:', error);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Error getting listing' }));
                });
        } catch (error) {
            console.error('Error parsing request:', error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data' }));
        }
    });
}

/**
 * Handle recording an activity completion
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleRecordActivityRequest(req, res) {
    console.log("🔍 [ACTIVITY] API endpoint /api/activity/record called");

    if (req.method !== 'POST') {
        console.log("❌ [ACTIVITY] Method not allowed:", req.method);
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get the session ID from header or cookie
    let sessionId = req.headers['x-session-id'];

    if (!sessionId) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not authenticated' }));
        return;
    }

    const userId = activeSessions[sessionId];

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const data = JSON.parse(body);
            const { activityType, activityId, result, metadata } = data;

            if (!activityType || !activityId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Missing required fields: activityType and activityId' }));
                return;
            }

            // Get username for logging
            db.get('SELECT username FROM users WHERE userId = ?', [userId], (err, user) => {
                if (err || !user) {
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Error getting user data' }));
                    return;
                }

                const username = user.username;

                // Process metadata - handle both string and object formats
                let metadataJson = null;
                if (metadata) {
                    if (typeof metadata === 'string') {
                        // If metadata is already a JSON string, use it directly
                        try {
                            // Validate it's valid JSON by parsing and re-stringifying
                            const parsed = JSON.parse(metadata);
                            metadataJson = JSON.stringify(parsed);
                        } catch (e) {
                            // If parsing fails, treat it as a regular string
                            metadataJson = JSON.stringify({ value: metadata });
                        }
                    } else {
                        // If metadata is an object, stringify it
                        metadataJson = JSON.stringify(metadata);
                    }
                }

                // Check if activities database is connected
                if (!dbActivities) {
                    // Try to reconnect to the database
                    const DB_ACTIVITIES_PATH = path.join(__dirname, 'data', 'dawnsword_activities.db');
                    dbActivities = new sqlite3.Database(DB_ACTIVITIES_PATH, (err) => {
                        if (err) {
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ error: 'Activities database connection failed' }));
                            return;
                        }

                        // Continue with the activity recording
                        processActivityRecord();
                    });
                } else {
                    // Database is already connected, proceed with recording
                    processActivityRecord();
                }

                // Function to process the activity record
                function processActivityRecord() {
                    dbActivities.get("SELECT name FROM sqlite_master WHERE type='table' AND name='player_activities'", (err, table) => {
                        if (err) {
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ error: 'Database error checking tables' }));
                            return;
                        }

                        if (!table) {
                            // Try to create the tables
                            dbActivities.serialize(() => {
                                // Create player_activities table
                                dbActivities.run(`CREATE TABLE IF NOT EXISTS player_activities (
                                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                                    user_id INTEGER NOT NULL,
                                    username TEXT NOT NULL,
                                    activity_type TEXT NOT NULL,
                                    activity_id TEXT NOT NULL,
                                    result TEXT DEFAULT 'completed',
                                    metadata TEXT,
                                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                                )`, (err) => {
                                    if (err) {
                                        res.writeHead(500, { 'Content-Type': 'application/json' });
                                        res.end(JSON.stringify({ error: 'Failed to create activities table' }));
                                        return;
                                    }

                                    // Create activity_cooldowns table
                                    dbActivities.run(`CREATE TABLE IF NOT EXISTS activity_cooldowns (
                                        user_id INTEGER NOT NULL,
                                        activity_type TEXT NOT NULL,
                                        activity_id TEXT NOT NULL,
                                        expires_at INTEGER NOT NULL,
                                        PRIMARY KEY (user_id, activity_type, activity_id)
                                    )`, (err) => {
                                        if (err) {
                                            res.writeHead(500, { 'Content-Type': 'application/json' });
                                            res.end(JSON.stringify({ error: 'Failed to create cooldowns table' }));
                                            return;
                                        }

                                        // Now proceed with the insert
                                        insertActivityRecord();
                                    });
                                });
                            });
                        } else {
                            insertActivityRecord();
                        }
                    });
                }

                // Function to insert the activity record
                function insertActivityRecord() {
                    // Insert activity record
                    dbActivities.run(
                        `INSERT INTO player_activities
                        (user_id, username, activity_type, activity_id, result, metadata, timestamp)
                        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
                        [userId, username, activityType, activityId, result || 'completed', metadataJson],
                        function(err) {
                            if (err) {
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ error: 'Database error recording activity' }));
                                return;
                            }

                            // Also update the cooldown table
                            const cooldownHours = getCooldownHours(activityType);

                            if (cooldownHours > 0) {
                                const cooldownSeconds = cooldownHours * 3600;
                                const expiresAt = Math.floor(Date.now() / 1000) + cooldownSeconds;

                                dbActivities.run(
                                    `INSERT INTO activity_cooldowns
                                    (user_id, activity_type, activity_id, expires_at)
                                    VALUES (?, ?, ?, ?)
                                    ON CONFLICT(user_id, activity_type, activity_id)
                                    DO UPDATE SET expires_at = ?`,
                                    [userId, activityType, activityId, expiresAt, expiresAt],
                                    function(_err) {
                                        // Even if there's an error setting cooldown, we still return success for the activity
                                        res.writeHead(200, { 'Content-Type': 'application/json' });
                                        res.end(JSON.stringify({
                                            success: true,
                                            message: 'Activity recorded',
                                            activityId: this.lastID,
                                            cooldown: cooldownHours > 0 ? {
                                                hours: cooldownHours,
                                                expiresAt: expiresAt
                                            } : null
                                        }));
                                    }
                                );
                            } else {
                                // No cooldown for this activity type
                                res.writeHead(200, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({
                                    success: true,
                                    message: 'Activity recorded',
                                    activityId: this.lastID,
                                    cooldown: null
                                }));
                            }
                        }
                    );
                }
            });
        } catch (error) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data' }));
        }
    });
}

/**
 * Handle checking activity status (if player can perform an activity)
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleActivityStatusRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get the session ID from header or cookie
    let sessionId = req.headers['x-session-id'];
    if (!sessionId) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not authenticated' }));
        return;
    }

    const userId = activeSessions[sessionId];

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { activityType, activityId } = JSON.parse(body);

            if (!activityType || !activityId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Missing required fields: activityType and activityId' }));
                return;
            }

            // Check if activity is on cooldown
            const currentTime = Math.floor(Date.now() / 1000);

            dbActivities.get(
                `SELECT expires_at FROM activity_cooldowns
                WHERE user_id = ? AND activity_type = ? AND activity_id = ? AND expires_at > ?`,
                [userId, activityType, activityId, currentTime],
                function(err, row) {
                    if (err) {
                        console.error('Error checking activity status:', err);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: 'Database error checking activity status' }));
                        return;
                    }

                    if (row) {
                        // Activity is on cooldown
                        const remainingSeconds = row.expires_at - currentTime;
                        const remainingHours = Math.ceil(remainingSeconds / 3600);
                        const remainingMinutes = Math.ceil(remainingSeconds / 60);

                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({
                            available: false,
                            cooldown: {
                                expiresAt: row.expires_at,
                                remainingSeconds: remainingSeconds,
                                remainingMinutes: remainingMinutes,
                                remainingHours: remainingHours
                            }
                        }));
                    } else {
                        // Activity is available
                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({
                            available: true
                        }));
                    }
                }
            );
        } catch (error) {
            console.error('Error parsing request:', error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data' }));
        }
    });
}

// Debug endpoint removed for security reasons

/**
 * Handle retrieving activity history for a player
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleActivityHistoryRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get the session ID from header or cookie
    let sessionId = req.headers['x-session-id'];
    if (!sessionId) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not authenticated' }));
        return;
    }

    const userId = activeSessions[sessionId];

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { activityType, activityId, limit } = JSON.parse(body);

            if (!activityType) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Missing required field: activityType' }));
                return;
            }

            // Build the query based on parameters
            let query = `SELECT * FROM player_activities
                         WHERE user_id = ? AND activity_type = ?`;
            const params = [userId, activityType];

            // Add activityId filter if provided
            if (activityId) {
                query += ` AND activity_id = ?`;
                params.push(activityId);
            }

            // Add order and limit
            query += ` ORDER BY timestamp DESC LIMIT ?`;
            params.push(limit || 10); // Default to 10 if not specified

            dbActivities.all(query, params, function(err, rows) {
                if (err) {
                    console.error('Error retrieving activity history:', err);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Database error retrieving activity history' }));
                    return;
                }

                // Process rows to parse metadata JSON if present
                const activities = rows.map(row => {
                    const activity = { ...row };
                    if (activity.metadata) {
                        try {
                            activity.metadata = JSON.parse(activity.metadata);
                        } catch (e) {
                            // If parsing fails, keep as string
                            console.warn(`Failed to parse metadata for activity ${activity.id}:`, e);
                        }
                    }
                    return activity;
                });

                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: true,
                    activities: activities
                }));
            });
        } catch (error) {
            console.error('Error parsing request:', error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data' }));
        }
    });
}

/**
 * Get cooldown hours for a specific activity type
 * @param {string} activityType - The type of activity
 * @returns {number} - Cooldown hours (0 means no cooldown)
 */
function getCooldownHours(activityType) {
    // Define cooldown periods for different activity types
    const cooldowns = {
        'duel': 1,          // 1 hour cooldown for duels
        'quest': 24,        // 24 hour cooldown for quests
        'dungeon': 12,      // 12 hour cooldown for dungeons
        'boss': 48,         // 48 hour cooldown for boss fights
        'daily': 24,        // 24 hour cooldown for daily activities
        'weekly': 168,      // 168 hour (7 days) cooldown for weekly activities
    };

    return cooldowns[activityType] || 0; // Return 0 (no cooldown) if activity type not found
}

/**
 * Handle quick sell request
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleQuickSellRequest(req, res) {
    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Get the session ID from header or cookie
    let sessionId = req.headers['x-session-id'];

    // If not in header, try cookie
    if (!sessionId) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not authenticated' }));
        return;
    }

    const userId = activeSessions[sessionId];

    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { itemId, quantity } = JSON.parse(body);

            // Get user data from player_data
            db.get('SELECT playerStateJSON FROM player_data WHERE userId = ?', [userId], (err, userData) => {
                if (err) {
                    console.error('Error getting user data:', err);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Error getting user data' }));
                    return;
                }

                if (!userData) {
                    res.writeHead(404, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'User not found' }));
                    return;
                }

                // Parse player state
                let playerState = {};
                try {
                    playerState = JSON.parse(userData.playerStateJSON || '{}');
                } catch (parseErr) {
                    console.error('Error parsing player state:', parseErr);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Error parsing player state' }));
                    return;
                }

                // We'll skip inventory check since we're using the client-side inventory
                // The client has already verified the player has the item

                // Get the full item details from World items
                const itemsFile = path.join(__dirname, 'data', 'items', 'items.json');
                const items = JSON.parse(fs.readFileSync(itemsFile, 'utf8'));
                const itemDetails = items[itemId];

                if (!itemDetails) {
                    res.writeHead(400, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'Item not found in items database' }));
                    return;
                }

                // Calculate value - default to 0 if no value is defined
                const itemValue = itemDetails.value !== undefined ? itemDetails.value : 0;
                const totalValue = itemValue * quantity;
                const itemName = itemDetails.name;

                // Make sure gold exists in playerState
                if (typeof playerState.gold === 'undefined') {
                    playerState.gold = 0;
                }

                // Store the before gold balance
                const beforeGold = playerState.gold;

                // Add the sell value to the player's gold
                playerState.gold += totalValue;

                // Store the after gold balance
                const afterGold = playerState.gold;

                // Update player state in the database
                db.run(
                    'UPDATE player_data SET playerStateJSON = ? WHERE userId = ?',
                    [JSON.stringify(playerState), userId],
                    function(err) {
                        if (err) {
                            console.error('Error updating player state:', err);
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ error: 'Error updating player state' }));
                            return;
                        }

                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({
                            success: true,
                            itemName: itemName,
                            quantity,
                            totalValue,
                            beforeGold,
                            afterGold
                        }));
                    }
                );
            });
        } catch (error) {
            console.error('Error parsing request:', error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Invalid request data' }));
        }
    });
}

/**
 * Parse cookies from request headers
 * @param {http.IncomingMessage} req - The request object
 * @returns {Object} - Object with cookie name-value pairs
 */
function parseCookies(req) {
    const cookies = {};
    const cookieHeader = req.headers.cookie;
    if (cookieHeader) {
        cookieHeader.split(';').forEach(cookie => {
            const parts = cookie.split('=');
            const name = parts[0].trim();
            const value = parts[1] ? parts[1].trim() : '';
            cookies[name] = value;
        });
    }
    return cookies;
}

// --- Socket.IO Setup ---
const io = new Server(server, {
    cors: {
        origin: true, // Allow requests from any origin with credentials
        methods: ["GET", "POST"],
        credentials: true // Allow credentials (cookies, authorization headers, etc.)
    },
    // Enable connection state recovery to handle temporary disconnections
    connectionStateRecovery: {
        // How long to store session data (2 minutes)
        maxDisconnectionDuration: 2 * 60 * 1000,
        // Skip middlewares on recovery
        skipMiddlewares: true
    },
    // Increase ping timeout to handle slow connections
    pingTimeout: 60000,
    // Increase ping interval for better stability
    pingInterval: 25000
});

// Initialize the server-side NPC AI system
ServerNpcAI.init(io);

io.on('connection', (socket) => {
    console.log(`Socket connected: ${socket.id}`);

    // Basic Authentication
    socket.on('authenticate', async (sessionId) => {
        const userId = activeSessions[sessionId];
        if (!userId) {
            console.log(`Authentication failed for socket ${socket.id}: Invalid session ID`);
            socket.emit('auth_failed', 'Invalid session. Please login again.');
            // Consider disconnecting? socket.disconnect(true);
            return;
        }

        console.log(`Authenticating socket ${socket.id} for userId ${userId}`);

        // Check if this user already has an active socket connection
        if (activeUserSockets[userId]) {
            const existingSocketId = activeUserSockets[userId];

            // Check if the existing socket is still connected
            const existingSocket = io.sockets.sockets.get(existingSocketId);
            if (existingSocket) {
                console.log(`User ${userId} already has an active connection (socket ${existingSocketId}). Disconnecting previous connection.`);

                // Notify the existing socket that it's being disconnected due to a new login
                existingSocket.emit('duplicate_session', 'Your account has been logged in from another window or browser. This session has been disconnected.');

                // Disconnect the existing socket
                existingSocket.disconnect(true);

                // Remove the player from connected players
                if (connectedPlayers[existingSocketId]) {
                    const disconnectedPlayer = connectedPlayers[existingSocketId];
                    const zoneId = disconnectedPlayer.zoneId;

                    // Broadcast player disconnection to others in the same zone
                    for (const otherSocketId in connectedPlayers) {
                        if (otherSocketId !== existingSocketId && connectedPlayers[otherSocketId].zoneId === zoneId) {
                            io.to(otherSocketId).emit('player_left', existingSocketId);
                        }
                    }

                    // Remove player from NPC AI tracking
                    ServerNpcAI.removePlayer(existingSocketId);

                    // Remove from connected players
                    delete connectedPlayers[existingSocketId];
                }
            }
        }

        // Update the active socket for this user
        activeUserSockets[userId] = socket.id;

        // Fetch username
        db.get('SELECT username FROM users WHERE userId = ?', [userId], (userErr, userRow) => {
             if (userErr || !userRow) {
                 console.error(`Authentication error for socket ${socket.id}: Cannot find user ${userId}`, userErr);
                 socket.emit('auth_failed', 'User data not found.');
                 socket.disconnect(true);
                 return;
             }

             const username = userRow.username;
             // Check if the authenticated user is an admin
             const isPlayerAdmin = adminUsernames.includes(username);
             console.log(`User ${username} connected. Admin Status: ${isPlayerAdmin}`);

             // Fetch player data
             db.get('SELECT playerStateJSON FROM player_data WHERE userId = ?', [userId], (dataErr, dataRow) => {
                 let playerState = dataRow?.playerStateJSON ? JSON.parse(dataRow.playerStateJSON) : {}; // Start with empty state if no save
                 if (dataErr) {
                     console.error(`Error fetching player data for user ${userId}:`, dataErr.message);
                     // Proceed with defaults, maybe log error
                 } else if (!dataRow || !dataRow.playerStateJSON) {
                     console.log(`No saved player data found for user ${userId}. Using defaults.`);
                 } else {
                     console.log(`Loaded initial player state for ${username}:`, playerState);
                 }

                 // Store basic player info for server use with explicit numeric coordinates
                 const playerX = Number(playerState?.x || 0);
                 const playerY = Number(playerState?.y || 0);
                 const playerZoneId = playerState?.zoneId || 'avelia';

                 console.log(`Setting up player ${username} with position: x=${playerX}, y=${playerY}, zone=${playerZoneId}`);

                 // Initialize player data without horse information
                 connectedPlayers[socket.id] = {
                     userId,
                     username,
                     isAdmin: isPlayerAdmin,
                     zoneId: playerZoneId,
                     x: playerX,
                     y: playerY,
                     horse: null // Initialize as null, will be set later if active horse exists
                 };

                 // Check if player has an active horse that is not stabled
                 dbDynamic.get(`SELECT * FROM player_horses WHERE user_id = ? AND is_active = 1 AND stable_id IS NULL`, [userId], (horseErr, horse) => {
                     if (horseErr) {
                         console.error(`Error fetching active horse for user ${userId}:`, horseErr.message);
                     } else if (horse) {
                         console.log(`Found active horse for ${username}:`, horse);
                         // Store the active horse in the player data
                         connectedPlayers[socket.id].horse = horse;
                     } else {
                         console.log(`No active horse found for ${username}`);
                     }

                     console.log(`Player ${username} authenticated successfully. Sending data.`);

                     // Track player zone for NPC AI
                     ServerNpcAI.trackPlayerZone(socket.id, playerZoneId);

                     // Send NPC positions to the player
                     ServerNpcAI.sendNpcPositionsToClient(socket.id, playerZoneId);

                     // Send success confirmation and player data
                     socket.emit('auth_success', {
                         userId,
                         username: username, // Use fetched username
                         playerData: playerState, // Send the potentially modified playerState
                         isAdmin: isPlayerAdmin // Add isAdmin flag to client data
                     });

                     // Join the zone room
                     const currentZone = connectedPlayers[socket.id].zoneId;
                     // socket.join(currentZone); // Not implemented yet

                     // --- Chat History is now requested by the client after initialization ---

                     // --- Send existing players in the same zone to the new player ---
                     const playersInZone = {};
                     let playerCount = 0;

                     console.log(`Preparing to send current players in zone ${currentZone} to ${username}`);
                     console.log(`Total connected players: ${Object.keys(connectedPlayers).length}`);

                     for (const otherSocketId in connectedPlayers) {
                         if (otherSocketId !== socket.id) {
                             console.log(`Checking player ${connectedPlayers[otherSocketId].username} in zone ${connectedPlayers[otherSocketId].zoneId}`);

                             if (connectedPlayers[otherSocketId].zoneId === currentZone) {
                                 // Ensure we're sending complete player data with numeric coordinates
                                 const playerX = Number(connectedPlayers[otherSocketId].x || 0);
                                 const playerY = Number(connectedPlayers[otherSocketId].y || 0);

                                 console.log(`Adding player ${connectedPlayers[otherSocketId].username} at position x=${playerX}, y=${playerY}`);

                                 // Only include horse data if it's an active horse (not stabled)
                                 const otherPlayerHorse = connectedPlayers[otherSocketId].horse;
                                 const horseData = otherPlayerHorse && (!otherPlayerHorse.stable_id || otherPlayerHorse.is_active === 1)
                                     ? otherPlayerHorse
                                     : null;

                                 playersInZone[otherSocketId] = {
                                     ...connectedPlayers[otherSocketId],
                                     // Ensure x and y are explicitly included and are numbers
                                     x: playerX,
                                     y: playerY,
                                     horse: horseData, // Include only active horse data
                                     horsePosition: horseData ? connectedPlayers[otherSocketId].horsePosition || null : null // Include horse position only if horse is active
                                 };
                                 playerCount++;
                             }
                         }
                     }

                     console.log(`Sending ${playerCount} players to ${username}:`, playersInZone);
                     socket.emit('current_players', playersInZone); // Send the list
                     console.log(`Sent current players in zone ${currentZone} to ${username}`);
                     // --- End Send existing players ---

                     // Broadcast updated player count to all clients
                     broadcastPlayerCount();

                     // Send the first message from server_messages.json to the newly connected player
                     if (serverMessages.enabled && serverMessages.messages && serverMessages.messages.length > 0) {
                         const firstMessage = serverMessages.messages[0];

                         // Log the exact message for debugging
                         console.log('First server message for new player:', {
                             message: firstMessage,
                             hasLinks: firstMessage.includes('[link:'),
                             username: username
                         });

                         // Send the message to the newly connected player
                         socket.emit('server_message', {
                             message: firstMessage,
                             color: '#9370DB', // Medium purple color that fits with your game
                             hasLinks: firstMessage.includes('[link:')
                         });

                         console.log(`Welcome message sent to ${username}: ${firstMessage}`);
                     }

                     // --- Broadcast new player to others in the same zone ---
                     for (const otherSocketId in connectedPlayers) {
                         if (otherSocketId !== socket.id && connectedPlayers[otherSocketId].zoneId === currentZone) {
                             // Make sure we're sending complete player data including position
                             const playerData = {
                                 ...connectedPlayers[socket.id],
                                 // Ensure x and y are explicitly included and are numbers
                                 x: Number(connectedPlayers[socket.id].x || 0),
                                 y: Number(connectedPlayers[socket.id].y || 0)
                             };

                             console.log(`Broadcasting player_joined event to ${connectedPlayers[otherSocketId].username} with data:`, {
                                 socketId: socket.id,
                                 playerData: {
                                     username: playerData.username,
                                     x: playerData.x,
                                     y: playerData.y,
                                     zoneId: playerData.zoneId,
                                     // Only include horse data if it's an active horse (not stabled)
                                     horse: playerData.horse && (!playerData.horse.stable_id || playerData.horse.is_active === 1)
                                         ? playerData.horse
                                         : null
                                 }
                             });

                             // Create the updated player data with correct horse information
                             const updatedPlayerData = {
                                 username: playerData.username,
                                 x: playerData.x,
                                 y: playerData.y,
                                 zoneId: playerData.zoneId,
                                 // Only include horse data if it's an active horse (not stabled)
                                 horse: playerData.horse && (!playerData.horse.stable_id || playerData.horse.is_active === 1)
                                     ? playerData.horse
                                     : null
                             };

                             io.to(otherSocketId).emit('player_joined', { socketId: socket.id, playerData: updatedPlayerData });

                             // Request position update from existing player to ensure new player sees them
                             io.to(otherSocketId).emit('request_position_update');
                         }
                     }
                     console.log(`Broadcasted player_joined for ${username} to zone ${currentZone}`);
                     console.log(`Requested position updates from all players in zone ${currentZone}`);
                     // --- End Broadcast new player ---
                 });
             });
        });
    });


    socket.on('disconnect', () => {
        console.log(`Socket disconnected: ${socket.id}`);
        const disconnectedPlayer = connectedPlayers[socket.id];
        if (disconnectedPlayer) {
            console.log(`Player disconnected: ${disconnectedPlayer.username}`);

            // Remove from activeUserSockets if this is the current socket for this user
            const userId = disconnectedPlayer.userId;
            if (activeUserSockets[userId] === socket.id) {
                console.log(`Removing user ${userId} from activeUserSockets`);
                delete activeUserSockets[userId];
            }

            // --- Broadcast player disconnection to others in the same zone ---
            const zoneId = disconnectedPlayer.zoneId; // Get zone before deleting
            delete connectedPlayers[socket.id]; // Remove player from tracking FIRST
            for (const otherSocketId in connectedPlayers) {
                 // Check if the remaining player was in the same zone
                 if (connectedPlayers[otherSocketId].zoneId === zoneId) {
                     io.to(otherSocketId).emit('player_left', socket.id); // Send the ID of the player who left
                 }
            }
            console.log(`Broadcasted player_left for socket ${socket.id} to zone ${zoneId}`);
             // --- End Broadcast player disconnection ---

            // Remove player from NPC AI tracking
            ServerNpcAI.removePlayer(socket.id);

            // Broadcast updated player count to all clients
            broadcastPlayerCount();
        }
        // Clean up session? Maybe not here, sessions might persist longer than socket connections.
    });

    // --- Player Movement Handling ---
    socket.on('player_move', (data) => {
        // Check rate limiting
        if (isRateLimited(socket.id, 'player_move')) {
            // Silently drop excessive movement events
            return;
        }

        const movingPlayer = connectedPlayers[socket.id];
        if (!movingPlayer) {
            console.warn(`Received move event from unknown socket: ${socket.id}`);
            return; // Ignore if player not tracked
        }

        // Basic validation (more can be added, e.g., distance check)
        if (typeof data.x !== 'number' || typeof data.y !== 'number' || typeof data.zoneId !== 'string') {
            console.warn(`Invalid move data received from ${movingPlayer.username}:`, data);
            return;
        }

        // Check if player is changing zones
        const oldZoneId = movingPlayer.zoneId;
        const newZoneId = data.zoneId;
        const isZoneChange = oldZoneId !== newZoneId;

        // Update server state with explicit numeric values
        movingPlayer.x = Number(data.x);
        movingPlayer.y = Number(data.y);
        movingPlayer.zoneId = newZoneId; // Update zone

        // Store horse information in the player data only if it's an active horse (not stabled)
        if (data.horse && (!data.horse.stable_id || data.horse.is_active === 1)) {
            movingPlayer.horse = data.horse;
        }

        // Store horse position if provided
        if (data.horsePosition) {
            movingPlayer.horsePosition = data.horsePosition;
        }

        // Update player zone tracking for NPC AI
        ServerNpcAI.trackPlayerZone(socket.id, newZoneId);

        // If player changed zones, send them the NPCs in the new zone
        if (isZoneChange) {
            ServerNpcAI.sendNpcPositionsToClient(socket.id, newZoneId);
        }

        // If player changed zones, notify players in the new zone
        if (isZoneChange) {
            console.log(`Player ${movingPlayer.username} changed zones from ${oldZoneId} to ${newZoneId}`);

            // Notify players in the new zone about this player
            for (const otherSocketId in connectedPlayers) {
                if (otherSocketId !== socket.id && connectedPlayers[otherSocketId].zoneId === newZoneId) {
                    // Send player_joined event to players in the new zone
                    // Only include horse data if it's an active horse (not stabled)
                    const horseData = movingPlayer.horse && (!movingPlayer.horse.stable_id || movingPlayer.horse.is_active === 1)
                        ? movingPlayer.horse
                        : null;

                    io.to(otherSocketId).emit('player_joined', {
                        socketId: socket.id,
                        playerData: {
                            username: movingPlayer.username,
                            x: movingPlayer.x,
                            y: movingPlayer.y,
                            zoneId: movingPlayer.zoneId,
                            mountedHorse: data.mountedHorse, // Include horse information
                            horse: horseData, // Include only active horse data
                            horsePosition: horseData ? movingPlayer.horsePosition : null // Include horse position only if horse is active
                        }
                    });
                }
            }
        }

        // Broadcast to other players in the same zone immediately
        // socket.broadcast.emit sends to everyone *except* the originating socket
        for (const otherSocketId in connectedPlayers) {
            if (otherSocketId !== socket.id) { // Don't send back to self
                const otherPlayer = connectedPlayers[otherSocketId];
                // Only send if players are in the same zone
                if (otherPlayer.zoneId === movingPlayer.zoneId) {


                    // Only include horse data if it's an active horse (not stabled)
                    const horseData = movingPlayer.horse && (!movingPlayer.horse.stable_id || movingPlayer.horse.is_active === 1)
                        ? movingPlayer.horse
                        : null;

                    io.to(otherSocketId).emit('player_moved', {
                        socketId: socket.id, // Identify which player moved
                        username: movingPlayer.username,
                        x: movingPlayer.x,
                        y: movingPlayer.y,
                        zoneId: movingPlayer.zoneId,
                        mountedHorse: data.mountedHorse && horseData ? data.mountedHorse : false, // Include horse information only if horse is active
                        horse: horseData, // Include only active horse data
                        horsePosition: horseData ? movingPlayer.horsePosition : null, // Include horse position only if horse is active
                        timestamp: Date.now() // Add timestamp for better synchronization
                    });
                }
            }
        }
    });
    // --- End Player Movement Handling ---

    // --- Horse Position Update Handling ---
    socket.on('horse_position_update', (data) => {
        const player = connectedPlayers[socket.id];
        if (!player) {
            console.warn(`Received horse position update from unknown socket: ${socket.id}`);
            return; // Ignore if player not tracked
        }

        // Basic validation
        if (typeof data.x !== 'number' || typeof data.y !== 'number' || !data.horseId) {
            console.warn(`Invalid horse position data received from ${player.username}:`, data);
            return;
        }

        // Store horse position in player data
        if (!player.horsePositions) {
            player.horsePositions = {};
        }

        // Calculate server-side velocity if not provided
        let velocityX = data.velocityX || 0;
        let velocityY = data.velocityY || 0;

        const prevPosition = player.horsePositions[data.horseId];
        if (prevPosition && data.timestamp && prevPosition.timestamp) {
            const timeDelta = (data.timestamp - prevPosition.timestamp) / 1000;
            if (timeDelta > 0 && (velocityX === 0 && velocityY === 0)) {
                velocityX = (Number(data.x) - prevPosition.x) / timeDelta;
                velocityY = (Number(data.y) - prevPosition.y) / timeDelta;
            }
        }

        // Store the current timestamp
        const currentTimestamp = Date.now();

        player.horsePositions[data.horseId] = {
            x: Number(data.x),
            y: Number(data.y),
            isMounted: data.isMounted || false,
            lastUpdateTime: currentTimestamp,
            velocityX: velocityX,
            velocityY: velocityY,
            timestamp: currentTimestamp
        };

        // Broadcast to other players in the same zone
        for (const otherSocketId in connectedPlayers) {
            if (otherSocketId !== socket.id) { // Don't send back to self
                const otherPlayer = connectedPlayers[otherSocketId];
                // Only send if players are in the same zone
                if (otherPlayer.zoneId === player.zoneId) {
                    io.to(otherSocketId).emit('horse_position_update', {
                        socketId: socket.id, // Identify which player's horse moved
                        username: player.username,
                        horseId: data.horseId,
                        x: data.x,
                        y: data.y,
                        isMounted: data.isMounted || false,
                        velocityX: velocityX, // Use calculated velocity
                        velocityY: velocityY, // Use calculated velocity
                        timestamp: currentTimestamp, // Use the same timestamp for all clients
                        color: data.color || 'brown', // Include horse color
                        horseName: data.horseName || `${player.username}'s horse` // Include horse name
                    });
                }
            }
        }
    });
    // --- End Horse Position Update Handling ---

    // --- Request Horse Position Handling ---
    socket.on('request_horse_position', (data) => {
        const requestingPlayer = connectedPlayers[socket.id];
        if (!requestingPlayer) {
            console.warn(`Horse position request from unknown socket: ${socket.id}`);
            return; // Ignore if player not tracked
        }

        const playerId = data.playerId;
        if (!playerId) {
            console.warn(`Invalid horse position request from ${requestingPlayer.username}: Missing playerId`);
            return;
        }

        console.log(`Player ${requestingPlayer.username} requested horse position for player ID: ${playerId}`);

        // Find the requested player in connected players
        const targetPlayer = connectedPlayers[playerId];
        if (!targetPlayer) {
            console.warn(`Player ${playerId} not found in connected players for horse position request`);
            return;
        }

        // Check if the target player has horse positions stored
        if (targetPlayer.horsePositions) {
            // Send all horse positions for this player
            for (const horseId in targetPlayer.horsePositions) {
                const horsePosition = targetPlayer.horsePositions[horseId];

                // Send the horse position to the requesting player
                // Use a consistent timestamp for all clients
                const currentTimestamp = Date.now();

                socket.emit('horse_position_update', {
                    socketId: playerId,
                    username: targetPlayer.username,
                    horseId: horseId,
                    x: horsePosition.x,
                    y: horsePosition.y,
                    isMounted: horsePosition.isMounted || false,
                    velocityX: horsePosition.velocityX || 0,
                    velocityY: horsePosition.velocityY || 0,
                    timestamp: currentTimestamp,
                    color: targetPlayer.horse?.color || 'brown', // Include horse color
                    horseName: targetPlayer.horse?.name || `${targetPlayer.username}'s horse` // Include horse name
                });
            }
        } else if (targetPlayer.horse) {
            // If no specific positions but player has a horse, use player position as fallback
            // Use a consistent timestamp for all clients
            const currentTimestamp = Date.now();

            socket.emit('horse_position_update', {
                socketId: playerId,
                username: targetPlayer.username,
                horseId: targetPlayer.horse.id,
                x: targetPlayer.x,
                y: targetPlayer.y,
                isMounted: false, // Assume not mounted if no specific position
                timestamp: currentTimestamp,
                color: targetPlayer.horse.color || 'brown', // Include horse color
                horseName: targetPlayer.horse.name || `${targetPlayer.username}'s horse` // Include horse name
            });
        }
    });
    // --- End Request Horse Position Handling ---

    // --- Horse Removal Handling ---
    socket.on('horse_removed', (data) => {
        const player = connectedPlayers[socket.id];
        if (!player) {
            console.warn(`Received horse removal from unknown socket: ${socket.id}`);
            return; // Ignore if player not tracked
        }

        // Basic validation
        if (!data || !data.horseId) {
            console.warn(`Invalid horse removal data received from ${player.username}:`, data);
            return;
        }

        // Remove horse position from player data
        if (player.horsePositions && player.horsePositions[data.horseId]) {
            delete player.horsePositions[data.horseId];
        }

        // Broadcast to other players in the same zone
        for (const otherSocketId in connectedPlayers) {
            if (otherSocketId !== socket.id) { // Don't send back to self
                const otherPlayer = connectedPlayers[otherSocketId];
                // Only send if players are in the same zone
                if (otherPlayer.zoneId === player.zoneId) {
                    io.to(otherSocketId).emit('horse_removed', {
                        socketId: socket.id, // Identify which player's horse was removed
                        username: player.username,
                        horseId: data.horseId
                    });
                }
            }
        }
    });
    // --- End Horse Removal Handling ---

  // --- Chat Message Handling ---
socket.on('send_chat_message', (data) => { // Renamed 'message' to 'data' because we expect an object
    // Check rate limiting
    if (isRateLimited(socket.id, 'send_chat_message')) {
        socket.emit('system_message', { message: 'You are sending messages too quickly. Please wait a moment.' });
        return;
    }

    const sender = connectedPlayers[socket.id];
    if (!sender) {
        console.warn(`Received chat message from unknown socket: ${socket.id}`);
        return; // Ignore if sender not tracked/authenticated
    }

    // --- VALIDATE INCOMING DATA ---
    // Expecting an object like { text: "hello", type: "world" }
    if (typeof data !== 'object' || data === null ||
        typeof data.text !== 'string' || typeof data.type !== 'string') {
        console.warn(`Malformed chat data received from ${sender.username}:`, data);
        // Optionally, send an error back to the sender
        socket.emit('system_message', { message: 'Error: Invalid chat message format.' });
        return;
    }

    const sanitizedMessage = data.text.trim().substring(0, 200); // Limit length
    const messageType = data.type.trim().toLowerCase().substring(0, 20); // Sanitize type, e.g., 'world', 'recruit'

    // Optional: Validate messageType against a list of known types
    const validChatTypes = ['world', 'zone', 'recruit', 'guild']; // Add more as needed
    if (!validChatTypes.includes(messageType)) {
        console.warn(`Invalid message type "${messageType}" from ${sender.username}. Discarding message.`);
        socket.emit('system_message', { message: `Error: Unknown chat type "${messageType}".` });
        return;
    }

    if (!sanitizedMessage) {
        return; // Ignore empty messages after trimming
    }

    console.log(`[Chat] [Type: ${messageType}] ${sender.username} in zone ${sender.zoneId}: ${sanitizedMessage}`);

    // --- Store message in DB (NOW INCLUDES message_type) ---
    db.run(
        `INSERT INTO chat_messages (userId, username, zoneId, message, message_type, timestamp)
         VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`, // Added message_type column
        [sender.userId, sender.username, sender.zoneId, sanitizedMessage, messageType], // Added messageType value
        function(err) {
            if (err) {
                console.error(`Error saving chat message for user ${sender.userId} (type: ${messageType}):`, err.message);
                // Decide if we should still broadcast or notify user of failure
                socket.emit('system_message', { message: 'Error: Could not save your message.' });
            } else {
                const savedMessageId = this.lastID;
                console.log(`Chat message (type: ${messageType}) saved for user ${sender.userId} with ID ${savedMessageId}`);

                // Data to broadcast to clients (NOW INCLUDES type)
                const messageDataForClient = {
                    messageId: savedMessageId,
                    username: sender.username,
                    isAdmin: sender.isAdmin, // Make sure sender object has isAdmin
                    message: sanitizedMessage,
                    type: messageType, // <<< --- INCLUDE THE TYPE HERE
                    timestamp: new Date().toISOString(),
                    zoneId: sender.zoneId // Good to include for potential zone-specific filtering later
                };

                // For now, broadcast all types of messages to everyone.
                // Client-side will filter what to display based on the active tab.
                io.emit('chat_message', messageDataForClient);
                console.log(`[DEBUG] Broadcasted [${messageType}] chat message from ${sender.username} to all.`);
            }
        }
    );
    // --- End Store message ---
});
// --- End Chat Message Handling ---

    // Handler for player list request
    socket.on('request_player_list', () => {
        // Check rate limiting
        if (isRateLimited(socket.id, 'request_player_list')) {
            return; // Silently drop excessive requests
        }

        // Ensure the player is authenticated before proceeding
        const senderData = connectedPlayers[socket.id];
        if (!senderData) {
            console.log(`Player list request from unauthenticated socket: ${socket.id}`);
            // Optionally send an error back, or just ignore
            return;
        }

        console.log(`Player ${senderData.username} requested player list.`);

        // Extract usernames from the connectedPlayers object
        const onlineUsernames = Object.values(connectedPlayers)
                                      .map(player => player.username)
                                      .filter(username => username); // Filter out any potential undefined/null usernames

        // Send the list back to the requesting client
         socket.emit('player_list_response', onlineUsernames);
     });

    // Handler for player count request (for index page)
    socket.on('request_player_count', () => {
        console.log(`Player count requested from socket: ${socket.id}`);

        // Get the current player count
        const playerCount = Object.keys(connectedPlayers).length;

        // Send the count directly to the requesting client
        socket.emit('player_count_update', { count: playerCount });
    });

    // --- Handler for Admin List Request ---
    socket.on('request_admin_list', () => {
        // Ensure the player is authenticated before proceeding
        const senderData = connectedPlayers[socket.id];
        if (!senderData) {
            console.log(`Admin list request from unauthenticated socket: ${socket.id}`);
            // Optionally send an error back, or just ignore
            return;
        }
        console.log(`Player ${senderData.username} requested admin list.`);
        // Send the loaded admin usernames back to the requesting client
        // adminUsernames is loaded at server start
        socket.emit('admin_list_response', adminUsernames);
    });
    // --- End Handler for Admin List Request ---

    // --- Handler for Chat History Request ---
    socket.on('request_chat_history', (requestData) => {
        // Check rate limiting
        if (isRateLimited(socket.id, 'request_chat_history')) {
            return; // Silently drop excessive requests
        }

        // Received chat history request
        const player = connectedPlayers[socket.id];
        if (!player) {
            // Ignore unauthenticated request
             return; // Ignore if player not authenticated
         }

         // Extract chat type from request data (default to 'world' if not specified)
         const chatType = (requestData && requestData.chatType) ? requestData.chatType : 'world';
         console.log(`Chat history requested by ${player.username} for type: ${chatType}`);

         // Build query based on chat type
         let query, params;

         if (chatType === 'zone') {
             // For zone chat, filter by current player's zone and message type
             query = `SELECT messageId, username, message, timestamp, userId, message_type, zoneId
                      FROM chat_messages
                      WHERE message_type = 'zone' AND zoneId = ?
                      ORDER BY timestamp DESC LIMIT ?`;
             params = [player.zoneId, CHAT_HISTORY_LIMIT];
         } else if (chatType === 'world') {
             // For world chat, get all world messages
             query = `SELECT messageId, username, message, timestamp, userId, message_type, zoneId
                      FROM chat_messages
                      WHERE message_type = 'world'
                      ORDER BY timestamp DESC LIMIT ?`;
             params = [CHAT_HISTORY_LIMIT];
         } else {
             // For other chat types (recruit, guild, etc.), filter by message type
             query = `SELECT messageId, username, message, timestamp, userId, message_type, zoneId
                      FROM chat_messages
                      WHERE message_type = ?
                      ORDER BY timestamp DESC LIMIT ?`;
             params = [chatType, CHAT_HISTORY_LIMIT];
         }

        db.all(query, params, (histErr, messages) => {
            if (histErr) {
                console.error(`Error fetching chat history for ${chatType}:`, histErr.message);
                socket.emit('chat_history', []); // Send empty history on error
            } else {
                // Process messages to add isAdmin flag and include all necessary fields
                const processedMessages = messages.map(msg => {
                    return {
                        messageId: msg.messageId,
                        username: msg.username,
                        message: msg.message,
                        timestamp: msg.timestamp,
                        type: msg.message_type || 'world', // Include message type
                        zoneId: msg.zoneId, // Include zone ID
                        isAdmin: adminUsernames.includes(msg.username)
                    };
                });

                // Send chat history to client in chronological order (oldest first)
                socket.emit('chat_history', processedMessages.reverse());
                console.log(`Sent ${processedMessages.length} ${chatType} chat messages to ${player.username}`);
            }
        });
    });
    // --- End Handler for Chat History Request ---

    // --- Handler for Request All Positions ---
    socket.on('request_all_positions', () => {
        const requestingPlayer = connectedPlayers[socket.id];
        if (!requestingPlayer) {
            console.warn(`Position update request from unknown socket: ${socket.id}`);
            return; // Ignore if player not tracked
        }

        console.log(`Player ${requestingPlayer.username} requested all player positions`);

        // Request position updates from all players in the same zone
        for (const otherSocketId in connectedPlayers) {
            if (otherSocketId !== socket.id && connectedPlayers[otherSocketId].zoneId === requestingPlayer.zoneId) {
                // Send a request to each player to update their position
                io.to(otherSocketId).emit('request_position_update');
                console.log(`Requested position update from ${connectedPlayers[otherSocketId].username}`);
            }
        }
    });
    // --- End Handler for Request All Positions ---

    // --- Handler for Request All Horses ---
    socket.on('request_all_horses', () => {
        const requestingPlayer = connectedPlayers[socket.id];
        if (!requestingPlayer) {
            console.warn(`Horse position request from unknown socket: ${socket.id}`);
            return; // Ignore if player not tracked
        }

        console.log(`Player ${requestingPlayer.username} requested all horse positions`);

        // Request horse positions from all players in the same zone
        for (const otherSocketId in connectedPlayers) {
            if (otherSocketId !== socket.id && connectedPlayers[otherSocketId].zoneId === requestingPlayer.zoneId) {
                // Send a request to each player to update their horse position
                io.to(otherSocketId).emit('request_horse_position');
                console.log(`Requested horse position update from ${connectedPlayers[otherSocketId].username}`);
            }
        }
    });

    // --- Handler for Request Player Data ---
    socket.on('request_player_data', (data) => {
        const requestingPlayer = connectedPlayers[socket.id];
        if (!requestingPlayer) {
            console.warn(`Player data request from unknown socket: ${socket.id}`);
            return; // Ignore if player not tracked
        }

        const playerId = data.playerId;
        if (!playerId) {
            console.warn(`Invalid player data request from ${requestingPlayer.username}: Missing playerId`);
            return;
        }

        console.log(`Player ${requestingPlayer.username} requested data for player ID: ${playerId}`);

        // Find the requested player in connected players
        let targetSocketId = null;
        for (const socketId in connectedPlayers) {
            if (socketId === playerId) {
                targetSocketId = socketId;
                break;
            }
        }

        if (!targetSocketId) {
            console.warn(`Player ${playerId} not found in connected players`);
            socket.emit('player_data_response', { playerId, error: 'Player not found' });
            return;
        }

        const targetPlayer = connectedPlayers[targetSocketId];

        // Get player data from database
        db.get('SELECT playerStateJSON FROM player_data WHERE userId = ?', [targetPlayer.userId], (err, row) => {
            if (err) {
                console.error(`Error fetching player data for user ${targetPlayer.userId}:`, err.message);
                socket.emit('player_data_response', { playerId, error: 'Database error' });
                return;
            }

            if (!row || !row.playerStateJSON) {
                console.log(`No saved player data found for user ${targetPlayer.userId}`);
                socket.emit('player_data_response', {
                    playerId,
                    playerData: {
                        username: targetPlayer.username,
                        level: 1, // Default level
                        equipment: {} // Empty equipment
                    }
                });
                return;
            }

            try {
                const playerState = JSON.parse(row.playerStateJSON);
                console.log(`Sending player data for ${targetPlayer.username} to ${requestingPlayer.username}`);

                // Send only necessary data (not the full player state)
                socket.emit('player_data_response', {
                    playerId,
                    playerData: {
                        username: targetPlayer.username,
                        level: playerState.level || 1,
                        equipment: playerState.equipment || {}
                    }
                });
            } catch (parseErr) {
                console.error(`Error parsing player data for user ${targetPlayer.userId}:`, parseErr.message);
                socket.emit('player_data_response', { playerId, error: 'Data parsing error' });
            }
        });
    });
    // --- End Handler for Request Player Data ---

    // --- Handler for Recording Activity ---
    socket.on('record_activity', (activityData) => {
        console.log(`[ACTIVITY DEBUG] Received record_activity event from socket ${socket.id}`);
        console.log(`[ACTIVITY DEBUG] Activity data:`, activityData);

        const player = connectedPlayers[socket.id];
        if (!player) {
            console.warn(`[ACTIVITY DEBUG] Activity record request from unknown socket: ${socket.id}`);
            console.warn(`[ACTIVITY DEBUG] Connected players:`, Object.keys(connectedPlayers));
            socket.emit('activity_recorded', {
                success: false,
                error: 'Player not authenticated'
            });
            return; // Ignore if player not authenticated
        }

        console.log(`[ACTIVITY DEBUG] Player found:`, player);

        const { activityType, activityId, result, metadata } = activityData;

        if (!activityType || !activityId) {
            console.warn(`[ACTIVITY DEBUG] Invalid activity data from ${player.username}: Missing required fields`);
            socket.emit('activity_recorded', {
                success: false,
                error: 'Missing required fields'
            });
            return;
        }

        console.log(`[ACTIVITY DEBUG] Recording activity for ${player.username}: ${activityType} (${activityId}) - Result: ${result || 'completed'}`);

        // Process metadata - handle both string and object formats
        let metadataJson = null;
        if (metadata) {
            console.log("[ACTIVITY DEBUG] Processing metadata:", typeof metadata, metadata);
            if (typeof metadata === 'string') {
                // If metadata is already a JSON string, use it directly
                try {
                    // Validate it's valid JSON by parsing and re-stringifying
                    const parsed = JSON.parse(metadata);
                    metadataJson = JSON.stringify(parsed);
                    console.log("[ACTIVITY DEBUG] Using pre-stringified metadata");
                } catch (e) {
                    // If parsing fails, treat it as a regular string
                    console.log("[ACTIVITY DEBUG] Invalid JSON string, converting to object:", e.message);
                    metadataJson = JSON.stringify({ value: metadata });
                }
            } else {
                // If metadata is an object, stringify it
                metadataJson = JSON.stringify(metadata);
                console.log("[ACTIVITY DEBUG] Stringified object metadata");
            }
        }
        console.log(`[ACTIVITY DEBUG] Final metadata JSON:`, metadataJson);

        // Check if dbActivities is available
        if (!dbActivities) {
            console.error('[ACTIVITY DEBUG] dbActivities is not available!');

            // Try to reconnect to the database
            const DB_ACTIVITIES_PATH = path.join(__dirname, 'data', 'dawnsword_activities.db');
            dbActivities = new sqlite3.Database(DB_ACTIVITIES_PATH, (err) => {
                if (err) {
                    console.error(`[ACTIVITY DEBUG] Error reconnecting to activities database:`, err.message);
                    socket.emit('activity_recorded', {
                        success: false,
                        error: 'Activities database connection failed'
                    });
                    return;
                }

                console.log("[ACTIVITY DEBUG] Reconnected to activities database");
                // Continue with the activity recording
                processActivityRecord();
            });
        } else {
            // Database is already connected, proceed with recording
            processActivityRecord();
        }

        // Function to process the activity record
        function processActivityRecord() {
            console.log('[ACTIVITY DEBUG] Checking activities database tables...');
            dbActivities.get("SELECT name FROM sqlite_master WHERE type='table' AND name='player_activities'", (err, table) => {
                if (err) {
                    console.error("[ACTIVITY DEBUG] Error checking tables:", err);
                    socket.emit('activity_recorded', {
                        success: false,
                        error: 'Database error checking tables'
                    });
                    return;
                }

                if (!table) {
                    console.error("[ACTIVITY DEBUG] player_activities table does not exist!");

                    // Try to create the tables
                    dbActivities.serialize(() => {
                        console.log("[ACTIVITY DEBUG] Attempting to create missing tables...");

                        // Create player_activities table
                        dbActivities.run(`CREATE TABLE IF NOT EXISTS player_activities (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            user_id INTEGER NOT NULL,
                            username TEXT NOT NULL,
                            activity_type TEXT NOT NULL,
                            activity_id TEXT NOT NULL,
                            result TEXT DEFAULT 'completed',
                            metadata TEXT,
                            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                        )`, (err) => {
                            if (err) {
                                console.error("[ACTIVITY DEBUG] Error creating player_activities table:", err);
                                socket.emit('activity_recorded', {
                                    success: false,
                                    error: 'Failed to create activities table'
                                });
                                return;
                            }

                            // Create activity_cooldowns table
                            dbActivities.run(`CREATE TABLE IF NOT EXISTS activity_cooldowns (
                                user_id INTEGER NOT NULL,
                                activity_type TEXT NOT NULL,
                                activity_id TEXT NOT NULL,
                                expires_at INTEGER NOT NULL,
                                PRIMARY KEY (user_id, activity_type, activity_id)
                            )`, (err) => {
                                if (err) {
                                    console.error("[ACTIVITY DEBUG] Error creating activity_cooldowns table:", err);
                                    socket.emit('activity_recorded', {
                                        success: false,
                                        error: 'Failed to create cooldowns table'
                                    });
                                    return;
                                }

                                console.log("[ACTIVITY DEBUG] Successfully created missing tables");
                                // Now proceed with the insert
                                insertActivityRecord();
                            });
                        });
                    });
                } else {
                    console.log("[ACTIVITY DEBUG] player_activities table exists, proceeding with insert");
                    insertActivityRecord();
                }

                // Function to insert the activity record
                function insertActivityRecord() {
                    // Insert activity record - make sure we're using the correct user ID
                    console.log("[ACTIVITY DEBUG] Player info:", player);
                    console.log("[ACTIVITY DEBUG] Inserting activity record with params:", [player.userId, player.username, activityType, activityId, result || 'completed', metadataJson]);

                    dbActivities.run(
                        `INSERT INTO player_activities
                        (user_id, username, activity_type, activity_id, result, metadata, timestamp)
                        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
                        [player.userId, player.username, activityType, activityId, result || 'completed', metadataJson],
                        function(err) {
                            if (err) {
                                console.error('[ACTIVITY DEBUG] Error recording activity via socket:', err);
                                socket.emit('activity_recorded', {
                                    success: false,
                                    error: 'Database error: ' + err.message
                                });
                                return;
                            }

                            console.log(`[ACTIVITY DEBUG] Activity recorded successfully for ${player.username} with ID ${this.lastID}`);

                            // Also update the cooldown table
                            const cooldownHours = getCooldownHours(activityType);
                            if (cooldownHours > 0) {
                                const cooldownSeconds = cooldownHours * 3600;
                                const expiresAt = Math.floor(Date.now() / 1000) + cooldownSeconds;

                                dbActivities.run(
                                    `INSERT INTO activity_cooldowns
                                    (user_id, activity_type, activity_id, expires_at)
                                    VALUES (?, ?, ?, ?)
                                    ON CONFLICT(user_id, activity_type, activity_id)
                                    DO UPDATE SET expires_at = ?`,
                                    [player.userId, activityType, activityId, expiresAt, expiresAt],
                                    function(err) {
                                        if (err) {
                                            console.error('[ACTIVITY DEBUG] Error setting cooldown via socket:', err);
                                            socket.emit('activity_recorded', {
                                                success: true,
                                                activityType,
                                                activityId,
                                                cooldown: null,
                                                error: 'Error setting cooldown: ' + err.message
                                            });
                                        } else {
                                            console.log(`[ACTIVITY DEBUG] Cooldown set for ${player.username}: ${activityType} (${cooldownHours} hours)`);

                                            // Notify the client about the cooldown
                                            socket.emit('activity_recorded', {
                                                success: true,
                                                activityType,
                                                activityId,
                                                cooldown: {
                                                    hours: cooldownHours,
                                                    expiresAt: expiresAt
                                                }
                                            });
                                        }
                                    }
                                );
                            } else {
                                // No cooldown for this activity type
                                socket.emit('activity_recorded', {
                                    success: true,
                                    activityType,
                                    activityId,
                                    cooldown: null
                                });
                            }
                        }
                    );
                }
            });
        }
    });
    // --- Handler for Quest-Related Events ---

    // Handler for quest list request
    socket.on('request_quests', async () => {
        const player = connectedPlayers[socket.id];
        if (!player) {
            console.warn(`Quest list request from unknown socket: ${socket.id}`);
            socket.emit('quests_response', {
                success: false,
                error: 'Player not authenticated'
            });
            return; // Ignore if player not authenticated
        }

        try {
            // Get quests for the player
            const questsData = await QuestManager.getQuestsForUI(player.userId);

            // Send the quests to the player
            socket.emit('quests_response', {
                success: true,
                data: questsData
            });
        } catch (error) {
            console.error('Error getting quests:', error);
            socket.emit('quests_response', {
                success: false,
                error: 'Failed to get quests'
            });
        }
    });

    // Handler for starting a quest
    socket.on('start_quest', async (data) => {
        const player = connectedPlayers[socket.id];
        if (!player) {
            console.warn(`Start quest request from unknown socket: ${socket.id}`);
            socket.emit('quest_started', {
                success: false,
                error: 'Player not authenticated'
            });
            return; // Ignore if player not authenticated
        }

        if (!data || !data.questId) {
            console.warn(`Invalid start quest data from ${player.username}:`, data);
            socket.emit('quest_started', {
                success: false,
                error: 'Invalid quest data'
            });
            return;
        }

        try {
            // Start the quest
            const success = await QuestManager.startQuest(player.userId, data.questId);

            if (success) {
                // Get updated quests for the player
                const questsData = await QuestManager.getQuestsForUI(player.userId);

                // Send success response
                socket.emit('quest_started', {
                    success: true,
                    questId: data.questId,
                    quests: questsData
                });
            } else {
                socket.emit('quest_started', {
                    success: false,
                    error: 'Failed to start quest'
                });
            }
        } catch (error) {
            console.error('Error starting quest:', error);
            socket.emit('quest_started', {
                success: false,
                error: 'Failed to start quest'
            });
        }
    });

    // Handler for resource gathering (for quest progress)
    socket.on('resource_gathered', async (data) => {
        const player = connectedPlayers[socket.id];
        if (!player) {
            console.warn(`Resource gathered event from unknown socket: ${socket.id}`);
            return; // Ignore if player not authenticated
        }

        if (!data || !data.resourceId || !data.amount) {
            console.warn(`Invalid resource gathered data from ${player.username}:`, data);
            return;
        }

        try {
            // Process the gathered resource for quest progress
            const updatedQuests = await QuestManager.processGatheredResource(
                player.userId,
                data.resourceId,
                data.amount
            );

            // If any quests were updated, notify the player
            if (updatedQuests && updatedQuests.length > 0) {
                // Get updated quests for the player
                const questsData = await QuestManager.getQuestsForUI(player.userId);

                // Send quest update notification
                socket.emit('quest_progress_update', {
                    success: true,
                    updatedQuests: updatedQuests,
                    quests: questsData
                });

                // Check for completed quests
                const completedQuests = updatedQuests.filter(q => q.status === 'completed');
                if (completedQuests.length > 0) {
                    // Send quest completion notification
                    socket.emit('quest_completed', {
                        success: true,
                        completedQuests: completedQuests
                    });
                }
            }
        } catch (error) {
            console.error('Error processing gathered resource for quests:', error);
        }
    });

    // --- Handler for Activity Status Request ---
    socket.on('request_activity_status', (data) => {
        console.log(`[ACTIVITY DEBUG] Received request_activity_status event from socket ${socket.id}`);
        console.log(`[ACTIVITY DEBUG] Request data:`, data);

        const player = connectedPlayers[socket.id];
        if (!player) {
            console.warn(`[ACTIVITY DEBUG] Activity status request from unknown socket: ${socket.id}`);
            socket.emit('activity_status_response', {
                success: false,
                error: 'Player not authenticated'
            });
            return; // Ignore if player not authenticated
        }

        const { activityType, activityId } = data;
        if (!activityType || !activityId) {
            console.warn(`[ACTIVITY DEBUG] Invalid activity status request from ${player.username}: Missing required fields`);
            socket.emit('activity_status_response', {
                success: false,
                error: 'Missing required fields'
            });
            return;
        }

        console.log(`[ACTIVITY DEBUG] Checking activity status for ${player.username}: ${activityType} (${activityId})`);

        // Check if dbActivities is available
        if (!dbActivities) {
            console.error('[ACTIVITY DEBUG] dbActivities is not available for status check!');
            socket.emit('activity_status_response', {
                success: false,
                error: 'Database connection not available'
            });
            return;
        }

        // Check if the player is on cooldown for this activity
        dbActivities.get(
            `SELECT expires_at FROM activity_cooldowns
             WHERE user_id = ? AND activity_type = ? AND activity_id = ?`,
            [player.userId, activityType, activityId],
            (err, cooldown) => {
                if (err) {
                    console.error('[ACTIVITY DEBUG] Error checking cooldown:', err);
                    socket.emit('activity_status_response', {
                        success: false,
                        error: 'Database error checking cooldown'
                    });
                    return;
                }

                // Check if cooldown exists and is still active
                const now = Math.floor(Date.now() / 1000);
                const cooldownActive = cooldown && cooldown.expires_at > now;
                const cooldownData = cooldownActive ? {
                    expiresAt: cooldown.expires_at,
                    remainingSeconds: cooldown.expires_at - now
                } : null;

                // Get activity history
                dbActivities.all(
                    `SELECT id, result, timestamp, metadata FROM player_activities
                     WHERE user_id = ? AND activity_type = ? AND activity_id = ?
                     ORDER BY timestamp DESC LIMIT 5`,
                    [player.userId, activityType, activityId],
                    (err, activities) => {
                        if (err) {
                            console.error('[ACTIVITY DEBUG] Error fetching activity history:', err);
                            socket.emit('activity_status_response', {
                                success: false,
                                error: 'Database error fetching history',
                                cooldown: cooldownData // Still include cooldown data if available
                            });
                            return;
                        }

                        // Process activities to parse metadata
                        const processedActivities = activities.map(activity => {
                            let parsedMetadata = null;
                            if (activity.metadata) {
                                try {
                                    parsedMetadata = JSON.parse(activity.metadata);
                                } catch (e) {
                                    console.warn('[ACTIVITY DEBUG] Error parsing activity metadata:', e);
                                }
                            }
                            return {
                                id: activity.id,
                                result: activity.result,
                                timestamp: activity.timestamp,
                                metadata: parsedMetadata
                            };
                        });

                        // Send response with cooldown and history data
                        socket.emit('activity_status_response', {
                            success: true,
                            activityType,
                            activityId,
                            cooldown: cooldownData,
                            activities: processedActivities
                        });

                        console.log(`[ACTIVITY DEBUG] Sent activity status response for ${player.username}: ${processedActivities.length} activities, cooldown: ${cooldownActive ? 'active' : 'inactive'}`);
                    }
                );
            }
        );
    });
    // --- End Handler for Activity Status Request ---

    // --- End Handler for Recording Activity ---

    // --- NPC Position Request Handling ---
    socket.on('request_npc_positions', () => {
        const player = connectedPlayers[socket.id];
        if (!player) {
            console.warn(`Received NPC position request from unknown socket: ${socket.id}`);
            return;
        }

        // Send NPC positions to the client
        ServerNpcAI.sendNpcPositionsToClient(socket.id, player.zoneId);
    });
    // --- End NPC Position Request Handling ---

    // --- Horse Position Update Handling ---
    socket.on('horse_position_update', (data) => {
        const player = connectedPlayers[socket.id];
        if (!player) {
            console.warn(`Received horse position update from unknown socket: ${socket.id}`);
            return;
        }

        // Basic validation
        if (!data || typeof data.x !== 'number' || typeof data.y !== 'number' || !data.horseId) {
            console.warn(`Invalid horse position data received from ${player.username}:`, data);
            return;
        }

        // Throttle debug logging to avoid console spam
        const now = Date.now();
        if (!player._lastHorsePositionLogTime || now - player._lastHorsePositionLogTime > 5000) {
            console.log(`[DEBUG] Received horse position update from ${player.username}: Horse ID ${data.horseId} at (${data.x}, ${data.y})`);
            player._lastHorsePositionLogTime = now;
        }

        // Store the horse position in the player data
        if (!player.horsePositions) {
            player.horsePositions = {};
        }
        // Store the horse position with velocity information for smoother interpolation
        const previousPosition = player.horsePositions[data.horseId];
        const velocityX = previousPosition ? (Number(data.x) - previousPosition.x) / ((now - previousPosition.timestamp) / 1000) : 0;
        const velocityY = previousPosition ? (Number(data.y) - previousPosition.y) / ((now - previousPosition.timestamp) / 1000) : 0;

        // Get the zone ID from the data or use the player's current zone
        const horseZoneId = data.zoneId || player.zoneId;

        player.horsePositions[data.horseId] = {
            x: Number(data.x),
            y: Number(data.y),
            zoneId: horseZoneId,
            isMounted: data.isMounted || false,
            timestamp: now,
            velocityX: velocityX,
            velocityY: velocityY
        };

        // Broadcast the horse position to all other players in the same zone
        // Throttle broadcasts to avoid network congestion
        if (!player._lastHorseBroadcastTimes) {
            player._lastHorseBroadcastTimes = {};
        }

        if (!player._lastHorseBroadcastTimes[data.horseId] ||
            now - player._lastHorseBroadcastTimes[data.horseId] > 100) { // Max 10 broadcasts per second per horse

            player._lastHorseBroadcastTimes[data.horseId] = now;

            for (const otherSocketId in connectedPlayers) {
                if (otherSocketId !== socket.id) { // Don't send back to self
                    const otherPlayer = connectedPlayers[otherSocketId];

                    // Broadcast to ALL players, not just those in the same zone
                    // Throttle debug logging
                    if (!player._lastHorseBroadcastLogTime || now - player._lastHorseBroadcastLogTime > 5000) {
                        console.log(`[DEBUG] Broadcasting horse position for ${player.username}'s horse to ${otherPlayer.username}`);
                        player._lastHorseBroadcastLogTime = now;
                    }

                    const horsePosition = player.horsePositions[data.horseId];
                    io.to(otherSocketId).emit('horse_position_update', {
                        socketId: socket.id,
                        username: player.username,
                        horseId: data.horseId,
                        x: Number(data.x),
                        y: Number(data.y),
                        zoneId: horseZoneId, // Use the horse's zone ID
                        isMounted: data.isMounted || false,
                        velocityX: horsePosition.velocityX || 0,
                        velocityY: horsePosition.velocityY || 0,
                        timestamp: horsePosition.timestamp,
                        color: data.color || player.horse?.color || 'brown', // Include horse color
                        horseName: data.horseName || player.horse?.name || `${player.username}'s horse` // Include horse name
                    });
                }
            }
        }
    });
    // --- Horse Position Request Handling ---
    socket.on('request_horse_position', (data) => {
        const player = connectedPlayers[socket.id];
        if (!player) {
            console.warn(`Received horse position request from unknown socket: ${socket.id}`);
            return;
        }

        // Basic validation
        if (!data || !data.playerId) {
            console.warn(`Invalid horse position request data received from ${player.username}:`, data);
            return;
        }

        console.log(`[DEBUG] Received horse position request from ${player.username} for player ID: ${data.playerId}`);

        // Find the requested player
        let requestedPlayer = null;
        for (const otherSocketId in connectedPlayers) {
            if (connectedPlayers[otherSocketId].id === data.playerId) {
                requestedPlayer = connectedPlayers[otherSocketId];
                break;
            }
        }

        if (!requestedPlayer) {
            console.warn(`Could not find player with ID ${data.playerId} for horse position request`);
            return;
        }

        // Check if the requested player has horse positions
        if (!requestedPlayer.horsePositions) {
            console.warn(`Player ${requestedPlayer.username} has no horse positions`);
            return;
        }

        // Send all horse positions for the requested player
        console.log(`[DEBUG] Sending horse positions for ${requestedPlayer.username} to ${player.username}`);
        for (const horseId in requestedPlayer.horsePositions) {
            const horsePosition = requestedPlayer.horsePositions[horseId];
            socket.emit('horse_position_update', {
                socketId: requestedPlayer.socketId,
                username: requestedPlayer.username,
                horseId: horseId,
                x: horsePosition.x,
                y: horsePosition.y,
                zoneId: requestedPlayer.zoneId, // Include the zone ID
                isMounted: horsePosition.isMounted || false,
                velocityX: horsePosition.velocityX || 0,
                velocityY: horsePosition.velocityY || 0,
                timestamp: horsePosition.timestamp || Date.now(),
                color: requestedPlayer.horse?.color || 'brown', // Include horse color
                horseName: requestedPlayer.horse?.name || `${requestedPlayer.username}'s horse` // Include horse name
            });
        }
    });
    // --- End Horse Position Request Handling ---

    // --- End Horse Position Update Handling ---

    // --- Chat Message Deletion Handling ---
    socket.on('delete_chat_message', (data) => {
        const player = connectedPlayers[socket.id];
        if (!player) {
            console.warn(`Received chat message deletion request from unknown socket: ${socket.id}`);
            return;
        }

        // Check if player is an admin
        if (!player.isAdmin) {
            console.warn(`Non-admin user ${player.username} attempted to delete a chat message`);
            return;
        }

        const messageId = data.messageId;
        if (!messageId) {
            console.warn(`Invalid message deletion request from ${player.username}: Missing messageId`);
            return;
        }

        console.log(`Admin ${player.username} is deleting chat message ID: ${messageId}`);

        // Delete the message from the database
        db.run('DELETE FROM chat_messages WHERE messageId = ?', [messageId], function(err) {
            if (err) {
                console.error(`Error deleting chat message ${messageId}:`, err.message);
                return;
            }

            if (this.changes === 0) {
                console.warn(`No message found with ID ${messageId} to delete`);
                return;
            }

            console.log(`Successfully deleted chat message ${messageId}`);

            // Broadcast the deletion to all connected clients
            io.emit('message_deleted', { messageId });
        });
    });
    // --- End Chat Message Deletion Handling ---
 });

// --- End Socket.IO Setup ---

// --- Express App for Guild Routes ---
const expressApp = express();
expressApp.use(express.json()); // Middleware to parse JSON bodies

// Middleware to make activeSessions available to guildRoutes (if needed for session check)
// This is a simplified way to pass server-level state to Express routes.
// A more robust solution might involve a shared session store or context.
expressApp.use((req, _res, next) => {
    req.activeSessions = activeSessions; // Make activeSessions available
    // A more robust way to get userId from sessionId would be needed here if not using headers
    // For example, if your checkSession middleware in guildRoutes needs to look up sessionId:
    // req.getUserIdFromSession = (sessionId) => activeSessions[sessionId];
    next();
});

expressApp.use('/api/guilds', guildRoutes); // Mount guild routes
// --- End Express App Setup ---


// Start Server
server.listen(PORT, '0.0.0.0', () => {
    console.log(`DawnSword game server running at http://localhost:${PORT}/`);
    console.log('Press Ctrl+C to stop the server');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down server...');
    let closedCount = 0;
    const totalConnections = [db, dbDynamic, dbActivities].filter(d => d).length; // Count how many connections are active, including activities DB

    const closeDb = (dbConn, dbName) => {
        if (dbConn) {
            dbConn.close((err) => {
                if (err) {
                    console.error(`Error closing ${dbName} database:`, err.message);
                } else {
                    console.log(`${dbName} database connection closed.`);
                }
                closedCount++;
                if (closedCount === totalConnections) {
                    console.log("All database connections closed. Exiting.");
                    process.exit(0);
                }
            });
        } else {
             // If a connection wasn't even established, count it as 'closed' for exit logic
             closedCount++;
             if (closedCount === totalConnections) {
                 console.log("Exiting (no active DB connections).");
                 process.exit(0);
             }
        }
    };

    if (totalConnections === 0) {
         console.log("No active database connections to close. Exiting.");
         process.exit(0);
    } else {
        closeDb(db, 'main');
        closeDb(dbDynamic, 'dynamic');
        closeDb(dbActivities, 'activities'); // Close the activities database connection
    }
});

// --- Horse API Handlers ---
/**
 * Handle requests to get available horses
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleHorsesRequest(req, res) {
    console.log(`[API /api/horses] Received request.`);
    console.log(`[API /api/horses] Headers:`, req.headers);

    // Check session from cookie
    let sessionId = null;
    if (req.headers.cookie) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    // If not in cookie, try header
    if (!sessionId && req.headers['x-session-id']) {
        sessionId = req.headers['x-session-id'];
    }

    // If still not found, try query parameter
    if (!sessionId) {
        const parsedUrl = url.parse(req.url, true);
        if (parsedUrl.query.sessionId) {
            sessionId = parsedUrl.query.sessionId;
        }
    }

    console.log(`[API /api/horses] Session ID: ${sessionId}`);
    console.log(`[API /api/horses] Active session? ${!!activeSessions[sessionId]}`);

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Get the location from query parameters
    const parsedUrl = url.parse(req.url, true);
    const location = parsedUrl.query.location || 'avelia';

    // Read horse types from JSON file
    const typesPath = path.join(__dirname, 'data', 'horses', 'horse_types.json');
    const colorsPath = path.join(__dirname, 'data', 'horses', 'horse_colors.json');

    console.log(`[API /api/horses] Reading horse types from: ${typesPath}`);
    console.log(`[API /api/horses] Reading horse colors from: ${colorsPath}`);

    // Read both files
    fs.readFile(typesPath, 'utf8', (err, typesData) => {
        if (err) {
            console.error(`[API /api/horses] Error reading horse_types.json:`, err);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Error reading horse types' }));
            return;
        }

        fs.readFile(colorsPath, 'utf8', (err, colorsData) => {
            if (err) {
                console.error(`[API /api/horses] Error reading horse_colors.json:`, err);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Error reading horse colors' }));
                return;
            }

            try {
                const types = JSON.parse(typesData);
                const colors = JSON.parse(colorsData);
                const horses = [];

                // For each horse type and color combination, create a horse
                Object.values(types).forEach(type => {
                    Object.values(colors).forEach(color => {
                        // Generate a unique ID
                        const id = `${type.id}_${color.id}_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

                        // Calculate the price based on the base price and color modifier
                        const price = Math.round(type.basePrice * color.priceModifier);

                        // Create the horse name
                        const name = `${color.name} ${type.name}`;

                        // Create the description
                        const description = `${color.description} ${type.description}`;

                        // Add the horse to the array
                        horses.push({
                            id: id,
                            horse_id: type.id,
                            name: name,
                            description: description,
                            color: color.name.toLowerCase(),
                            speed: type.baseSpeed,
                            price: price,
                            location: location,
                            available: 1,
                            base_price: type.basePrice,
                            price_modifier: color.priceModifier,
                            color_id: color.id,
                            type_id: type.id,
                            type: type.name,
                            stamina_multiplier: type.stamina_multiplier || 2.0
                        });
                    });
                });

                console.log(`[API /api/horses] Generated ${horses.length} horses for location ${location}`);

                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: true, horses }));
            } catch (parseErr) {
                console.error(`[API /api/horses] Error parsing horse data:`, parseErr);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Error parsing horse data' }));
            }
        });
    });
}

/**
 * Handle requests to purchase a horse
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleHorsePurchaseRequest(req, res) {
    console.log(`[API /api/horses/purchase] Received request.`);

    // Check session from cookie
    let sessionId = null;
    if (req.headers.cookie) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    // If not in cookie, try header
    if (!sessionId && req.headers['x-session-id']) {
        sessionId = req.headers['x-session-id'];
    }

    // If still not found, try query parameter
    if (!sessionId) {
        const parsedUrl = url.parse(req.url, true);
        if (parsedUrl.query.sessionId) {
            sessionId = parsedUrl.query.sessionId;
        }
    }

    console.log(`[API /api/horses/purchase] Session ID: ${sessionId}`);
    console.log(`[API /api/horses/purchase] Active session? ${!!activeSessions[sessionId]}`);

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Get user ID from session
    const userId = activeSessions[sessionId];

    // Parse request body
    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { horseId, colorId, price } = JSON.parse(body);
            console.log(`[API /api/horses/purchase] Request body:`, { horseId, colorId, price });

            if (!horseId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Missing horse ID' }));
                return;
            }

            if (!colorId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Missing color ID' }));
                return;
            }

            // Read the horse types and colors from the files
            const typesPath = path.join(__dirname, 'data', 'horses', 'horse_types.json');
            const colorsPath = path.join(__dirname, 'data', 'horses', 'horse_colors.json');

            console.log(`[API /api/horses/purchase] Reading horse types from: ${typesPath}`);
            console.log(`[API /api/horses/purchase] Reading horse colors from: ${colorsPath}`);

            // Read both files
            fs.readFile(typesPath, 'utf8', (err, typesData) => {
                if (err) {
                    console.error(`[API /api/horses/purchase] Error reading horse_types.json:`, err);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'Error loading horse types' }));
                    return;
                }

                fs.readFile(colorsPath, 'utf8', (err, colorsData) => {
                    if (err) {
                        console.error(`[API /api/horses/purchase] Error reading horse_colors.json:`, err);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: false, error: 'Error loading horse colors' }));
                        return;
                    }

                    try {
                        const types = JSON.parse(typesData);
                        const colors = JSON.parse(colorsData);

                        // Parse the complex horse ID format if needed
                        let baseHorseId = horseId;

                        // Check if it's a complex ID (e.g., riding_horse_brown_1745994144814_850)
                        const parts = horseId.split('_');
                        if (parts.length >= 4 && !isNaN(parts[parts.length - 2])) {
                            // Format: riding_horse_brown_1745994144814_850
                            baseHorseId = parts[0] + '_' + parts[1]; // riding_horse
                            // If colorId wasn't provided, use the one from the complex ID
                            if (!colorId) {
                                colorId = parts[2]; // brown
                            }
                            console.log(`[API /api/horses/purchase] Parsed complex ID: baseHorseId=${baseHorseId}, colorId=${colorId}`);
                        }

                        // Get the horse type and color
                        const horseType = types[baseHorseId];
                        const horseColor = colors[colorId];

                        if (!horseType) {
                            console.error(`[API /api/horses/purchase] Horse type not found:`, baseHorseId);
                            res.writeHead(404, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ success: false, error: 'Horse type not found' }));
                            return;
                        }

                        if (!horseColor) {
                            console.error(`[API /api/horses/purchase] Horse color not found:`, colorId);
                            res.writeHead(404, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ success: false, error: 'Horse color not found' }));
                            return;
                        }

                        // Calculate the expected price
                        const expectedPrice = Math.round(horseType.basePrice * horseColor.priceModifier);

                        // Verify the price matches what we expect (prevent client-side manipulation)
                        if (price && price !== expectedPrice) {
                            console.error(`[API /api/horses/purchase] Price mismatch: expected ${expectedPrice}, got ${price}`);
                            res.writeHead(400, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ success: false, error: 'Invalid price' }));
                            return;
                        }

                        // Create a horse object
                        const horse = {
                            id: `${horseId}_${colorId}_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
                            horse_id: horseType.id,
                            name: `${horseColor.name} ${horseType.name}`,
                            description: horseType.description,
                            color: horseColor.id,
                            speed: horseType.baseSpeed,
                            stamina: 100,
                            price: expectedPrice,
                            stamina_multiplier: horseType.stamina_multiplier || 2.0
                        };

                        // Get user daconsole.log("Selected Code");ta to check gold from playerStateJSON
                        db.get(`SELECT u.username, p.playerStateJSON FROM users u
                                LEFT JOIN player_data p ON u.userId = p.userId
                                WHERE u.userId = ?`, [userId], (err, user) => {
                            if (err) {
                                console.error(`[API /api/horses/purchase] Error fetching user:`, err);
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                return;
                            }

                            if (!user) {
                                res.writeHead(404, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ success: false, error: 'User not found' }));
                                return;
                            }

                            // Parse player state to get gold
                            let playerState = {};
                            try {
                                if (user.playerStateJSON) {
                                    playerState = JSON.parse(user.playerStateJSON);
                                }
                            } catch (error) {
                                console.error(`[API /api/horses/purchase] Error parsing player state:`, error);
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ success: false, error: 'Error parsing player state' }));
                                return;
                            }

                            // Get gold from player state
                            const playerGold = playerState.gold || 0;
                            console.log(`[API /api/horses/purchase] Player gold: ${playerGold}, Horse price: ${horse.price}`);

                            // Check if user has enough gold
                            if (playerGold < horse.price) {
                                res.writeHead(400, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ success: false, error: 'Not enough gold' }));
                                return;
                            }

                            // Check if user already has a horse
                            dbDynamic.get(`SELECT * FROM player_horses WHERE user_id = ? AND is_active = 1`, [userId], (err, existingHorse) => {
                                if (err) {
                                    console.error(`[API /api/horses/purchase] Error checking existing horse:`, err);
                                    res.writeHead(500, { 'Content-Type': 'application/json' });
                                    res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                    return;
                                }

                                // Count how many horses the player has
                                dbDynamic.get(`SELECT COUNT(*) as count FROM player_horses WHERE user_id = ?`, [userId], (err, result) => {
                                    if (err) {
                                        console.error(`[API /api/horses/purchase] Error counting player horses:`, err);
                                        res.writeHead(500, { 'Content-Type': 'application/json' });
                                        res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                        return;
                                    }

                                    const horseCount = result ? result.count : 0;

                                    // Check if player has reached the maximum number of horses (4)
                                    if (horseCount >= 4) {
                                        res.writeHead(400, { 'Content-Type': 'application/json' });
                                        res.end(JSON.stringify({
                                            success: false,
                                            error: 'You already own the maximum number of horses (4). Visit a stable to manage your horses.'
                                        }));
                                        return;
                                    }

                                    // Start a transaction
                                    dbDynamic.run('BEGIN TRANSACTION', (err) => {
                                        if (err) {
                                            console.error(`[API /api/horses/purchase] Error starting transaction:`, err);
                                            res.writeHead(500, { 'Content-Type': 'application/json' });
                                            res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                            return;
                                        }

                                        // Create a new player horse entry
                                        const playerHorseId = horse.id;
                                        const now = Date.now();

                                        // If there's no active horse, set this one as active
                                        const isActive = existingHorse ? 0 : 1;

                                        dbDynamic.run(
                                            `INSERT INTO player_horses (
                                                id, user_id, username, horse_id, name, description, color, speed, stamina,
                                                last_updated, is_active, stamina_multiplier, health, max_health, hunger, max_hunger, status
                                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                                            [
                                                playerHorseId,
                                                userId,
                                                user.username,
                                                horse.horse_id,
                                                horse.name,
                                                horse.description,
                                                horse.color,
                                                horse.speed,
                                                horse.stamina,
                                                now,
                                                isActive,
                                                horse.stamina_multiplier,
                                                100, // Default health
                                                100, // Default max health
                                                100, // Default hunger
                                                100, // Default max hunger
                                                'unmounted' // Default status
                                            ],
                                            function(err) {
                                                if (err) {
                                                    dbDynamic.run('ROLLBACK');
                                                    console.error(`[API /api/horses/purchase] Error creating player horse:`, err);
                                                    res.writeHead(500, { 'Content-Type': 'application/json' });
                                                    res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                                    return;
                                                }

                                                // Deduct gold from player state
                                                // Update playerStateJSON with new gold amount
                                                playerState.gold = playerGold - horse.price;

                                                // Save updated player state
                                                db.run(
                                                    `UPDATE player_data SET playerStateJSON = ? WHERE userId = ?`,
                                                    [JSON.stringify(playerState), userId],
                                                    function(err) {
                                                        if (err) {
                                                            dbDynamic.run('ROLLBACK');
                                                            console.error(`[API /api/horses/purchase] Error updating player gold:`, err);
                                                            res.writeHead(500, { 'Content-Type': 'application/json' });
                                                            res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                                            return;
                                                        }

                                                        // Commit the transaction
                                                        dbDynamic.run('COMMIT', (err) => {
                                                            if (err) {
                                                                dbDynamic.run('ROLLBACK');
                                                                console.error(`[API /api/horses/purchase] Error committing transaction:`, err);
                                                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                                                res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                                                return;
                                                            }

                                                            // Add type property to the horse for client-side use
                                                            horse.type = horseType.name;

                                                            // Return the purchased horse
                                                            res.writeHead(200, { 'Content-Type': 'application/json' });
                                                            res.end(JSON.stringify({
                                                                success: true,
                                                                horse: horse
                                                            }));
                                                        });
                                                    }
                                                );
                                            }
                                        );
                                    });
                                });
                            });
                        });
                    } catch (parseErr) {
                        console.error(`[API /api/horses/purchase] Error parsing JSON data:`, parseErr);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: false, error: 'Error parsing data' }));
                    }
                });
            });
        } catch (error) {
            console.error(`[API /api/horses/purchase] Error parsing request:`, error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Invalid request' }));
        }
    });
}

/**
 * Handle requests to get a player's horse
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handlePlayerHorseRequest(req, res) {
    console.log(`[API /api/horses/player/current] Received request.`);

    // Check session from cookie
    let sessionId = null;
    if (req.headers.cookie) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    // If not in cookie, try header
    if (!sessionId && req.headers['x-session-id']) {
        sessionId = req.headers['x-session-id'];
    }

    // If still not found, try query parameter
    if (!sessionId) {
        const parsedUrl = url.parse(req.url, true);
        if (parsedUrl.query.sessionId) {
            sessionId = parsedUrl.query.sessionId;
        }
    }

    console.log(`[API /api/horses/player/current] Session ID: ${sessionId}`);
    console.log(`[API /api/horses/player/current] Active session? ${!!activeSessions[sessionId]}`);

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Get user ID from session
    const userId = activeSessions[sessionId];

    // Get the player's active horse that is not stabled
    dbDynamic.get(`SELECT * FROM player_horses WHERE user_id = ? AND is_active = 1 AND stable_id IS NULL`, [userId], (err, horse) => {
        if (err) {
            console.error(`[API /api/horses/player/current] Error fetching player horse:`, err);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Database error' }));
            return;
        }

        if (!horse) {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true, hasHorse: false }));
            return;
        }

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: true, hasHorse: true, horse }));
    });
}
/**
 * Handle requests to get a specific horse by ID
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 * @param {string} horseId - The ID of the horse to get
 */
function handleHorseByIdRequest(req, res, horseId) {
    console.log(`[API /api/horses/${horseId}] Received request.`);

    // Check session from cookie
    let sessionId = null;
    if (req.headers.cookie) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    // If not in cookie, try header
    if (!sessionId && req.headers['x-session-id']) {
        sessionId = req.headers['x-session-id'];
    }

    // If still not found, try query parameter
    if (!sessionId) {
        const parsedUrl = url.parse(req.url, true);
        if (parsedUrl.query.sessionId) {
            sessionId = parsedUrl.query.sessionId;
        }
    }

    console.log(`[API /api/horses/${horseId}] Session ID: ${sessionId}`);
    console.log(`[API /api/horses/${horseId}] Active session? ${!!activeSessions[sessionId]}`);

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // First check if it's a stable horse
    dbDynamic.get(`SELECT * FROM stable_horses WHERE id = ?`, [horseId], (err, stableHorse) => {
        if (err) {
            console.error(`[API /api/horses/${horseId}] Error fetching stable horse:`, err);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Database error' }));
            return;
        }

        if (stableHorse) {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true, horse: stableHorse, type: 'stable' }));
            return;
        }

        // If not a stable horse, check if it's a player horse
        dbDynamic.get(`SELECT * FROM player_horses WHERE id = ?`, [horseId], (err, playerHorse) => {
            if (err) {
                console.error(`[API /api/horses/${horseId}] Error fetching player horse:`, err);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Database error' }));
                return;
            }

            if (playerHorse) {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: true, horse: playerHorse, type: 'player' }));
                return;
            }

            // Horse not found
            res.writeHead(404, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Horse not found' }));
        });
    });
}

/**
 * Handle requests to get horses stabled at a specific location
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 * @param {string} stableId - The ID of the stable
 */
function handleStableHorsesRequest(req, res, stableId) {
    console.log(`[API /api/horses/stable/${stableId}] Received request.`);

    // Check session from cookie
    let sessionId = null;
    if (req.headers.cookie) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    // If not in cookie, try header
    if (!sessionId && req.headers['x-session-id']) {
        sessionId = req.headers['x-session-id'];
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Get user ID from session
    const userId = activeSessions[sessionId];

    // Get the player's active horse
    dbDynamic.get(`SELECT * FROM player_horses WHERE user_id = ? AND is_active = 1`, [userId], (err, activeHorse) => {
        if (err) {
            console.error(`[API /api/horses/stable/${stableId}] Error fetching active horse:`, err);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Database error' }));
            return;
        }

        // Get horses stabled at this location
        dbDynamic.all(`SELECT * FROM player_horses WHERE user_id = ? AND stable_id = ?`, [userId, stableId], (err, stabledHorses) => {
            if (err) {
                console.error(`[API /api/horses/stable/${stableId}] Error fetching stabled horses:`, err);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Database error' }));
                return;
            }

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                success: true,
                horses: stabledHorses || [],
                activeHorse: activeHorse
            }));
        });
    });
}

/**
 * Handle requests to store a horse at a stable
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleStoreHorseRequest(req, res) {
    console.log(`[API /api/horses/stable/store] Received request.`);

    // Check session from cookie
    let sessionId = null;
    if (req.headers.cookie) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    // If not in cookie, try header
    if (!sessionId && req.headers['x-session-id']) {
        sessionId = req.headers['x-session-id'];
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Get user ID from session
    const userId = activeSessions[sessionId];

    // Parse request body
    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { horseId, stableId } = JSON.parse(body);

            if (!horseId || !stableId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Horse ID and Stable ID are required' }));
                return;
            }

            // First, verify the horse belongs to the player
            dbDynamic.get(`SELECT * FROM player_horses WHERE id = ? AND user_id = ?`, [horseId, userId], (err, horse) => {
                if (err) {
                    console.error(`[API /api/horses/stable/store] Error checking horse ownership:`, err);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'Database error' }));
                    return;
                }

                if (!horse) {
                    res.writeHead(404, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'Horse not found or does not belong to player' }));
                    return;
                }

                // Update the horse's stable_id and set it to inactive
                dbDynamic.run(
                    `UPDATE player_horses SET stable_id = ?, is_active = 0, last_updated = ? WHERE id = ?`,
                    [stableId, Date.now(), horseId],
                    function(err) {
                        if (err) {
                            console.error(`[API /api/horses/stable/store] Error storing horse at stable:`, err);
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ success: false, error: 'Database error' }));
                            return;
                        }

                        // Get the updated horse data
                        dbDynamic.get(`SELECT * FROM player_horses WHERE id = ?`, [horseId], (err, updatedHorse) => {
                            if (err) {
                                console.error(`[API /api/horses/stable/store] Error fetching updated horse:`, err);
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                return;
                            }

                            res.writeHead(200, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ success: true, horse: updatedHorse }));
                        });
                    }
                );
            });
        } catch (error) {
            console.error(`[API /api/horses/stable/store] Error parsing request:`, error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Invalid request' }));
        }
    });
}

/**
 * Handle requests to sell a horse at a stable
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleSellHorseRequest(req, res) {
    console.log(`[API /api/horses/stable/sell] Received request.`);

    // Check session from cookie
    let sessionId = null;
    if (req.headers.cookie) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    // If not in cookie, try header
    if (!sessionId && req.headers['x-session-id']) {
        sessionId = req.headers['x-session-id'];
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Get user ID from session
    const userId = activeSessions[sessionId];

    // Parse request body
    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { horseId, stableId } = JSON.parse(body);

            if (!horseId || !stableId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Horse ID and Stable ID are required' }));
                return;
            }

            // Convert userId to number for comparison since it might be stored as string in some places
            const userIdNum = Number(userId);

            // Get user data from player_data table
            db.get('SELECT playerStateJSON FROM player_data WHERE userId = ?', [userIdNum], (err, userData) => {
                if (err) {
                    console.error(`[API /api/horses/stable/sell] Error fetching user data:`, err);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'Database error' }));
                    return;
                }

                if (!userData || !userData.playerStateJSON) {
                    res.writeHead(404, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'User data not found' }));
                    return;
                }

                // Parse the player state JSON
                let playerState;
                try {
                    playerState = JSON.parse(userData.playerStateJSON);
                } catch (parseErr) {
                    console.error(`[API /api/horses/stable/sell] Error parsing player state:`, parseErr);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'Error parsing player data' }));
                    return;
                }
                dbDynamic.get(`SELECT * FROM player_horses WHERE id = ? AND user_id = ? AND stable_id = ?`,
                    [horseId, userIdNum, stableId], (err, horse) => {
                    if (err) {
                        console.error(`[API /api/horses/stable/sell] Error checking horse:`, err);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: false, error: 'Database error' }));
                        return;
                    }

                    if (!horse) {
                        res.writeHead(404, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: false, error: 'Horse not found in this stable or does not belong to player' }));
                        return;
                    }

                    // Calculate sell price (50% of original value)
                    // We need to get the original horse price from horse_types.json and horse_colors.json
                    const typesPath = path.join(__dirname, 'data', 'horses', 'horse_types.json');
                    const colorsPath = path.join(__dirname, 'data', 'horses', 'horse_colors.json');

                    console.log(`[API /api/horses/stable/sell] Reading horse data for ${horse.id}, type: ${horse.horse_id}, color: ${horse.color}`);

                    // Read both files
                    fs.readFile(typesPath, 'utf8', (err, typesData) => {
                        if (err) {
                            console.error(`[API /api/horses/stable/sell] Error reading horse_types.json:`, err);
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ success: false, error: 'Error reading horse type data' }));
                            return;
                        }

                        fs.readFile(colorsPath, 'utf8', (err, colorsData) => {
                            if (err) {
                                console.error(`[API /api/horses/stable/sell] Error reading horse_colors.json:`, err);
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ success: false, error: 'Error reading horse color data' }));
                                return;
                            }

                            try {
                                const types = JSON.parse(typesData);
                                const colors = JSON.parse(colorsData);

                                // Parse the complex horse ID to get the base type
                                const parts = horse.horse_id.split('_');
                                let baseHorseId = horse.horse_id;

                                // Check if it's a complex ID with timestamp
                                if (parts.length >= 4 && !isNaN(parts[parts.length - 2])) {
                                    // Format: riding_horse_brown_1745994144814_850
                                    baseHorseId = parts[0] + '_' + parts[1]; // riding_horse
                                }

                                console.log(`[API /api/horses/stable/sell] Parsed horse ID: ${baseHorseId}`);

                                // Get the horse type and color
                                const horseType = types[baseHorseId] || types[horse.horse_id];
                                const horseColor = colors[horse.color];

                                console.log(`[API /api/horses/stable/sell] Horse type:`, horseType);
                                console.log(`[API /api/horses/stable/sell] Horse color:`, horseColor);

                                // We'll calculate the price based on horse type and color

                                // Variables for price calculation
                                let originalPrice = 0;
                                let sellValueBeforeTax = 0;

                                if (horseType && horseType.basePrice) {
                                    // Store the original purchase price
                                    originalPrice = horseType.basePrice;

                                    // Apply color modifier if available
                                    if (horseColor && horseColor.priceModifier) {
                                        originalPrice = Math.round(originalPrice * horseColor.priceModifier);
                                    }

                                    // Log the original price for debugging
                                    console.log(`[API /api/horses/stable/sell] Original price: ${originalPrice}`);

                                    // Sell for 50% of original price
                                    sellValueBeforeTax = Math.floor(originalPrice * 0.5);
                                } else {
                                    // Default values if horse type not found
                                    originalPrice = 100;
                                    sellValueBeforeTax = 50;
                                }

                                console.log(`[API /api/horses/stable/sell] Sell value before tax: ${sellValueBeforeTax}`);

                                // Calculate 10% tax
                                const taxAmount = Math.floor(sellValueBeforeTax * 0.1);
                                console.log(`[API /api/horses/stable/sell] Tax amount: ${taxAmount}`);

                                // Final price after tax
                                const sellPrice = sellValueBeforeTax - taxAmount;
                                console.log(`[API /api/horses/stable/sell] Final sell price: ${sellPrice}`);

                                // Simplify the process - first delete the horse
                                console.log(`[API /api/horses/stable/sell] Deleting horse ${horseId} for user ${userIdNum}`);
                                dbDynamic.run('DELETE FROM player_horses WHERE id = ? AND user_id = ? AND stable_id = ?',
                                    [horseId, userIdNum, stableId], function(err) {
                                    if (err) {
                                        console.error(`[API /api/horses/stable/sell] Error deleting horse:`, err);
                                        res.writeHead(500, { 'Content-Type': 'application/json' });
                                        res.end(JSON.stringify({ success: false, error: 'Error deleting horse: ' + err.message }));
                                        return;
                                    }

                                    if (this.changes === 0) {
                                        console.error(`[API /api/horses/stable/sell] No horse was deleted. Horse might not exist or belong to user.`);
                                        res.writeHead(404, { 'Content-Type': 'application/json' });
                                        res.end(JSON.stringify({ success: false, error: 'Horse not found or already sold' }));
                                        return;
                                    }

                                    console.log(`[API /api/horses/stable/sell] Horse deleted successfully. Adding ${sellPrice} gold to user ${userIdNum}`);

                                    // Update the player's gold in the playerState
                                    console.log(`[API /api/horses/stable/sell] Current player gold: ${playerState.gold || 0}`);

                                    // Make sure gold exists in playerState
                                    if (typeof playerState.gold === 'undefined') {
                                        playerState.gold = 0;
                                    }

                                    // Add the sell price to the player's gold
                                    playerState.gold += sellPrice;

                                    console.log(`[API /api/horses/stable/sell] New player gold: ${playerState.gold}`);

                                    // Update the playerStateJSON in the database
                                    db.run('UPDATE player_data SET playerStateJSON = ? WHERE userId = ?',
                                        [JSON.stringify(playerState), userIdNum], function(err) {
                                        if (err) {
                                            console.error(`[API /api/horses/stable/sell] Error updating player state:`, err);
                                            res.writeHead(500, { 'Content-Type': 'application/json' });
                                            res.end(JSON.stringify({ success: false, error: 'Error updating player state: ' + err.message }));
                                            return;
                                        }

                                        if (this.changes === 0) {
                                            console.error(`[API /api/horses/stable/sell] No player state was updated. User might not exist.`);
                                            res.writeHead(404, { 'Content-Type': 'application/json' });
                                            res.end(JSON.stringify({ success: false, error: 'User not found' }));
                                            return;
                                        }

                                        console.log(`[API /api/horses/stable/sell] Gold updated successfully. Returning success response.`);

                                        // Return the result
                                        res.writeHead(200, { 'Content-Type': 'application/json' });
                                        res.end(JSON.stringify({
                                            success: true,
                                            horse: horse,
                                            goldAmount: sellPrice,
                                            taxAmount: taxAmount,
                                            totalBeforeTax: sellValueBeforeTax,
                                            originalPrice: originalPrice,
                                            message: `You sold your ${horse.color} horse for ${sellPrice} gold (after 10% tax of ${taxAmount} gold).`
                                        }));
                                    });
                                });
                            } catch (parseErr) {
                                console.error(`[API /api/horses/stable/sell] Error parsing horse data:`, parseErr);
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ success: false, error: 'Error parsing horse data' }));
                            }
                        });
                    });
                });
            });
        } catch (error) {
            console.error(`[API /api/horses/stable/sell] Error parsing request:`, error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Invalid request' }));
        }
    });
}

/**
 * Handle requests to activate a horse from a stable
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleActivateHorseRequest(req, res) {
    console.log(`[API /api/horses/stable/activate] Received request.`);

    // Check session from cookie
    let sessionId = null;
    if (req.headers.cookie) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    // If not in cookie, try header
    if (!sessionId && req.headers['x-session-id']) {
        sessionId = req.headers['x-session-id'];
    }

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Get user ID from session
    const userId = activeSessions[sessionId];

    // Parse request body
    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const { horseId, stableId } = JSON.parse(body);

            if (!horseId || !stableId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Horse ID and Stable ID are required' }));
                return;
            }

            // First, verify the horse belongs to the player and is in the specified stable
            dbDynamic.get(
                `SELECT * FROM player_horses WHERE id = ? AND user_id = ? AND stable_id = ?`,
                [horseId, userId, stableId],
                (err, horse) => {
                    if (err) {
                        console.error(`[API /api/horses/stable/activate] Error checking horse in stable:`, err);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: false, error: 'Database error' }));
                        return;
                    }

                    if (!horse) {
                        res.writeHead(404, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: false, error: 'Horse not found in this stable or does not belong to player' }));
                        return;
                    }

                    // Begin a transaction to update horse active status
                    dbDynamic.run('BEGIN TRANSACTION', (err) => {
                        if (err) {
                            console.error(`[API /api/horses/stable/activate] Error beginning transaction:`, err);
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ success: false, error: 'Database error' }));
                            return;
                        }

                        // First, set all horses for this user to inactive
                        dbDynamic.run(
                            'UPDATE player_horses SET is_active = 0 WHERE user_id = ?',
                            [userId],
                            (err) => {
                                if (err) {
                                    console.error(`[API /api/horses/stable/activate] Error deactivating horses:`, err);
                                    dbDynamic.run('ROLLBACK');
                                    res.writeHead(500, { 'Content-Type': 'application/json' });
                                    res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                    return;
                                }

                                // Then, set the specified horse to active and clear its stable_id
                                dbDynamic.run(
                                    'UPDATE player_horses SET is_active = 1, stable_id = NULL, last_updated = ? WHERE id = ?',
                                    [Date.now(), horseId],
                                    (err) => {
                                        if (err) {
                                            console.error(`[API /api/horses/stable/activate] Error activating horse from stable:`, err);
                                            dbDynamic.run('ROLLBACK');
                                            res.writeHead(500, { 'Content-Type': 'application/json' });
                                            res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                            return;
                                        }

                                        // Commit the transaction
                                        dbDynamic.run('COMMIT', (err) => {
                                            if (err) {
                                                console.error(`[API /api/horses/stable/activate] Error committing transaction:`, err);
                                                dbDynamic.run('ROLLBACK');
                                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                                res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                                return;
                                            }

                                            // Get the updated horse data
                                            dbDynamic.get('SELECT * FROM player_horses WHERE id = ?', [horseId], (err, updatedHorse) => {
                                                if (err) {
                                                    console.error(`[API /api/horses/stable/activate] Error fetching updated horse:`, err);
                                                    res.writeHead(500, { 'Content-Type': 'application/json' });
                                                    res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                                    return;
                                                }

                                                // Find the user's socket ID to broadcast the horse activation
                                                let userSocketId = null;
                                                for (const socketId in connectedPlayers) {
                                                    if (connectedPlayers[socketId].id === userId) {
                                                        userSocketId = socketId;
                                                        break;
                                                    }
                                                }

                                                // Update the player's horse in connectedPlayers
                                                if (userSocketId && connectedPlayers[userSocketId]) {
                                                    connectedPlayers[userSocketId].horse = updatedHorse;
                                                    console.log(`Updated horse for player ${connectedPlayers[userSocketId].username}:`, updatedHorse);

                                                    // Get the player's username and zone
                                                    const username = connectedPlayers[userSocketId].username;
                                                    const zoneId = connectedPlayers[userSocketId].zoneId;
                                                    const playerX = connectedPlayers[userSocketId].x || 0;
                                                    const playerY = connectedPlayers[userSocketId].y || 0;

                                                    console.log(`[DEBUG] Player ${username} activated horse in zone ${zoneId} at position (${playerX}, ${playerY})`);

                                                    // Broadcast to ALL players, not just those in the same zone
                                                    // This ensures everyone gets the update even if they change zones
                                                    for (const otherSocketId in connectedPlayers) {
                                                        // Don't send to self (though the client will ignore it anyway)
                                                        if (otherSocketId !== userSocketId) {
                                                            io.to(otherSocketId).emit('player_horse_activated', {
                                                                socketId: userSocketId,
                                                                username: username,
                                                                horse: updatedHorse,
                                                                zoneId: zoneId,
                                                                x: playerX,
                                                                y: playerY
                                                            });
                                                            console.log(`Notified player ${connectedPlayers[otherSocketId].username} about horse activation`);
                                                        }
                                                    }

                                                    // Also send a horse position update to ensure everyone has the correct position
                                                    for (const otherSocketId in connectedPlayers) {
                                                        if (otherSocketId !== userSocketId) {
                                                            io.to(otherSocketId).emit('horse_position_update', {
                                                                socketId: userSocketId,
                                                                username: username,
                                                                horseId: updatedHorse.id,
                                                                x: playerX,
                                                                y: playerY,
                                                                isMounted: false,
                                                                velocityX: 0,
                                                                velocityY: 0,
                                                                timestamp: Date.now(),
                                                                color: updatedHorse.color || 'brown',
                                                                horseName: updatedHorse.name || `${username}'s horse`
                                                            });
                                                        }
                                                    }
                                                }

                                                res.writeHead(200, { 'Content-Type': 'application/json' });
                                                res.end(JSON.stringify({ success: true, horse: updatedHorse }));
                                            });
                                        });
                                    }
                                );
                            }
                        );
                    });
                }
            );
        } catch (error) {
            console.error(`[API /api/horses/stable/activate] Error parsing request:`, error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Invalid request' }));
        }
    });
}

/**
 * Handle requests to rename a player's horse
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleRenameHorseRequest(req, res) {
    console.log(`[API /api/horses/player/rename] Received request.`);

    // Check session
    const sessionId = getSessionId(req);
    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Get user ID from session
    const userId = activeSessions[sessionId];

    // Parse request body
    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const data = JSON.parse(body);
            const { name } = data;

            if (!name || name.trim() === '') {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Horse name cannot be empty' }));
                return;
            }

            // Sanitize the name (limit to 30 chars)
            const sanitizedName = name.trim().substring(0, 30);

            // Check if player has a horse
            dbDynamic.get(`SELECT * FROM player_horses WHERE user_id = ?`, [userId], (err, horse) => {
                if (err) {
                    console.error(`[API /api/horses/player/rename] Error fetching player horse:`, err);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'Database error' }));
                    return;
                }

                if (!horse) {
                    res.writeHead(404, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'You do not own a horse' }));
                    return;
                }

                // Update the horse name
                dbDynamic.run(
                    `UPDATE player_horses SET name = ?, last_updated = ? WHERE user_id = ?`,
                    [sanitizedName, Date.now(), userId],
                    function(err) {
                        if (err) {
                            console.error(`[API /api/horses/player/rename] Error updating horse name:`, err);
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ success: false, error: 'Database error' }));
                            return;
                        }

                        // Get the updated horse
                        dbDynamic.get(`SELECT * FROM player_horses WHERE user_id = ?`, [userId], (err, updatedHorse) => {
                            if (err) {
                                console.error(`[API /api/horses/player/rename] Error fetching updated horse:`, err);
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                return;
                            }

                            res.writeHead(200, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ success: true, horse: updatedHorse }));
                        });
                    }
                );
            });
        } catch (error) {
            console.error(`[API /api/horses/player/rename] Error parsing request:`, error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Invalid request data' }));
        }
    });
}

/**
 * Handle requests to update a player's horse stats
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleUpdateHorseStatsRequest(req, res) {
    console.log(`[API /api/horses/player/update-stats] Received request.`);

    // Check session
    const sessionId = getSessionId(req);
    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Get user ID from session
    const userId = activeSessions[sessionId];

    // Parse request body
    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const data = JSON.parse(body);
            const { health, hunger, stamina, status } = data;

            // Check if player has a horse
            dbDynamic.get(`SELECT * FROM player_horses WHERE user_id = ?`, [userId], (err, horse) => {
                if (err) {
                    console.error(`[API /api/horses/player/update-stats] Error fetching player horse:`, err);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'Database error' }));
                    return;
                }

                if (!horse) {
                    res.writeHead(404, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'You do not own a horse' }));
                    return;
                }

                // Build the update query dynamically
                const updateFields = [];
                const updateValues = [];

                // Check each possible stat and add it to the update if provided
                if (health !== undefined) {
                    const maxHealth = horse.max_health || 100;
                    updateFields.push('health = ?');
                    updateValues.push(Math.max(0, Math.min(maxHealth, health)));
                }

                if (hunger !== undefined) {
                    const maxHunger = horse.max_hunger || 100;
                    updateFields.push('hunger = ?');
                    updateValues.push(Math.max(0, Math.min(maxHunger, hunger)));
                }

                if (stamina !== undefined) {
                    const maxStamina = horse.max_stamina || 100;
                    updateFields.push('stamina = ?');
                    updateValues.push(Math.max(0, Math.min(maxStamina, stamina)));
                }

                if (status !== undefined) {
                    updateFields.push('status = ?');
                    updateValues.push(status);
                }

                // Always update the last_updated timestamp
                updateFields.push('last_updated = ?');
                updateValues.push(Date.now());

                // If no fields to update, just return the current horse
                if (updateFields.length === 1) { // Only last_updated
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: true, horse }));
                    return;
                }

                // Add the user ID for the WHERE clause
                updateValues.push(userId);

                // Execute the update
                const query = `
                    UPDATE player_horses
                    SET ${updateFields.join(', ')}
                    WHERE user_id = ?
                `;

                dbDynamic.run(query, updateValues, function(err) {
                    if (err) {
                        console.error(`[API /api/horses/player/update-stats] Error updating horse stats:`, err);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: false, error: 'Database error' }));
                        return;
                    }

                    // Get the updated horse
                    dbDynamic.get(`SELECT * FROM player_horses WHERE user_id = ?`, [userId], (err, updatedHorse) => {
                        if (err) {
                            console.error(`[API /api/horses/player/update-stats] Error fetching updated horse:`, err);
                            res.writeHead(500, { 'Content-Type': 'application/json' });
                            res.end(JSON.stringify({ success: false, error: 'Database error' }));
                            return;
                        }

                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: true, horse: updatedHorse }));
                    });
                });
            });
        } catch (error) {
            console.error(`[API /api/horses/player/update-stats] Error parsing request:`, error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Invalid request data' }));
        }
    });
}

// Helper function to get session ID from request
function getSessionId(req) {
    let sessionId = null;

    // Try to get from cookie
    if (req.headers.cookie) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    // If not in cookie, try header
    if (!sessionId && req.headers['x-session-id']) {
        sessionId = req.headers['x-session-id'];
    }

    return sessionId;
}



/**
 * Handle requests to get all horses owned by a player
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handlePlayerHorsesRequest(req, res) {
    console.log(`[API /api/horses/player/all] Received request.`);

    // Check session
    const sessionId = getSessionId(req);
    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Get user ID from session
    const userId = activeSessions[sessionId];

    // Get all horses owned by the player
    dbDynamic.all(`SELECT * FROM player_horses WHERE user_id = ? ORDER BY is_active DESC, last_updated DESC`, [userId], (err, horses) => {
        if (err) {
            console.error(`[API /api/horses/player/all] Error fetching player horses:`, err);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Database error' }));
            return;
        }

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: true, horses: horses || [] }));
    });
}

/**
 * Handle requests to set a horse as the active horse
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleSetActiveHorseRequest(req, res) {
    console.log(`[API /api/horses/player/set-active] Received request.`);

    // Check session
    const sessionId = getSessionId(req);
    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Get user ID from session
    const userId = activeSessions[sessionId];

    // Parse request body
    let body = '';
    req.on('data', chunk => { body += chunk.toString(); });
    req.on('end', () => {
        try {
            const data = JSON.parse(body);
            const { horseId } = data;

            if (!horseId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Horse ID is required' }));
                return;
            }

            // Verify the horse belongs to the player
            dbDynamic.get(`SELECT * FROM player_horses WHERE id = ? AND user_id = ?`, [horseId, userId], (err, horse) => {
                if (err) {
                    console.error(`[API /api/horses/player/set-active] Error checking horse ownership:`, err);
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'Database error' }));
                    return;
                }

                if (!horse) {
                    res.writeHead(404, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ success: false, error: 'Horse not found or does not belong to you' }));
                    return;
                }

                // Begin a transaction to update horse active status
                dbDynamic.run('BEGIN TRANSACTION', (err) => {
                    if (err) {
                        console.error(`[API /api/horses/player/set-active] Error beginning transaction:`, err);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: false, error: 'Database error' }));
                        return;
                    }

                    // First, set all horses for this user to inactive
                    dbDynamic.run(
                        'UPDATE player_horses SET is_active = 0 WHERE user_id = ?',
                        [userId],
                        (err) => {
                            if (err) {
                                console.error(`[API /api/horses/player/set-active] Error deactivating horses:`, err);
                                dbDynamic.run('ROLLBACK');
                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                return;
                            }

                            // Then, set the specified horse to active
                            dbDynamic.run(
                                'UPDATE player_horses SET is_active = 1, last_updated = ? WHERE id = ?',
                                [Date.now(), horseId],
                                (err) => {
                                    if (err) {
                                        console.error(`[API /api/horses/player/set-active] Error activating horse:`, err);
                                        dbDynamic.run('ROLLBACK');
                                        res.writeHead(500, { 'Content-Type': 'application/json' });
                                        res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                        return;
                                    }

                                    // Commit the transaction
                                    dbDynamic.run('COMMIT', (err) => {
                                        if (err) {
                                            console.error(`[API /api/horses/player/set-active] Error committing transaction:`, err);
                                            dbDynamic.run('ROLLBACK');
                                            res.writeHead(500, { 'Content-Type': 'application/json' });
                                            res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                            return;
                                        }

                                        // Get the updated horse
                                        dbDynamic.get(`SELECT * FROM player_horses WHERE id = ?`, [horseId], (err, updatedHorse) => {
                                            if (err) {
                                                console.error(`[API /api/horses/player/set-active] Error fetching updated horse:`, err);
                                                res.writeHead(500, { 'Content-Type': 'application/json' });
                                                res.end(JSON.stringify({ success: false, error: 'Database error' }));
                                                return;
                                            }

                                            res.writeHead(200, { 'Content-Type': 'application/json' });
                                            res.end(JSON.stringify({ success: true, horse: updatedHorse }));
                                        });
                                    });
                                }
                            );
                        }
                    );
                });
            });
        } catch (error) {
            console.error(`[API /api/horses/player/set-active] Error parsing request:`, error);
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Invalid request data' }));
        }
    });
}

/**
 * Handle requests to get horse colors
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleHorseColorsRequest(req, res) {
    console.log(`[API /api/horses/colors] Received request.`);

    // Only allow GET requests
    if (req.method !== 'GET') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Method not allowed' }));
        return;
    }

    // For debugging, log the headers
    console.log(`[API /api/horses/colors] Headers:`, req.headers);

    // Read the horse colors from the file
    const colorsPath = path.join(__dirname, 'data', 'horses', 'horse_colors.json');
    console.log(`[API /api/horses/colors] Reading colors from: ${colorsPath}`);

    fs.readFile(colorsPath, 'utf8', (err, data) => {
        if (err) {
            console.error(`[API /api/horses/colors] Error reading horse_colors.json:`, err);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Error loading horse colors' }));
            return;
        }

        try {
            const colors = JSON.parse(data);
            console.log(`[API /api/horses/colors] Successfully loaded ${Object.keys(colors).length} horse colors`);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true, colors }));
        } catch (parseErr) {
            console.error(`[API /api/horses/colors] Error parsing horse_colors.json:`, parseErr);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Error parsing horse colors data' }));
        }
    });
}

/**
 * Handle requests to get horse types
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 */
function handleHorseTypesRequest(req, res) {
    console.log(`[API /api/horses/types] Received request.`);

    // Only allow GET requests
    if (req.method !== 'GET') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Method not allowed' }));
        return;
    }

    // For debugging, log the headers
    console.log(`[API /api/horses/types] Headers:`, req.headers);

    // Read the horse types from the file
    const typesPath = path.join(__dirname, 'data', 'horses', 'horse_types.json');
    console.log(`[API /api/horses/types] Reading types from: ${typesPath}`);

    fs.readFile(typesPath, 'utf8', (err, data) => {
        if (err) {
            console.error(`[API /api/horses/types] Error reading horse_types.json:`, err);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Error loading horse types' }));
            return;
        }

        try {
            const types = JSON.parse(data);
            console.log(`[API /api/horses/types] Successfully loaded ${Object.keys(types).length} horse types`);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true, types }));
        } catch (parseErr) {
            console.error(`[API /api/horses/types] Error parsing horse_types.json:`, parseErr);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Error parsing horse types data' }));
        }
    });
}

/**
 * Handle requests to get a specific horse by ID
 * @param {http.IncomingMessage} req - The request object
 * @param {http.ServerResponse} res - The response object
 * @param {string} horseId - The ID of the horse to get
 */
function handleHorseByIdRequest(req, res, horseId) {
    console.log(`[API /api/horses/${horseId}] Received request.`);

    // Only allow GET requests
    if (req.method !== 'GET') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Method not allowed' }));
        return;
    }

    // Check session from cookie
    let sessionId = null;
    if (req.headers.cookie) {
        const cookies = parseCookies(req);
        sessionId = cookies.sessionId;
    }

    // If not in cookie, try header
    if (!sessionId && req.headers['x-session-id']) {
        sessionId = req.headers['x-session-id'];
    }

    // If still not found, try query parameter
    if (!sessionId) {
        const parsedUrl = url.parse(req.url, true);
        if (parsedUrl.query.sessionId) {
            sessionId = parsedUrl.query.sessionId;
        }
    }

    console.log(`[API /api/horses/${horseId}] Session ID: ${sessionId}`);
    console.log(`[API /api/horses/${horseId}] Active session? ${!!activeSessions[sessionId]}`);

    if (!sessionId || !activeSessions[sessionId]) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Not authenticated' }));
        return;
    }

    // Read the horse types from the file
    const typesPath = path.join(__dirname, 'data', 'horses', 'horse_types.json');
    fs.readFile(typesPath, 'utf8', (err, typesData) => {
        if (err) {
            console.error(`[API /api/horses/${horseId}] Error reading horse_types.json:`, err);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Error loading horse types' }));
            return;
        }

        try {
            const types = JSON.parse(typesData);

            // Parse the complex horse ID format (e.g., riding_horse_brown_1745994144814_850)
            console.log(`[API /api/horses/${horseId}] Parsing complex horse ID`);

            // Try to extract the base horse type and color
            const parts = horseId.split('_');
            let baseHorseId = null;
            let colorId = null;

            if (parts.length >= 2) {
                // Check if it's a complex ID with timestamp
                if (parts.length >= 4 && !isNaN(parts[parts.length - 2])) {
                    // Format: riding_horse_brown_1745994144814_850
                    baseHorseId = parts[0] + '_' + parts[1]; // riding_horse
                    colorId = parts[2]; // brown
                    console.log(`[API /api/horses/${horseId}] Parsed as complex ID: baseHorseId=${baseHorseId}, colorId=${colorId}`);
                } else {
                    // Simple format: riding_horse
                    baseHorseId = horseId;
                    console.log(`[API /api/horses/${horseId}] Using as simple ID: ${baseHorseId}`);
                }
            } else {
                baseHorseId = horseId;
                console.log(`[API /api/horses/${horseId}] Using as simple ID: ${baseHorseId}`);
            }

            // Find the requested horse type
            const horseType = types[baseHorseId];

            if (!horseType) {
                console.error(`[API /api/horses/${horseId}] Horse type not found: ${baseHorseId}`);
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: 'Horse type not found' }));
                return;
            }

            // Format the horse data
            const horse = {
                id: horseType.id,
                name: horseType.name,
                description: horseType.description,
                basePrice: horseType.basePrice,
                speed: horseType.baseSpeed,
                stamina_multiplier: horseType.stamina_multiplier || 2.0,
                level: horseType.level || 1
            };

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true, horse }));
        } catch (parseErr) {
            console.error(`[API /api/horses/${horseId}] Error parsing horse_types.json:`, parseErr);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Error parsing horse types data' }));
        }
    });
}

// --- End Horse API Handlers ---
