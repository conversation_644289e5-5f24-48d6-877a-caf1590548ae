/**
 * Main game logic for the DawnSword game
 * Handles game initialization, commands, game loop, movement, and interactions.
 */

const Game = {
    initialized: false,
    socket: null, // Added to store the socket connection
    lastTimestamp: 0,
    inputState: { // Track keyboard state for movement
        north: false,
        east: false,
        south: false,
        west: false,
        sprint: false
    },
    staminaExhaustedMessageShown: false, // Flag to track if stamina exhaustion message has been shown
    otherPlayers: {}, // Store data about other connected players { socketId: { username, x, y, zoneId } }
    chatMessages: [], // Array to store all received chat messages

    musicStarted: false, // Flag to track if music has been started initially

    /**
     * Update the online users count in the UI
     * @param {number} count - The number of online users
     */
    updateOnlineUsersCount: function(count) {
        // Call the updateOnlineCount function from onlineUsers.js (now on window)
        if (typeof window.updateOnlineCount === 'function') {
            window.updateOnlineCount(count);
        } else {
            // Try to update the element directly as a fallback
            const onlineCountElement = document.getElementById('online-count');
            if (onlineCountElement) {
                onlineCountElement.textContent = count;
            }
        }
    },

    /**
      * Initialize the game
      */
     init: async function() { // Made async
         try {
             if (this.initialized) return true;

             // --- Check if io is defined before proceeding ---
             if (typeof io === 'undefined') {
                 // Retry initialization after a short delay
                 setTimeout(() => Game.init(), 100);
                 return false; // Stop this attempt, wait for retry
             }
             // --- End Check ---

             // --- Check if game is already running in another tab ---
             if (typeof SessionCheck !== 'undefined') {
                 const isOnlyTab = SessionCheck.init();
                 if (!isOnlyTab) {
                     alert('The game is already running in another tab or window. Please use that tab or close it before opening a new one.');
                     window.location.href = 'login.html';
                     return false;
                 }
             }
             // --- End Check ---

             // --- Connect to Socket.IO Server ---
             this.socket = io(); // Now safe to call io()

             this.socket.on('connect', () => {
                 // Authenticate with the server using sessionId from localStorage
                 const sessionId = localStorage.getItem('sessionId');
                 if (sessionId) {
                     this.socket.emit('authenticate', sessionId);
                 } else {
                     window.location.href = 'login.html'; // Redirect if no session
                 }
             });

             this.socket.on('authenticated', (_playerState) => {
                 // TODO: Use playerState from server if needed, maybe merge with loadedData?
                 // For now, we primarily rely on loadGameState for initial state.

                 // Send initial position to server immediately after authentication
                 if (this.socket && this.socket.connected) {
                     const initialPosition = {
                         x: Player.x,
                         y: Player.y,
                         zoneId: Player.currentZoneId
                     };
                     this.socket.emit('player_move', initialPosition);
                 }
             });

             this.socket.on('auth_failed', (_message) => {
                 localStorage.removeItem('sessionId'); // Clear invalid session
                 localStorage.removeItem('username');
                 window.location.href = 'login.html'; // Redirect to login
             });

             // Handle duplicate session notification
             this.socket.on('duplicate_session', (message) => {
                 console.log('Duplicate session detected:', message);
                 // Show an alert to the user
                 alert('Your account has been logged in from another window or browser. This session will be disconnected.');
                 // Redirect to login page
                 window.location.href = 'login.html';
             });

             this.socket.on('auth_success', (data) => {
                 // Store admin status in a variable that will be checked after initialization
                 this.receivedAdminStatus = data.isAdmin;

                 // Set admin status directly on the Player object if it's initialized
                 if (typeof Player !== 'undefined') {
                     Player.isAdmin = data.isAdmin;
                 }
             });

             // Listen for player count updates from the server
             this.socket.on('player_count_update', (data) => {
                 this.updateOnlineUsersCount(data.count);
             });

             // Listen for gold updates from the server
             this.socket.on('gold_update', (data) => {
                 // Update player's gold
                 Player.gold = data.gold;

                 // Update gold display
                 if (typeof UI !== 'undefined' && UI.updateGoldDisplay) {
                     UI.updateGoldDisplay();
                 }

                 console.log(`Gold updated: ${data.gold} (${data.transaction})`);
             });

             this.socket.on('disconnect', (reason) => {
                 console.log(`Disconnected from server. Reason: ${reason}`);
                 this.addMessage('Disconnected from server. Attempting to reconnect...', 'error'); // Don't force scroll

                 // Set a flag to indicate we're trying to reconnect
                 this.isReconnecting = true;

                 // Show reconnecting status to user
                 const saveIndicator = document.getElementById('save-indicator');
                 if (saveIndicator) {
                     saveIndicator.textContent = 'Reconnecting...';
                     saveIndicator.style.display = 'inline-block';
                     saveIndicator.style.color = '#ff9900';
                 }
             });

             // Handle reconnect attempt
             this.socket.io.on('reconnect_attempt', (attemptNumber) => {
                 console.log(`Reconnection attempt: ${attemptNumber}`);
                 this.addMessage(`Reconnection attempt: ${attemptNumber}...`, 'system');
             });

             // Handle successful reconnection
             this.socket.io.on('reconnect', () => {
                 console.log('Reconnected to server');
                 this.addMessage('Reconnected to server!', 'success');
                 this.isReconnecting = false;

                 // Update UI to show reconnected
                 const saveIndicator = document.getElementById('save-indicator');
                 if (saveIndicator) {
                     saveIndicator.textContent = 'Connected';
                     saveIndicator.style.color = '#00ff00';
                     // Hide after 3 seconds
                     setTimeout(() => {
                         saveIndicator.style.display = 'none';
                     }, 3000);
                 }

                 // Re-authenticate with the server
                 const sessionId = localStorage.getItem('sessionId');
                 if (sessionId) {
                     this.socket.emit('authenticate', sessionId);
                 }

                 // Re-sync player position
                 if (Player && Player.x !== undefined && Player.y !== undefined && Player.currentZoneId) {
                     this.socket.emit('player_move', {
                         x: Player.x,
                         y: Player.y,
                         zoneId: Player.currentZoneId
                     });
                 }
             });

             // Handle reconnect error
             this.socket.io.on('reconnect_error', (error) => {
                 console.error('Reconnection error:', error);
                 this.addMessage('Failed to reconnect. Will try again...', 'error');
             });

             // TODO: Add listeners for game state updates (e.g., other players moving)
             this.socket.on('player_moved', (data) => {
                 if (data.socketId !== this.socket.id) { // Make sure it's not our own movement
                     // Get existing player data to preserve other properties
                     const existingPlayer = this.otherPlayers[data.socketId] || {};

                     const playerX = Number(data.x);
                     const playerY = Number(data.y);
                     const playerZoneId = data.zoneId || 'unknown';
                     const playerUsername = data.username || existingPlayer.username || 'Unknown Player';

                     // If this is a new player or they changed zones, update immediately
                     const isNewPlayer = !existingPlayer.username;
                     const changedZone = existingPlayer.zoneId !== playerZoneId;

                     if (isNewPlayer || changedZone) {
                         // Store player data with explicit properties - immediate update
                         this.otherPlayers[data.socketId] = {
                             username: playerUsername,
                             x: playerX,
                             y: playerY,
                             targetX: playerX,
                             targetY: playerY,
                             zoneId: playerZoneId,
                             id: data.socketId, // Add ID for reference
                             isMounted: data.isMounted || false,
                             lastUpdateTime: performance.now() / 1000 // Track time for interpolation
                         };
                     } else {
                         // Store player data with target position for interpolation
                         this.otherPlayers[data.socketId] = {
                             ...existingPlayer,
                             username: playerUsername,
                             targetX: playerX,
                             targetY: playerY,
                             zoneId: playerZoneId,
                             id: data.socketId, // Add ID for reference
                             isMounted: data.isMounted || false,
                             lastUpdateTime: performance.now() / 1000 // Track time for interpolation
                         };
                     }


                     // Immediately update the minimap to reduce perceived lag
                     Minimap.update();
                 }
             });
             this.socket.on('current_players', (players) => {

                 // Clear existing other players and add the current list
                 this.otherPlayers = {};

                 // Count total online players (including self)
                 const onlineCount = Object.keys(players).length + 1; // +1 for self
                 this.updateOnlineUsersCount(onlineCount);

                 for (const socketId in players) {
                     if (socketId !== this.socket.id) { // Don't add self
                         // Ensure coordinates are numbers
                         const player = players[socketId];

                         if (!player) {
                             continue;
                         }

                         const playerX = Number(player.x || 0);
                         const playerY = Number(player.y || 0);
                         const playerZoneId = player.zoneId || 'unknown';
                         const playerUsername = player.username || 'Unknown Player';

                         this.otherPlayers[socketId] = {
                             username: playerUsername,
                             x: playerX,
                             y: playerY,
                             targetX: playerX,
                             targetY: playerY,
                             zoneId: playerZoneId,
                             id: socketId, // Add ID for reference
                             isMounted: player.mountedHorse || false,
                             // Only store horse data if it's an active horse (not stabled)
                             // First check if horse exists, then check if it's active and not stabled
                             horse: player.horse && player.horse !== undefined &&
                                    (player.horse.stable_id === null || player.horse.stable_id === undefined ||
                                     player.horse.is_active === 1) ? player.horse : null,
                             lastUpdateTime: performance.now() / 1000 // Track time for interpolation
                         };

                     }
                 }



                 // Force a complete update of the minimap to ensure all players are visible and coordinates are updated
                 Minimap.update();

                 // Send our position to the server after receiving current players
                 // This ensures other players can see us immediately
                 if (this.socket && this.socket.connected) {
                     const currentPosition = {
                         x: Player.x,
                         y: Player.y,
                         zoneId: Player.currentZoneId
                     };
                     this.socket.emit('player_move', currentPosition);
                 }
             });
             this.socket.on('player_joined', (data) => {
                 if (data.socketId !== this.socket.id) {
                     // Ensure coordinates are numbers
                     const playerData = data.playerData;

                     if (!playerData) {
                         return;
                     }

                     const playerX = Number(playerData.x || 0);
                     const playerY = Number(playerData.y || 0);
                     const playerZoneId = playerData.zoneId || 'unknown';
                     const playerUsername = playerData.username || 'Unknown Player';

                     // Store player data with explicit properties
                     this.otherPlayers[data.socketId] = {
                         username: playerUsername,
                         x: playerX,
                         y: playerY,
                         targetX: playerX,
                         targetY: playerY,
                         zoneId: playerZoneId,
                         id: data.socketId, // Add ID for reference
                         isMounted: playerData.mountedHorse || false,
                         // Only store horse data if it's an active horse (not stabled)
                         // First check if horse exists, then check if it's active and not stabled
                         horse: playerData.horse && playerData.horse !== undefined &&
                                (playerData.horse.stable_id === null || playerData.horse.stable_id === undefined ||
                                 playerData.horse.is_active === 1) ? playerData.horse : null,
                         lastUpdateTime: performance.now() / 1000 // Track time for interpolation
                     };

                     this.addMessage(`${playerUsername} has entered the area.`, 'system'); // Don't force scroll

                     // Clean up any existing horses for this player to prevent duplicates
                     if (typeof World !== 'undefined' && Player.currentZoneId) {
                         this.cleanupDuplicateHorses(data.socketId);
                     }

                     // Update the online player count immediately when a player joins
                     const onlineCount = Object.keys(this.otherPlayers).length + 1; // +1 for self
                     this.updateOnlineUsersCount(onlineCount);

                     // Force a complete update of the minimap to ensure the new player is visible and coordinates are updated
                     Minimap.update();

                     // Send our position to the server to ensure the new player sees us
                     if (this.socket && this.socket.connected) {
                         const currentPosition = {
                             x: Player.x,
                             y: Player.y,
                             zoneId: Player.currentZoneId
                         };
                             this.socket.emit('player_move', currentPosition);
                     }
                 }
             });
             this.socket.on('player_left', (socketId) => {
                 if (this.otherPlayers[socketId]) {
                     const playerName = this.otherPlayers[socketId].username;
                     this.addMessage(`${playerName} has left the area.`, 'system'); // Don't force scroll

                     // Remove any horses owned by this player from the world
                     if (typeof World !== 'undefined') {
                         const currentZone = World.getZone(Player.currentZoneId);
                         if (currentZone && currentZone.objects && Array.isArray(currentZone.objects.horses)) {
                             // Find all horses owned by this player
                             const playerHorses = currentZone.objects.horses.filter(h => h.ownerId === socketId);

                             // Remove each horse
                             for (const horse of playerHorses) {


                                 // Remove from the zone's horses array
                                 const horseIndex = currentZone.objects.horses.findIndex(h => h.id === horse.id);
                                 if (horseIndex !== -1) {
                                     currentZone.objects.horses.splice(horseIndex, 1);
                                 }

                                 // Remove from World.horses if it exists
                                 if (World.horses && World.horses[horse.id]) {
                                     delete World.horses[horse.id];
                                 }

                                 // Clean up the horse's AI state
                                 if (typeof NpcAI !== 'undefined') {
                                     const stateId = `${horse.id}_${Player.currentZoneId}`;
                                     if (NpcAI.npcStates[stateId]) {
                                         delete NpcAI.npcStates[stateId];
                                     }
                                 }
                             }
                         }
                     }

                     // Remove the player from tracking
                     delete this.otherPlayers[socketId];

                     // Update the online player count immediately when a player leaves
                     const onlineCount = Object.keys(this.otherPlayers).length + 1; // +1 for self
                     this.updateOnlineUsersCount(onlineCount);

                     Minimap.update(); // Update minimap and coordinates
                 }
             });
             this.socket.on('chat_message', (data) => {
                // Store all incoming messages
                const messageData = {
                    username: data.username,
                    message: data.message,
                    type: data.type || 'world', // Ensure type is present, default to 'world'
                    isAdmin: data.isAdmin,
                    messageId: data.messageId,
                    timestamp: data.timestamp
                };
                this.chatMessages.push(messageData);

                // Only display if it matches the active tab
                if (typeof ChatTabs !== 'undefined' && ChatTabs.activeChatType === messageData.type) {
                    UI.displayChatMessage(messageData.username, messageData.message, messageData.isAdmin, messageData.messageId, messageData.timestamp);
                }
             });

             // Handle server messages
             this.socket.on('server_message', (data) => {
                 // Get the original message
                 const originalMessage = data.message;

                 // Log the original message for debugging
                 console.log("Original server message:", originalMessage);

                 // Create a prefix for server messages
                 const prefix = "[SERVER MESSAGE] ";

                 // Process the message to handle links
                 let processedMessage = originalMessage;

                 // Check if the message contains links
                 if (originalMessage.includes("[link:")) {
                     console.log("Link pattern detected in message");

                     try {
                         // Extract all link patterns using a more robust approach
                         const linkRegex = /\[link:(https?:\/\/[^:]+):([^\]]+)\]/g;
                         let match;
                         let lastIndex = 0;
                         let result = prefix; // Start with the prefix

                         // Find all matches and build the message piece by piece
                         while ((match = linkRegex.exec(originalMessage)) !== null) {
                             // Add text before the link
                             result += originalMessage.substring(lastIndex, match.index);

                             // Extract URL and link text
                             const url = match[1];
                             const text = match[2];

                             console.log("Found link:", { url, text });

                             // Add the HTML link
                             result += `<a href="${url}" target="_blank" style="color: #9370DB; text-decoration: underline;">${text}</a>`;

                             // Update the last index
                             lastIndex = match.index + match[0].length;
                         }

                         // Add any remaining text after the last link
                         result += originalMessage.substring(lastIndex);

                         // Use the processed result
                         processedMessage = result;
                     } catch (error) {
                         console.error("Error processing links:", error);
                         // Fallback to simple prefix + message if there's an error
                         processedMessage = prefix + originalMessage;
                     }
                 } else {
                     // No links, just add the prefix
                     processedMessage = prefix + originalMessage;
                 }

                 console.log("Final processed message:", processedMessage);

                 // Add the message to the game console with the specified color
                 this.addMessage(processedMessage, 'server', true);
             });
             // Add listener for chat history
             this.socket.on('chat_history', (messages) => {
                console.log(`[DEBUG] Received chat history: ${messages.length} messages`);
                if (messages.length > 0) {
                    console.log(`[DEBUG] First message type: ${messages[0].type}, zone: ${messages[0].zoneId}`);
                }
                this.chatMessages = messages.map(msg => ({ // Store and map to ensure consistent structure
                    username: msg.username,
                    message: msg.message,
                    type: msg.type || 'world', // Ensure type is present
                    isAdmin: msg.isAdmin,
                    messageId: msg.messageId || msg.id,
                    timestamp: msg.timestamp,
                    zoneId: msg.zoneId // Include zone ID
                }));

                // Call UI.filterAndDisplayChatMessages to render based on the current active tab
                if (typeof UI !== 'undefined' && typeof UI.filterAndDisplayChatMessages === 'function') {
                    UI.filterAndDisplayChatMessages();
                } else {
                    // Fallback if UI function isn't ready (should not happen in normal flow)
                    const chatOutput = document.getElementById('chat-output');
                    if (chatOutput) {
                        chatOutput.innerHTML = ''; // Clear existing chat
                        this.chatMessages.forEach(msg => {
                            // Display all initially, or filter if ChatTabs is somehow available
                            if (typeof ChatTabs === 'undefined' || ChatTabs.activeChatType === msg.type) {
                                UI.displayChatMessage(
                                    msg.username,
                                    msg.message,
                                    msg.isAdmin,
                                    msg.messageId,
                                    msg.timestamp
                                );
                            }
                        });
                    }
                }
             });
             // Add listener for player list response
             this.socket.on('player_list_response', (usernames) => {
                 if (usernames && usernames.length > 0) {
                     // Display the full list including the current user
                     const formattedList = usernames.join(', ');
                     this.addMessage(`Players online (${usernames.length}): ${formattedList}`, 'system', true); // Force scroll for command result
                 } else {
                     // Or if the user is the only one online and filtering was active
                     this.addMessage('No players currently online (this seems odd).', 'system', true); // Force scroll for command result
                 }
             });
             // Add listener for admin list response
             this.socket.on('admin_list_response', (adminUsernames) => {
                 if (adminUsernames && adminUsernames.length > 0) {
                     const formattedList = adminUsernames.join(', ');
                     this.addMessage(`Current Admins: ${formattedList}`, 'system', true); // Force scroll for command result
                 } else {
                     this.addMessage('No admins are currently defined.', 'system', true); // Force scroll for command result
                 }
             });

             // Add listener for message deleted event
             this.socket.on('message_deleted', (data) => {
                 const messageId = data.messageId;
                 if (messageId) {
                     // Find and remove the message from the chat output
                     const chatOutput = document.getElementById('chat-output');
                     if (chatOutput) {
                         const messageElement = chatOutput.querySelector(`.chat-message[data-message-id="${messageId}"]`);
                         if (messageElement) {
                             messageElement.remove();
                         }
                     }
                 }
             });

             // Handle request for position update (sent when a new player joins)
             this.socket.on('request_position_update', () => {
                 if (this.socket && this.socket.connected) {
                     const currentPosition = {
                         x: Player.x,
                         y: Player.y,
                         zoneId: Player.currentZoneId
                     };
                     this.socket.emit('player_move', currentPosition);

                     // Also send horse position if mounted
                     if (Player.mountedHorse && typeof HorseSystem !== 'undefined') {
                         HorseSystem.sendHorsePositionUpdate(Player.mountedHorse.id, Player.x, Player.y, true);
                     }
                 }
             });

             // Handle horse position updates from other players
             this.socket.on('horse_position_update', (data) => {
                 if (data.socketId === this.socket.id) {
                     // Ignore our own horse position updates
                     return;
                 }

                 // Find the player in otherPlayers
                 const otherPlayer = this.otherPlayers[data.socketId];
                 if (!otherPlayer) {
                     return;
                 }

                 // Initialize horse positions object if it doesn't exist
                 if (!otherPlayer.horsePositions) {
                     otherPlayer.horsePositions = {};
                 }

                 // Store the horse position data
                 otherPlayer.horsePositions[data.horseId] = {
                     x: Number(data.x),
                     y: Number(data.y),
                     isMounted: data.isMounted || false,
                     velocityX: data.velocityX || 0,
                     velocityY: data.velocityY || 0,
                     timestamp: data.timestamp || Date.now(),
                     color: data.color || 'brown',
                     horseName: data.horseName || `${otherPlayer.username}'s horse`
                 };

                 // Get the horse's zone ID from the data or use the player's current zone
                 const horseZoneId = data.zoneId || Player.currentZoneId;

                 // Only add the horse to the world if it's in the current zone
                 if (horseZoneId === Player.currentZoneId) {
                     const currentZone = World.getZone(Player.currentZoneId);
                     if (currentZone && currentZone.objects) {
                         // Initialize horses array if it doesn't exist
                         if (!currentZone.objects.horses) {
                             currentZone.objects.horses = [];
                         }

                         // First check for and remove any duplicate horses for this player
                         this.cleanupDuplicateHorses(data.socketId);

                         // Then check if the player already has a horse in the world by owner ID
                         const existingHorseByOwner = currentZone.objects.horses.find(h => h.ownerId === data.socketId);

                         if (existingHorseByOwner) {
                             // Update the existing horse's properties
                             existingHorseByOwner.x = Number(data.x);
                             existingHorseByOwner.y = Number(data.y);
                             existingHorseByOwner.color = data.color || 'brown';
                             existingHorseByOwner.name = data.horseName || `${otherPlayer.username}'s horse`;

                             // Update the horse in World.horses if it exists
                             if (World.horses && World.horses[existingHorseByOwner.id]) {
                                 World.horses[existingHorseByOwner.id].x = Number(data.x);
                                 World.horses[existingHorseByOwner.id].y = Number(data.y);
                             }

                             // Horse position updated
                         } else {
                             // No horse exists for this player, create a new one using the database ID
                             const horse = {
                                 id: data.horseId, // Use the database ID directly
                                 ownerId: data.socketId,
                                 ownerName: otherPlayer.username,
                                 x: Number(data.x),
                                 y: Number(data.y),
                                 zoneId: horseZoneId,
                                 color: data.color || 'brown',
                                 name: data.horseName || `${otherPlayer.username}'s horse`,
                                 objectType: 'horses',
                                 ai: { type: 'horse' }
                             };

                             // Add the horse to the zone
                             currentZone.objects.horses.push(horse);

                             // Store the horse in the World.horses object for easy access
                             if (World.horses) {
                                 World.horses[data.horseId] = horse;
                             }

                             // Horse added
                         }
                     }
                 }

                 // Update the minimap to show the horse
                 Minimap.update();
             });

             // Handle horse removal events
             this.socket.on('horse_removed', (data) => {
                 if (data.socketId === this.socket.id) {
                     // Ignore our own horse removal events
                     return;
                 }

                 console.log(`[DEBUG] Received horse removal for player ${data.username}, horse ID: ${data.horseId}`);

                 // Find the player in otherPlayers
                 const otherPlayer = this.otherPlayers[data.socketId];
                 if (!otherPlayer) {
                     console.warn(`[DEBUG] Received horse removal for unknown player: ${data.socketId}`);
                     return;
                 }

                 // Remove the horse position from the player's data
                 if (otherPlayer.horsePositions && otherPlayer.horsePositions[data.horseId]) {
                     delete otherPlayer.horsePositions[data.horseId];
                 }

                 // Remove the horse from the world
                 if (typeof World !== 'undefined') {
                     // Find the horse in the current zone
                     const currentZone = World.getZone(Player.currentZoneId);
                     if (currentZone && currentZone.objects && Array.isArray(currentZone.objects.horses)) {
                         // Find the horse index
                         const horseIndex = currentZone.objects.horses.findIndex(h => h.id === data.horseId);
                         if (horseIndex !== -1) {
                             // Remove the horse from the zone
                             currentZone.objects.horses.splice(horseIndex, 1);
                             console.log(`[DEBUG] Removed horse from world: ${data.horseId}`);

                             // Clean up the horse's AI state in the NPC AI system
                             if (typeof NpcAI !== 'undefined') {
                                 const stateId = `${data.horseId}_${Player.currentZoneId}`;
                                 if (NpcAI.npcStates[stateId]) {
                                     console.log(`[DEBUG] Cleaning up AI state for removed horse: ${stateId}`);
                                     delete NpcAI.npcStates[stateId];
                                 }
                             }
                         }
                     }

                     // Also remove from World.horses object if it exists
                     if (World.horses && World.horses[data.horseId]) {
                         delete World.horses[data.horseId];
                     }
                 }

                 // Update the minimap to reflect the removed horse
                 if (typeof Minimap !== 'undefined') {
                     Minimap.update();
                 }


             });

             // Handle player horse activated event
             this.socket.on('player_horse_activated', (data) => {
                 if (data.socketId === this.socket.id) {
                     // Ignore our own horse activation events
                     return;
                 }

                 // Find the player in otherPlayers
                 const otherPlayer = this.otherPlayers[data.socketId];
                 if (!otherPlayer) {
                     return;
                 }

                 // Update the player's horse data
                 otherPlayer.horse = data.horse;

                 // Initialize horse positions object if it doesn't exist
                 if (!otherPlayer.horsePositions) {
                     otherPlayer.horsePositions = {};
                 }

                 // Use the position from the server if provided, otherwise use player position
                 const horseX = data.x !== undefined ? Number(data.x) : otherPlayer.x;
                 const horseY = data.y !== undefined ? Number(data.y) : otherPlayer.y;
                 const horseZoneId = data.zoneId || Player.currentZoneId;

                 // Use the database ID for the horse
                 const horseId = data.horse.id;

                 // Create a default horse position at the specified position
                 otherPlayer.horsePositions[horseId] = {
                     x: horseX,
                     y: horseY,
                     isMounted: false,
                     velocityX: 0,
                     velocityY: 0,
                     timestamp: Date.now(),
                     color: data.horse.color || 'brown',
                     horseName: data.horse.name || `${otherPlayer.username}'s horse`
                 };

                 // Only add the horse to the world if it's in the current zone
                 if (horseZoneId === Player.currentZoneId) {
                     const currentZone = World.getZone(Player.currentZoneId);
                     if (currentZone && currentZone.objects) {
                         // Initialize horses array if it doesn't exist
                         if (!currentZone.objects.horses) {
                             currentZone.objects.horses = [];
                         }

                         // First check for and remove any duplicate horses for this player
                         this.cleanupDuplicateHorses(data.socketId, otherPlayer.username);

                         // Then check if the player already has a horse in the world by owner ID
                         const existingHorseByOwner = currentZone.objects.horses.find(h => h.ownerId === data.socketId);

                         if (existingHorseByOwner) {
                             // Update the existing horse's properties
                             existingHorseByOwner.x = horseX;
                             existingHorseByOwner.y = horseY;
                             existingHorseByOwner.color = data.horse.color || 'brown';
                             existingHorseByOwner.name = data.horse.name || `${otherPlayer.username}'s horse`;

                             // Update the horse in World.horses if it exists
                             if (World.horses && World.horses[existingHorseByOwner.id]) {
                                 World.horses[existingHorseByOwner.id].x = horseX;
                                 World.horses[existingHorseByOwner.id].y = horseY;
                             }

                             // Horse updated
                         } else {
                             // No horse exists for this player, create a new one using the database ID
                             const horse = {
                                 id: horseId, // Use the database ID directly
                                 ownerId: data.socketId,
                                 ownerName: otherPlayer.username,
                                 x: horseX,
                                 y: horseY,
                                 zoneId: horseZoneId,
                                 color: data.horse.color || 'brown',
                                 name: data.horse.name || `${otherPlayer.username}'s horse`,
                                 objectType: 'horses',
                                 ai: { type: 'horse' }
                             };

                             // Add the horse to the zone
                             currentZone.objects.horses.push(horse);

                             // Store the horse in the World.horses object for easy access
                             if (World.horses) {
                                 World.horses[horseId] = horse;
                             }

                             // Horse added
                         }
                     }
                 }

                 // Update the minimap to show the horse
                 Minimap.update();
             });
             // --- End Socket.IO Setup ---


             // --- Load Game State ---
             const loadedData = await Utils.loadGameState(); // Use new load function
             if (!loadedData) {
                 // loadGameState handles redirecting to login if no session, so we might not reach here
                 this.addMessage('Failed to load game state or session invalid.', 'error'); // Don't force scroll on init error
                 // Potentially stop initialization if load fails critically
                  return false;
              }

             // --- Load Building Types ---
             let loadedBuildingTypes = null;
             try {
                 const buildingTypesResponse = await fetch('data/places/building_types.json');
                 if (buildingTypesResponse.ok) {
                     loadedBuildingTypes = await buildingTypesResponse.json();
                     console.log("Loaded building types from file:", loadedBuildingTypes);
                 } else {
                     console.error("CRITICAL: Could not load building_types.json. Indoor audio will not work.");
                     // Handle error appropriately - maybe prevent game start or use defaults?
                 }
             } catch (error) {
                 console.error("Error loading building_types.json:", error);
             }
             // --- End Load Building Types ---

             // --- Load Zone Definitions ---
             let loadedZoneDefinitions = null;
             try {
                 const definitionsResponse = await fetch('data/places/zone_definitions.json');
                 if (definitionsResponse.ok) {
                     loadedZoneDefinitions = await definitionsResponse.json();
                     console.log("Loaded zone definitions from file:", loadedZoneDefinitions);
                 } else {
                     console.error("CRITICAL: Could not load zone_definitions.json. World map and zone data will be incomplete.");
                     this.addMessage('CRITICAL: Failed to load zone definitions.', 'error'); // Don't force scroll
                     // Handle critical failure - maybe stop initialization or use defaults?
                     return false; // Stop initialization
                 }
             } catch (error) {
                 console.error("Error loading zone_definitions.json:", error);
                 this.addMessage('CRITICAL: Error loading zone definitions.', 'error'); // Don't force scroll
                 return false; // Stop initialization on error
             }
             // --- End Load Zone Definitions ---


             if (!loadedData) {
                 this.addMessage('Failed to load game state from server.', 'error'); // Don't force scroll on init error
                 // Proceed with defaults if load fails
             } else {
                 console.log("Loaded game state:", loadedData);
             }

             // Ensure the online count is set to at least 1 (self) during initialization
             const onlineCountElement = document.getElementById('online-count');
             if (onlineCountElement) {
                 onlineCountElement.textContent = '1';
                 console.log('Set initial online count to 1 during game initialization');
             }

             // Initialize core modules, passing loaded data
             // Pass the loaded zone definitions (which include level) to World.init
             await World.init(loadedZoneDefinitions, loadedBuildingTypes, loadedData?.world); // Pass loadedZoneDefinitions
             Player.init(loadedData?.player); // Pass player data (or null)

             // Set admin status if we received it from the server
             if (this.receivedAdminStatus !== undefined) {
                 Player.isAdmin = this.receivedAdminStatus;
             }

             // Load dynamic objects for the starting zone
             if (Player.currentZoneId) {
                 console.log(`Loading initial objects for starting zone: ${Player.currentZoneId}`);
                  await World.loadZoneObjects(Player.currentZoneId);
              } else {
                  console.error("Cannot load initial zone objects: Player.currentZoneId is not set after Player.init().");
              }

             // Initialize UI components & Audio AFTER initial zone objects are loaded
             UI.init();
             Inventory.init();
            Minimap.init();
            WorldMap.init();
            AudioManager.init(); // Initialize AudioManager
            NpcAI.init(); // Initialize NPC AI system

            // Initialize the horse system if available
            if (typeof HorseSystem !== 'undefined') {
                HorseSystem.init();
            }

            // Display initial location and stats
            this.updateLocationDisplay(); // Use new function
            UI.updatePlayerStats(); // Ensure stats are shown initially

            // Add a welcome message
            this.addMessage('Welcome to DawnSword! Use WASD to move. Type "help" for commands.', 'system'); // Don't force scroll welcome message

            // Setup input listeners
            this.setupInputListeners();

            // Add visibility change listener to handle tab switching
            document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

            // Focus the command input
            UI.focusCommandInput();

            // AudioManager now handles starting music after user interaction via its init listener

            this.initialized = true;
            console.log('Game initialized successfully.');

            // Send player position to server after initialization
            if (this.socket && this.socket.connected) {
                // Get horse position if it exists
                let horsePosition = null;
                if (Player.horse && Player.horse.id) {
                    const horseObj = World.getHorse(Player.horse.id);
                    if (horseObj) {
                        horsePosition = {
                            x: horseObj.x,
                            y: horseObj.y
                        };
                    }
                }

                const initialPosition = {
                    x: Player.x,
                    y: Player.y,
                    zoneId: Player.currentZoneId,
                    mountedHorse: Player.mountedHorse ? true : false,
                    horse: Player.horse, // Send the player's horse data
                    horsePosition: horsePosition // Send the actual horse position
                };
                console.log('Sending position after initialization:', initialPosition);
                this.socket.emit('player_move', initialPosition);

                // Request position updates from all other players to ensure we see them
                console.log('Requesting position updates from all players');
                this.socket.emit('request_all_positions');

                // Request horse position updates from all other players
                console.log('Requesting horse position updates from all players');
                this.socket.emit('request_all_horses');
            }

             // Start the game loop
             this.lastTimestamp = performance.now();
             requestAnimationFrame(this.gameLoop.bind(this));

             // --- Request Chat History for the default chat type ---
             if (this.socket && this.socket.connected) {
                 // Request chat history for the default chat type (world)
                 const defaultChatType = (typeof ChatTabs !== 'undefined' && ChatTabs.activeChatType) ? ChatTabs.activeChatType : 'world';
                 console.log(`Game: Requesting initial chat history for type: ${defaultChatType}`);
                 this.socket.emit('request_chat_history', { chatType: defaultChatType });
             } else {
                 console.warn('Game: Could not request chat history - socket not connected');
             }
             // --- End Request Chat History ---

             return true;
        } catch (error) {
            console.error('Error initializing game:', error);
            this.addMessage('Failed to initialize game. Check console for errors.', 'error'); // Don't force scroll init error
            return false;
        }
    },

    /**
     * Main game loop
     * @param {number} timestamp - The current time provided by requestAnimationFrame
     */
    // Player movement interpolation settings
    playerInterpolationSpeed: 100, // Units per second - extremely high for instant-looking movement

    // Track visibility state
    isPageVisible: true,

    /**
     * Clean up duplicate horses for a player
     * @param {string} playerId - The player's socket ID
     */
    cleanupDuplicateHorses: function(playerId) {
        if (typeof World === 'undefined' || !Player.currentZoneId) {
            return;
        }

        const currentZone = World.getZone(Player.currentZoneId);
        if (!currentZone || !currentZone.objects || !Array.isArray(currentZone.objects.horses)) {
            return;
        }

        // Find all horses owned by this player
        const playerHorses = currentZone.objects.horses.filter(h => h.ownerId === playerId);

        // Always clean up horses for this player, even if there's only one
        // This ensures we don't have any stale horses
        if (playerHorses.length > 0) {
            // If the player has a horse in their data, use that as the reference horse to keep
            const otherPlayer = this.otherPlayers[playerId];
            let referenceHorseId = null;

            if (otherPlayer && otherPlayer.horse && otherPlayer.horse.id) {
                // Use the horse ID from the player's data as the reference
                referenceHorseId = otherPlayer.horse.id;
            } else if (playerHorses.length > 1) {
                // If no reference horse but multiple horses, sort and keep the newest
                // Sort horses by ID (assuming newer horses have higher IDs)
                playerHorses.sort((a, b) => {
                    // If the ID is a UUID, compare them directly
                    if (a.id.includes('-') && b.id.includes('-')) {
                        return a.id.localeCompare(b.id);
                    }

                    // If the ID contains a timestamp (like horse_1234567890_123), extract and compare it
                    const aMatch = a.id.match(/horse_(\d+)_/);
                    const bMatch = b.id.match(/horse_(\d+)_/);

                    if (aMatch && bMatch) {
                        return parseInt(aMatch[1]) - parseInt(bMatch[1]);
                    }

                    // Fallback to string comparison
                    return a.id.localeCompare(b.id);
                });

                // Use the newest horse (last in the sorted array) as the reference
                referenceHorseId = playerHorses[playerHorses.length - 1].id;
            } else if (playerHorses.length === 1) {
                // If only one horse, use that as the reference
                referenceHorseId = playerHorses[0].id;
            }

            // Remove all horses except the reference horse
            for (const horse of playerHorses) {
                if (horse.id !== referenceHorseId) {
                    // Remove from the zone's horses array
                    const horseIndex = currentZone.objects.horses.findIndex(h => h.id === horse.id);
                    if (horseIndex !== -1) {
                        currentZone.objects.horses.splice(horseIndex, 1);

                        // Remove from World.horses if it exists
                        if (World.horses && World.horses[horse.id]) {
                            delete World.horses[horse.id];
                        }

                        // Clean up the horse's AI state
                        if (typeof NpcAI !== 'undefined') {
                            const stateId = `${horse.id}_${Player.currentZoneId}`;
                            if (NpcAI.npcStates[stateId]) {
                                delete NpcAI.npcStates[stateId];
                            }
                        }
                    }
                }
            }
        }
    },

    // Handle visibility change
    handleVisibilityChange: function() {
        this.isPageVisible = document.visibilityState === 'visible';

        // If becoming visible, force a health update and UI refresh
        if (this.isPageVisible) {
            console.log("Page became visible - forcing health update");
            // Force a health update when tab becomes visible again
            Player.forceHealthUpdate();
            UI.updatePlayerStats();
        }
    },

    gameLoop: function(timestamp) {
        if (!this.initialized) return;

        const deltaTime = (timestamp - this.lastTimestamp) / 1000; // Delta time in seconds
        this.lastTimestamp = timestamp;

        // --- Game Logic Updates ---
        this.updatePlayerPosition(deltaTime);
        Player.updateHunger(deltaTime); // Apply hunger over time, pass deltaTime
        Player.regenHealth(deltaTime); // Health regen and hunger drain at 0 health

        // Update other player positions with interpolation
        this.updateOtherPlayerPositions(deltaTime);

        // --- Rendering Updates ---
        // Always update the minimap to ensure other players are shown correctly and coordinates are updated
        Minimap.update(); // Use update() to ensure coordinates display is updated
        // WorldMap.update(); // Update world map if needed
        // UI updates are often triggered by specific events (stats change, inventory change)
        // but location display might need periodic updates if zone changes etc.
        // this.updateLocationDisplay(); // Only update if zone changes or needed

        // --- AI / World Updates ---
        // this.updateMobs(deltaTime);
        // Check if NpcAI is defined before calling update
        if (typeof NpcAI !== 'undefined') {
            // Initialize NpcAI if it hasn't been initialized yet
            if (!NpcAI._initialized) {
                console.log("Initializing NpcAI from game loop");
                try {
                    NpcAI.init();
                    NpcAI._initialized = true;
                } catch (error) {
                    console.error("Error initializing NpcAI:", error);
                }
            }

            // Update NPC AI if it's initialized
            if (NpcAI._initialized) {
                NpcAI.update(deltaTime);
            }
        }

        // Update horse system if it exists
        if (typeof HorseSystem !== 'undefined') {
            HorseSystem.update(deltaTime);
        }

        // Request the next frame
        requestAnimationFrame(this.gameLoop.bind(this));
    },

    /**
     * Setup keyboard listeners for movement and shortcuts
     */
    setupInputListeners: function() {
        document.addEventListener('keydown', (event) => {
            // CRITICAL CHECK: If the event target is the chat input, ignore the event for game actions
            if (event.target === UI.chatInput) {
                return; // Stop processing this event in the game listener
            }

            // If the command input has focus, handle Enter and return
            if (event.target === UI.commandInput) {
                if (event.key === 'Enter') {
                    UI.submitCommand();
                }
                return; // Stop processing further if command input is focused
            }

            // Sprint key (Shift)
            if (event.key === 'Shift' || event.key === 'ShiftLeft' || event.key === 'ShiftRight') {
                this.inputState.sprint = true;
            }

            // Only process movement keys when input does NOT have focus AND not in combat
            let movementKeyPressed = false;

            // Check if any input field has focus
            const inputHasFocus = event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA' || UI.inputHasFocus;

            if (!inputHasFocus && !Combat.inCombat) { // Prevent movement keys during combat or when input has focus
                switch (event.key.toLowerCase()) {
                    case 'w': this.inputState.north = true; movementKeyPressed = true; break;
                    case 'a': this.inputState.west = true; movementKeyPressed = true; break;
                    case 's': this.inputState.south = true; movementKeyPressed = true; break;
                    case 'd': this.inputState.east = true; movementKeyPressed = true; break;
                }
            }
            // Removed duplicated case statements and closing brace here

            // Prevent default browser action for movement keys if movement happened
            if (movementKeyPressed) {
                event.preventDefault();
            }
            // Single-key command shortcuts ('l', 'i', 'm', 'h') are not processed here.
        });

        document.addEventListener('keyup', (event) => {
            // Sprint key (Shift)
            if (event.key === 'Shift' || event.key === 'ShiftLeft' || event.key === 'ShiftRight') {
                this.inputState.sprint = false;
            }
            // Only reset movement state if input does NOT have focus AND not in combat
            if (!UI.inputHasFocus && !Combat.inCombat) {
                switch (event.key.toLowerCase()) {
                    case 'w': this.inputState.north = false; break;
                    case 'a': this.inputState.west = false; break;
                    case 's': this.inputState.south = false; break;
                    case 'd': this.inputState.east = false; break;
                }
            }
        });
    },

    /**
     * Update other player positions with interpolation
     * @param {number} deltaTime - Time elapsed since the last frame in seconds
     */
    updateOtherPlayerPositions: function(deltaTime) {
        for (const socketId in this.otherPlayers) {
            const player = this.otherPlayers[socketId];

            // Skip if player doesn't have target position
            if (player.targetX === undefined || player.targetY === undefined) {
                continue;
            }

            // Calculate distance to target
            const dx = player.targetX - player.x;
            const dy = player.targetY - player.y;
            const distanceSquared = dx * dx + dy * dy;

            // If we're close to the target, just snap to it
            // Using a larger threshold for faster snapping
            if (distanceSquared < 10) {
                player.x = player.targetX;
                player.y = player.targetY;
                continue;
            }

            // Calculate movement this frame
            const moveDistance = this.playerInterpolationSpeed * deltaTime;
            const totalDistance = Math.sqrt(distanceSquared);

            // Calculate the ratio of movement
            const ratio = moveDistance / totalDistance;

            // Clamp ratio to avoid overshooting
            const clampedRatio = Math.min(ratio, 1.0);

            // Update position
            player.x += dx * clampedRatio;
            player.y += dy * clampedRatio;

            // Update horse positions for this player if they have horses
            this.updateOtherPlayerHorsePositions(player, deltaTime);
        }
    },

    /**
     * Update horse positions for other players with interpolation
     * @param {Object} player - The player object whose horse positions to update
     * @param {number} deltaTime - Time elapsed since the last frame in seconds
     */
    updateOtherPlayerHorsePositions: function(player, deltaTime) {
        // Skip if player doesn't have horse positions
        if (!player.horsePositions) {
            return;
        }

        // Update each horse position
        for (const horseId in player.horsePositions) {
            const horse = player.horsePositions[horseId];

            // If player is mounted, the horse should be at the player's position
            if (horse.isMounted) {
                horse.x = player.x;
                horse.y = player.y;

                // Update the horse in the world
                if (typeof World !== 'undefined' && World.updateHorsePosition) {
                    World.updateHorsePosition(horseId, horse.x, horse.y);
                }
                continue;
            }

            // For unmounted horses, interpolate their position based on velocity
            if (horse.velocityX || horse.velocityY) {
                // Calculate new position based on velocity
                horse.x += horse.velocityX * deltaTime;
                horse.y += horse.velocityY * deltaTime;

                // Update the horse in the world
                if (typeof World !== 'undefined' && World.updateHorsePosition) {
                    World.updateHorsePosition(horseId, horse.x, horse.y);
                }
            }
        }
    },

    /**
     * Update player position based on input state and delta time
     * @param {number} deltaTime - Time elapsed since the last frame in seconds
     */
     updatePlayerPosition: async function(deltaTime) { // Made async
         // Prevent movement during combat
         if (Combat.inCombat) {
            // Reset input state to prevent movement after combat ends immediately
            this.inputState.north = false;
            this.inputState.south = false;
            this.inputState.east = false;
            this.inputState.west = false;
            return;
        }

        let dx = 0;
        let dy = 0;

        if (this.inputState.north) dy += 1;
        if (this.inputState.south) dy -= 1;
        if (this.inputState.east) dx += 1;
        if (this.inputState.west) dx -= 1;

        // Normalize diagonal movement
        if (dx !== 0 && dy !== 0) {
            const magnitude = Math.sqrt(dx * dx + dy * dy);
            dx /= magnitude;
            dy /= magnitude;
        }

        // Sprinting logic: sprinting only if moving and sprint key held and stamina > 0
        const isMoving = dx !== 0 || dy !== 0;
        const wantsToSprint = isMoving && this.inputState.sprint;
        const canSprint = Player.stamina > 0;
        const isSprinting = wantsToSprint && canSprint;

        // Slow down default speed, increase if sprinting
        const defaultSpeed = 60;
        const sprintSpeed = 120;

        // Force default speed if stamina is zero, regardless of sprint key state
        if (Player.stamina <= 0) {
            Player.moveSpeed = defaultSpeed;
            // Also force the sprint input state to false to ensure consistent behavior
            this.inputState.sprint = false;
        } else {
            Player.moveSpeed = isSprinting ? sprintSpeed : defaultSpeed;
        }

        // Show a message when stamina first reaches zero while trying to sprint
        if (wantsToSprint && !canSprint && !this.staminaExhaustedMessageShown && Player.stamina <= 0) {
            this.addMessage("You're too exhausted to keep running.", 'warning');
            this.staminaExhaustedMessageShown = true;
        } else if (canSprint && Player.stamina > 20 && this.staminaExhaustedMessageShown) {
            // Only reset the message flag when stamina is significantly regenerated (20%)
            this.staminaExhaustedMessageShown = false;
        }

        // Update stamina every frame (regenerates if not sprinting)
        Player.updateStamina(deltaTime, isSprinting);

        if (isMoving) {
            // Prevent sprinting if stamina is 0
            const moveAmount = Player.moveSpeed * deltaTime;
            const newX = Player.x + dx * moveAmount;
            const newY = Player.y + dy * moveAmount;

            // Check for zone transitions before setting position
            const currentPos = Player.getCurrentZoneAndPosition();
            if (!currentPos) return; // Should not happen if initialized

            let targetZoneId = currentPos.zoneId;
            let targetX = newX;
            let targetY = newY;

            const zone = currentPos.zone;
            if (zone.transitions) {
                for (const transition of zone.transitions) {
                    // Simple boundary check example (can be more complex shapes)
                     let crossed = false;
                     if (transition.x !== undefined && transition.yRange) { // Vertical boundary (East/West)
                         // Moving East (Player.x <= boundary and newX > boundary) OR Moving West (Player.x >= boundary and newX < boundary)
                         if ((Player.x <= transition.x && newX > transition.x) || (Player.x >= transition.x && newX < transition.x)) {
                             if (newY >= transition.yRange[0] && newY <= transition.yRange[1]) {
                                 crossed = true;
                             }
                         }
                     } else if (transition.y !== undefined && transition.xRange) { // Horizontal boundary (North/South)
                         // Reverted to simpler boundary crossing check
                         if ((Player.y <= transition.y && newY > transition.y) || (Player.y >= transition.y && newY < transition.y)) {
                            if (newX >= transition.xRange[0] && newX <= transition.xRange[1]) {
                                crossed = true;
                            }
                        }
                    }
                    // Add checks for other boundary types (polygon, circle) if needed

                    if (crossed) {
                        targetZoneId = transition.targetZone; // Removed duplicate assignment
                        targetX = transition.targetX !== undefined ? transition.targetX : newX; // Use target coords if specified
                        targetY = transition.targetY !== undefined ? transition.targetY : newY;
                        const newZone = World.getZone(targetZoneId);
                        this.addMessage(`Entering ${newZone?.name || 'unknown area'}...`, 'system'); // Don't force scroll zone transition message
                        this.updateLocationDisplay(newZone); // Update display immediately on zone change
                        WorldMap.update(); // Update world map highlight
                        break; // Assume only one transition per frame
                    }
                 }
             }

             // --- Structure Collision Check ---
             let collisionDetected = false;
             if (targetZoneId === currentPos.zoneId && zone.objects.structures) { // Only check structures in the current zone if not transitioning
                 for (const structure of zone.objects.structures) {
                     // Skip collision detection for town_square and dirt_path structures
                     if (structure.typeId === 'town_square' || structure.type === 'town_square' ||
                         structure.typeId === 'dirt_path' || structure.type === 'dirt_path') {
                         continue;
                     }

                     // Basic AABB check (assuming Player is a point for now)
                     // Structure.x, structure.y is the center of the structure
                     const halfWidth = structure.width / 2;
                     const halfHeight = structure.height / 2;
                     const structureLeft = structure.x - halfWidth;
                     const structureRight = structure.x + halfWidth;
                     const structureBottom = structure.y - halfHeight;
                     const structureTop = structure.y + halfHeight;

                     if (newX >= structureLeft && newX <= structureRight && newY >= structureBottom && newY <= structureTop) {
                         collisionDetected = true;
                         // console.log(`Collision detected with structure: ${structure.name}`); // Optional debug
                         break; // Stop checking after first collision
                     }
                 }
             }

             // If collision detected, don't update position (effectively stopping movement)
             // Otherwise, set the potentially updated position (could be clamped by zone boundary or be a zone transition)
             if (!collisionDetected) {
                 const previousZoneId = Player.currentZoneId; // Store previous zone
                 const wasMounted = Player.mountedHorse !== null; // Store mounted state before zone change
                 let mountedHorseData = null;

                 // If player is mounted, store the horse data before zone change
                 if (wasMounted && Player.mountedHorse) {
                     mountedHorseData = { ...Player.mountedHorse };
                 }

                 Player.setPosition(targetX, targetY, targetZoneId);

                 // Load objects if the zone actually changed and await completion
                 if (targetZoneId !== previousZoneId) {
                     await World.loadZoneObjects(targetZoneId); // Await the loading

                     // If player was mounted before zone change, ensure the horse is respawned and player remains mounted
                     if (wasMounted && mountedHorseData && typeof HorseSystem !== 'undefined') {
                         // First make sure player is dismounted to reset any stale state
                         Player.mountedHorse = null;

                         // Then despawn any existing horse to ensure clean state
                         HorseSystem.despawnPlayerHorse();

                         // Now spawn a fresh horse in the new zone
                         const spawnedHorse = HorseSystem.spawnPlayerHorse();

                         // If horse was successfully spawned, ensure player remains mounted
                         if (spawnedHorse) {
                             // Set the player as mounted on the horse
                             Player.mountedHorse = spawnedHorse;

                             // Update the horse's position to match the player's
                             World.updateHorsePosition(spawnedHorse.id, Player.x, Player.y);

                             // Send horse position update to server
                             HorseSystem.sendHorsePositionUpdate(spawnedHorse.id, Player.x, Player.y, true);
                         }
                     }
                     // If player has a horse but is not mounted, ensure it follows to the new zone
                     else if (Player.horse && !Player.mountedHorse && typeof HorseSystem !== 'undefined') {
                         // Despawn any existing horse to ensure clean state
                         HorseSystem.despawnPlayerHorse();

                         // Spawn a fresh horse in the new zone
                         HorseSystem.spawnPlayerHorse();
                     }

                     // Refresh zone chat if player is viewing zone chat
                     if (typeof ChatTabs !== 'undefined' && typeof ChatTabs.refreshZoneChat === 'function') {
                         ChatTabs.refreshZoneChat();
                     }

                     // Optionally trigger lookAround or minimap update *after* loading
                     this.lookAround(); // Refresh view after loading new zone objects
                 }
             }
              // If collision *was* detected, Player.setPosition is not called,
             // so the player remains at their previous Player.x, Player.y

             // --- Emit position update AFTER potential position change ---
             // This happens regardless of collision, sending the final client position for this frame.
             // Ideally, server should validate, but this syncs client state for now.
             if (this.socket && this.socket.connected) {
                 // Send real-time position update
                 this.socket.emit('player_move', { x: Player.x, y: Player.y, zoneId: Player.currentZoneId });
             }
             // --- End Emit ---


            // --- Trigger initial music start on first move (after interaction) ---
            if (!this.musicStarted && AudioManager.isAudioReady() && !Player.currentIndoorPlaceId) {
                AudioManager.startMusicPlayback();
                this.musicStarted = true;
            }
        }
    },

     /**
     * Callback function for when player position changes significantly (e.g., setPosition)
     */
    playerMoved: function() {
        // This function is called by Player.setPosition
        // Update the minimap and coordinates display when player position changes
        Minimap.update();
        // console.log("Game notified of player movement.");
     },

     /**
      * Central function to save the entire game state.
      * Saves player state and non-zone world state to player save file via /api/save.
      * Dynamic object state (harvested, mob health) should be saved to the database via separate API calls (TODO).
      */
     saveGame: async function() {
         console.log("Attempting to save game (player state)..."); // Removed "+ zones"
         let playerSaveSuccess = false;
         // let zoneSaveSuccess = false; // Removed zone save flag

         // --- 1. Save Player State (+ non-zone world state) ---
         const playerData = Player.saveState();
         const worldData = World.saveState(); // This now EXCLUDES zones

         if (!playerData || !worldData) {
             console.error("Failed to get state from Player or World module for player save.");
             this.addMessage("Error preparing player save data.", "error"); // Don't force scroll save error
             // Optionally decide if you want to proceed with zone save even if player save fails
         } else {
             const playerGameState = {
                 player: playerData,
                 world: worldData // Contains non-zone world state
             };
             playerSaveSuccess = await Utils.saveGameState(playerGameState); // Uses /api/save
             if (!playerSaveSuccess) {
                 console.error("Failed to save player game state via API.");
                 this.addMessage("Failed to save player data.", "error"); // Don't force scroll save error
             } else {
                 console.log("Player game state saved successfully via API.");
             }
         }

         // --- 2. Save Zones Data (REMOVED) ---
         // Saving dynamic objects (plants, mobs state) and potentially structures
         // should now happen via specific API calls to update the database,
         // likely triggered by the actions themselves (harvesting, combat ending, building).
         // The monolithic save of the entire world structure to world_zones.json is removed.
         console.log("Zone data saving to world_zones.json is disabled. Dynamic state should be saved to DB.");


         // --- 3. Show Feedback ---
         if (playerSaveSuccess) { // Only check player save success now
             Utils.showSaveIndicator(); // Show visual feedback
             console.log("Player game state saved successfully.");
         }
         // Error messages for player save failure are handled above.
     },

     /**
     * Process a player command
     * @param {string} commandText - The command text
     */
    merchantChatState: {
        active: false,
        merchantId: null
    },
    merchantConversations: {},
    pendingMerchantAction: null,

    processCommand: async function(commandText) {
        // Add the command to the output
        this.addMessage(`> ${commandText}`, 'player-command', true); // Force scroll player command echo

        // --- Combat Command Handling ---
        if (Combat.inCombat) {
            const combatCommands = ['attack', 'a', 'defend', 'd', 'flee', 'f'];
            if (combatCommands.includes(commandText.toLowerCase().trim())) {
                Combat.processCommand(commandText.toLowerCase().trim()); // Combat module handles its own messages/scrolling
            } else {
                this.addMessage("Invalid command during combat. Use Attack (a), Defend (d), or Flee (f).", 'error', true); // Force scroll error
            }
            // UI.scrollOutputToBottom(); // Remove this, addMessage handles it now
            return; // Don't process other commands while in combat
        }

        // If in merchant chat mode, treat all input as dialogue (and not in combat)
        if (this.merchantChatState.active) {
            const merchantId = this.merchantChatState.merchantId;
            const zonePos = Player.getCurrentZoneAndPosition();

            // Get merchant based on whether player is in an indoor place or not
            let merchant;
            if (zonePos.indoorPlaceId) {
                merchant = World.getNpc(merchantId);
                console.log(`[processCommand] Getting merchant ${merchantId} from World.npcs:`, merchant);
            } else {
                merchant = World.findObjectInZone(zonePos.zoneId, merchantId)?.object;
                console.log(`[processCommand] Getting merchant ${merchantId} from zone ${zonePos.zoneId}:`, merchant);
            }

            if (!merchant) {
                console.error(`[processCommand] Merchant ${merchantId} not found`);
                this.addMessage("The merchant is no longer here.", 'error', true); // Force scroll error
                this.merchantChatState.active = false;
                return;
            }

            const input = commandText.toLowerCase();

            if (input.includes('exit') || input.includes('bye') || input.includes('leave')) {
                this.addMessage(`You end the conversation with ${merchant.name}.`, 'system', true); // Force scroll exit message
                this.merchantChatState.active = false;
                return;
            }

            if (input.includes('list') || input.includes('wares') || input.includes('what do you have') || input.includes('show')) {
                this.showMerchantInventory(merchant);
                return;
            }

            if (input.includes('buy') || input.includes('get') || input.includes('purchase')) {
                // Parse quantity and item name from input (e.g., "buy 6 bread")
                let quantity = 1;
                let itemName = null;
                const buyMatch = input.match(/buy\s+(\d+)\s+(.+)/) || input.match(/get\s+(\d+)\s+(.+)/) || input.match(/purchase\s+(\d+)\s+(.+)/);
                if (buyMatch) {
                    quantity = parseInt(buyMatch[1], 10);
                    itemName = buyMatch[2].trim();
                } else {
                    // Fallback: find item name as before
                    itemName = merchant.merchantInventory
                        .filter(e => e.sells)
                        .map(e => {
                            const item = World.getItem(e.id);
                            return item ? item.name.toLowerCase() : e.id.toLowerCase();
                        })
                        .find(name => input.includes(name));
                }
                if (itemName) {
                    console.log(`[MerchantChat] Buying: ${quantity} x ${itemName}`);
                    this.buyFromMerchant(merchant, itemName, quantity);
                    return;
                } else {
                    this.addMessage(`${merchant.name} says: "I don't sell that item."`, 'npc-dialogue', true); // Force scroll response
                    return;
                }
            }

            if (input.includes('sell')) {
                // Parse quantity and item name from input (e.g., "sell 10 apple")
                let quantity = 1;
                let itemName = null;
                const sellMatch = input.match(/sell\s+(\d+)\s+(.+)/);
                if (sellMatch) {
                    quantity = parseInt(sellMatch[1], 10);
                    itemName = sellMatch[2].trim();
                } else {
                    // Fallback: find item name as before
                    itemName = merchant.merchantInventory
                        .filter(e => e.buys)
                        .map(e => {
                            const item = World.getItem(e.id);
                            return item ? item.name.toLowerCase() : e.id.toLowerCase();
                        })
                        .find(name => input.includes(name));
                }
                if (itemName) {
                    console.log(`[MerchantChat] Selling: ${quantity} x ${itemName}`);
                    this.sellToMerchant(merchant, itemName, quantity);
                    return;
                } else {
                    this.addMessage(`${merchant.name} says: "I don't buy that item."`, 'npc-dialogue', true); // Force scroll response
                    return;
                }
            }

            this.addMessage(`${merchant.name} says: "I'm sorry, I didn't understand. You can ask to buy, sell, or see my wares."`, 'npc-dialogue', true); // Force scroll response
            return;
        }

        // Parse the command
        const parts = commandText.toLowerCase().trim().split(' ');
        const command = parts[0];
        const args = parts.slice(1);

        if (command === 'talk' && args.length > 0) {
            const merchantId = args[0];
            const zonePos = Player.getCurrentZoneAndPosition();
            const merchant = World.findObjectInZone(zonePos.zoneId, merchantId)?.object;
            if (merchant && merchant.merchantInventory) {
                this.merchantChatState.active = true;
                this.merchantChatState.merchantId = merchantId;
                if (!this.merchantConversations[merchantId]) this.merchantConversations[merchantId] = [];
                this.addMessage(`You start a conversation with ${merchant.name}. Type /exit to stop talking.`, 'system', true); // Force scroll start message
                const greeting = merchant.dialogue?.greeting || `${merchant.name} looks at you expectantly.`;
                this.addMessage(`${merchant.name}: "${greeting}"`, 'npc-dialogue', true); // Force scroll greeting
            } else {
                this.addMessage("There's no merchant by that name nearby.", 'error', true); // Force scroll error
            }
            return;
        }

        if (command === 'exit') {
            if (this.merchantChatState.active) {
                this.addMessage(`You end the conversation with the merchant.`, 'system', true); // Force scroll exit message
                this.merchantChatState.active = false;
            } else {
                this.addMessage("You're not talking to anyone.", 'error', true); // Force scroll error
            }
            return;
        }

        // Merchant commands
        if (command === 'list' || (command === 'list' && args[0] === 'jules')) {
            const zonePos = Player.getCurrentZoneAndPosition();
            const jules = World.findObjectInZone(zonePos.zoneId, 'jules')?.object;
            if (jules && jules.merchantInventory) {
                Game.showMerchantInventory(jules); // This function will force scroll
            } else {
                Game.addMessage("Jules isn't nearby.", 'error', true); // Force scroll error
            }
            return;
        }

        if (command === 'buy' && args.length > 0) {
            let quantity = 1;
            let itemName = args.join(' ');
            // Check if the first argument is a number (quantity)
            const potentialQuantity = parseInt(args[0], 10);
            if (!isNaN(potentialQuantity) && potentialQuantity > 0) {
                quantity = potentialQuantity;
                itemName = args.slice(1).join(' '); // Item name is the rest
            }

            const zonePos = Player.getCurrentZoneAndPosition();
            // Find the merchant the player is currently interacting with (if any)
            const currentMerchantId = (window.DialogueEngine && DialogueEngine.active) ? DialogueEngine.currentNpc?.id : this.merchantChatState.merchantId;
            let merchant;
            if (currentMerchantId) {
                 merchant = World.getNpc(currentMerchantId) || World.findObjectInZone(zonePos.zoneId, currentMerchantId)?.object;
            }

            if (merchant && merchant.merchantInventory) {
                Game.buyFromMerchant(merchant, itemName, quantity);
            } else {
                // Fallback to Jules if not talking to anyone specific (or handle error)
                const jules = World.findObjectInZone(zonePos.zoneId, 'jules')?.object;
                 if (jules && jules.merchantInventory) {
                     Game.buyFromMerchant(jules, itemName, quantity); // This function will force scroll
                 } else {
                     Game.addMessage("No merchant nearby to buy from.", 'error', true); // Force scroll error
                 }
            }
            return;
        }

        if (command === 'sell' && args.length > 0) {
            let quantity = 1;
            let itemName = args.join(' ');
            // Check if the first argument is a number (quantity)
            const potentialQuantity = parseInt(args[0], 10);
            if (!isNaN(potentialQuantity) && potentialQuantity > 0) {
                quantity = potentialQuantity;
                itemName = args.slice(1).join(' '); // Item name is the rest
            }

            const zonePos = Player.getCurrentZoneAndPosition();
             // Find the merchant the player is currently interacting with (if any)
             const currentMerchantId = (window.DialogueEngine && DialogueEngine.active) ? DialogueEngine.currentNpc?.id : this.merchantChatState.merchantId;
             let merchant = null;
             if (currentMerchantId) {
                  merchant = World.getNpc(currentMerchantId) || World.findObjectInZone(zonePos.zoneId, currentMerchantId)?.object;
             }

            if (merchant && merchant.merchantInventory) {
                Game.sellToMerchant(merchant, itemName, quantity);
            } else {
                 // Fallback to Jules if not talking to anyone specific (or handle error)
                 const jules = World.findObjectInZone(zonePos.zoneId, 'jules')?.object;
                  if (jules && jules.merchantInventory) {
                      Game.sellToMerchant(jules, itemName, quantity); // This function will force scroll
                  } else {
                      Game.addMessage("No merchant nearby to sell to.", 'error', true); // Force scroll error
                  }
            }
            return;
        }

        // Process the command
        switch (command) {
            case 'help':
                this.showHelp();
                break;

            case 'look':
            case 'l':
                this.lookAround();
                break;

            case 'enter':
                if (args.length > 0) {
                    this.enterStructure(args.join(' '));
                } else {
                    this.enterNearestStructure();
                }
                break;

            case 'exit':
            case 'leave':
                this.exitIndoorPlace();
                break;

            case 'inventory':
            case 'inv':
            case 'i':
                this.showInventory();
                break;

            case 'use':
                if (args.length === 0) {
                    this.addMessage('Use what? Try "use [item name]" or click the use button.', 'error', true); // Force scroll error
                } else {
                    this.useItem(args.join(' ')); // This function will force scroll its messages
                }
                break;

            case 'map':
            case 'm':
                this.useItem('map'); // Assuming 'map' item opens the world map modal
                break;

            case 'clear':
                this.clearOutput();
                break;

            case 'name':
                UI.showNameChangeModal();
                break;

            // --- Horse Commands ---
            case 'horse':
                if (typeof HorseSystem !== 'undefined') {
                    HorseSystem.handleHorseCommand(args);
                } else {
                    this.addMessage('Horse system not available.', 'error', true);
                }
                break;

            case 'mount':
                if (typeof HorseSystem !== 'undefined') {
                    HorseSystem.mountHorse();
                } else {
                    this.addMessage('Horse system not available.', 'error', true);
                }
                break;

            case 'dismount':
                if (typeof HorseSystem !== 'undefined') {
                    HorseSystem.dismountHorse();
                } else {
                    this.addMessage('Horse system not available.', 'error', true);
                }
                break;

            case 'stable':
                if (typeof StableUI !== 'undefined') {
                    StableUI.openStableUI();
                    this.addMessage('Opening your stable...', 'system', true);
                } else {
                    this.addMessage('Stable system not available.', 'error', true);
                }
                break;

            // --- Admin Command: give ---
            case 'give':
                if (!Player.isAdmin) {
                    this.addMessage('You do not have permission to use this command.', 'error', true); // Force scroll error
                } else if (args.length === 0) {
                    this.addMessage('Usage: give [item_id] [quantity?]', 'error', true); // Force scroll error
                } else {
                    const itemId = args[0];
                    const quantity = parseInt(args[1]) || 1; // Default to 1 if quantity not specified or invalid
                    const item = World.getItem(itemId);
                    if (item) {
                        Player.addItem(item, quantity);
                        this.addMessage(`Gave ${quantity} x ${item.name} to player.`, 'system', true); // Force scroll success
                    } else {
                        this.addMessage(`Item ID "${itemId}" not found.`, 'error', true); // Force scroll error
                    }
                }
                break;

            // --- Admin Command: teleport ---
            case 'gotozone':
            case 'teleport':
                if (!Player.isAdmin) {
                    this.addMessage('You do not have permission to use this command.', 'error', true); // Force scroll error
                } else if (args.length === 0) {
                    this.addMessage('Usage: gotozone [zone_id]', 'error', true); // Force scroll error
                    const zoneList = Object.keys(World.getAllZones()).join(', ');
                    this.addMessage(`Available zones: ${zoneList}`, 'system', true); // Force scroll info
                } else {
                    const targetZoneId = args[0];
                    const targetZone = World.getZone(targetZoneId);
                    if (targetZone) {
                        // Calculate center of the target zone
                        const targetX = (targetZone.boundaries.minX + targetZone.boundaries.maxX) / 2;
                        const targetY = (targetZone.boundaries.minY + targetZone.boundaries.maxY) / 2;

                        const wasMounted = Player.mountedHorse !== null; // Store mounted state before teleport
                        let mountedHorseData = null;

                        // If player is mounted, store the horse data before teleport
                        if (wasMounted && Player.mountedHorse) {
                            mountedHorseData = { ...Player.mountedHorse };
                        }

                        Player.setPosition(targetX, targetY, targetZoneId); // Set position first
                        await World.loadZoneObjects(targetZoneId); // Await the loading

                        // If player was mounted before teleport, ensure the horse is respawned and player remains mounted
                        if (wasMounted && mountedHorseData && typeof HorseSystem !== 'undefined') {
                            // First make sure player is dismounted to reset any stale state
                            Player.mountedHorse = null;

                            // Then despawn any existing horse to ensure clean state
                            HorseSystem.despawnPlayerHorse();

                            // Now spawn a fresh horse in the new zone
                            const spawnedHorse = HorseSystem.spawnPlayerHorse();

                            // If horse was successfully spawned, ensure player remains mounted
                            if (spawnedHorse) {
                                // Set the player as mounted on the horse
                                Player.mountedHorse = spawnedHorse;

                                // Update the horse's position to match the player's
                                World.updateHorsePosition(spawnedHorse.id, Player.x, Player.y);

                                // Send horse position update to server
                                HorseSystem.sendHorsePositionUpdate(spawnedHorse.id, Player.x, Player.y, true);
                            }
                        }
                        // If player has a horse but is not mounted, ensure it follows to the new zone
                        else if (Player.horse && !Player.mountedHorse && typeof HorseSystem !== 'undefined') {
                            // Despawn any existing horse to ensure clean state
                            HorseSystem.despawnPlayerHorse();

                            // Spawn a fresh horse in the new zone
                            HorseSystem.spawnPlayerHorse();
                        }

                        // Refresh zone chat if player is viewing zone chat
                        if (typeof ChatTabs !== 'undefined' && typeof ChatTabs.refreshZoneChat === 'function') {
                            ChatTabs.refreshZoneChat();
                        }

                        // Optionally trigger lookAround or minimap update *after* loading
                        this.lookAround(); // Refresh view after loading new zone objects (will force scroll)
                        this.addMessage(`Teleported to ${targetZone.name} (${targetZoneId}).`, 'success', true); // Force scroll success
                        this.updateLocationDisplay(targetZone); // Update location display
                        WorldMap.update(); // Update world map highlight
                        // Start/Stop music based on destination
                        if (!Player.currentIndoorPlaceId) { // Check after setPosition
                            AudioManager.resumeMusic(); // Use resumeMusic
                        } else {
                            AudioManager.stopMusic(); // Use stopMusic
                        }
                    } else {
                        this.addMessage(`Zone ID "${targetZoneId}" not found.`, 'error', true); // Force scroll error
                        const zoneList = Object.keys(World.getAllZones()).join(', ');
                        this.addMessage(`Available zones: ${zoneList}`, 'system', true); // Force scroll info
                    }
                }
                break;

             // --- Admin Command: givegold ---
             case 'givegold':
                 if (!Player.isAdmin) {
                     this.addMessage('You do not have permission to use this command.', 'error', true); // Force scroll error
                 } else if (args.length === 0 || isNaN(parseInt(args[0])) || parseInt(args[0]) <= 0) {
                     this.addMessage('Usage: givegold [amount]', 'error', true); // Force scroll error
                 } else {
                     const amount = parseInt(args[0]);
                     Player.addGold(amount); // Use the new Player function
                     this.addMessage(`Gave ${amount} gold to player.`, 'system', true); // Force scroll success
                 }
                 break;

             // --- Admin Command: build ---
             case 'build':
                 if (!Player.isAdmin) {
                     this.addMessage('You do not have permission to use this command.', 'error', true); // Force scroll error
                 } else {
                     // Get current zone using the Player object
                     const playerPos = Player.getCurrentZoneAndPosition();
                     if (playerPos && playerPos.zone && playerPos.zone.id) {
                         this.addMessage('Opening build mode...', 'system', true); // Force scroll success
                         UI.openBuildMode(playerPos.zone.id);
                     } else {
                         this.addMessage('Cannot open build mode: Current zone information not found.', 'error', true); // Force scroll error
                     }
                 }
                 break;

             // --- NEW Unstuck Command ---
             case 'unstuck':
                 this.unstuckPlayer();
                 break;

             // --- NEW Admin List Command ---
             case '/seeadmins': // Using slash prefix for clarity
                 if (this.socket && this.socket.connected) {
                     this.addMessage('Requesting admin list...', 'system-italic', true); // Force scroll request message
                     this.socket.emit('request_admin_list');
                 } else {
                     this.addMessage('Cannot request admin list: Not connected to server.', 'error', true); // Force scroll error
                 }
                 break;

             // --- NEW Player List Command ---
             case 'playerlist':
                 if (this.socket) {
                     this.addMessage('Requesting player list...', 'system-italic', true); // Force scroll request message
                     this.socket.emit('request_player_list');
                 } else {
                     this.addMessage('Cannot request player list: Not connected to server.', 'error', true); // Force scroll error
                 }
                 break;

             // --- Interaction commands need updating ---
             case 'harvest':
             case 'gather':
                 if (args.length === 0) {
                    this.addMessage('Harvest what? Look around to see available plants/trees.', 'error', true); // Force scroll error
                } else {
                    // Need to find object ID based on args (e.g., "harvest herb_123" or "harvest nearest herb")
                    this.addMessage('Harvest command needs updating for new system.', 'warning', true); // Force scroll warning
                    // this.harvestObject(args.join(' ')); // TODO: Implement find logic
                }
                break;
             case 'hunt':
                 if (args.length === 0) {
                    this.addMessage('Hunt what? Look around to see nearby animals.', 'error', true); // Force scroll error
                } else {
                     this.addMessage('Hunt command needs updating.', 'warning', true); // Force scroll warning
                    // this.huntObject(args.join(' ')); // TODO: Implement find logic
                }
                break;
              case 'fight':
              case 'attack':
                  if (args.length === 0) {
                      this.addMessage('Fight what? Look around to see nearby creatures.', 'error', true); // Force scroll error
                  } else {
                      const targetNameOrId = args.join(' ');
                      const playerPos = Player.getCurrentZoneAndPosition();
                      if (playerPos && playerPos.zone) {
                          // Find the target mob/animal by name or ID nearby
                          const interactionRadius = 50; // Same radius as lookAround
                          const nearbyObjects = World.getObjectsInRadius(playerPos.zone.id, playerPos.x, playerPos.y, interactionRadius, ['mobs', 'animals']);
                          const target = nearbyObjects.find(obj =>
                              obj.id === targetNameOrId ||
                              obj.name.toLowerCase() === targetNameOrId.toLowerCase()
                          );

                          if (target) {
                              // Call the Game.fightMob function which now initiates combat
                              this.fightMob(playerPos.zone.id, target.id); // This function will force scroll its messages
                          } else {
                              this.addMessage(`You don't see "${targetNameOrId}" nearby to fight.`, 'error', true); // Force scroll error
                          }
                      } else {
                           this.addMessage('Cannot determine your location to find a target.', 'error', true); // Force scroll error
                      }
                  }
                  break;

              case 'duel':
                  if (args.length === 0) {
                      this.addMessage('Duel whom? Specify an NPC name or ID.', 'error', true);
                  } else {
                      const targetNameOrId = args.join(' ');
                      const playerPos = Player.getCurrentZoneAndPosition();
                      if (playerPos && playerPos.zone) {
                          // Find the target NPC by name or ID nearby
                          const interactionRadius = 50;
                          const nearbyNpcs = World.getObjectsInRadius(playerPos.zone.id, playerPos.x, playerPos.y, interactionRadius, ['npc', 'npcs']);
                          const target = nearbyNpcs.find(obj =>
                              obj.id === targetNameOrId ||
                              obj.name.toLowerCase() === targetNameOrId.toLowerCase()
                          );

                          if (target) {
                              // Start a duel with the NPC
                              this.startDuel(target, 'npc');
                          } else {
                              this.addMessage(`You don't see "${targetNameOrId}" nearby to duel.`, 'error', true);
                          }
                      } else {
                          this.addMessage('Cannot determine your location to find a dueling partner.', 'error', true);
                      }
                  }
                  break;
              case 'examine':
             case 'ex':
                 if (args.length === 0) {
                    this.addMessage('Examine what? Look around first.', 'error', true); // Force scroll error
                } else {
                     this.addMessage('Examine command needs updating.', 'warning', true); // Force scroll warning
                    // this.examineObject(args.join(' ')); // TODO: Implement find logic
                }
                 break;

            // --- NEW Loot Command ---
            case 'loot': {
                if (Combat.inCombat) {
                    this.addMessage("You cannot loot during combat!", 'error', true); // Force scroll error
                    break;
                }
                if (args.length === 0) {
                    this.addMessage('Loot what? Specify the creature name or ID (e.g., "loot spider" or "loot spider_123").', 'error', true); // Force scroll error
                    break;
                }

                const targetNameOrId = args.join(' '); // Join args in case name has spaces, though unlikely for mobs
                const lootPlayerPos = Player.getCurrentZoneAndPosition();
                const maxLootDistance = 50; // Define a reasonable loot distance

                if (!lootPlayerPos || !lootPlayerPos.zone) {
                    this.addMessage('Cannot determine your location to loot.', 'error', true); // Force scroll error
                    break;
                }

                // Find nearby mobs
                const nearbyMobs = World.getObjectsInRadius(lootPlayerPos.zone.id, lootPlayerPos.x, lootPlayerPos.y, maxLootDistance, ['mobs']);
                // --- DEBUGGING ---
                // console.log(`[Game.processCommand - loot] nearbyMobs found by getObjectsInRadius (distance <= ${maxLootDistance}):`, JSON.stringify(nearbyMobs));
                // --- END DEBUGGING ---

                // Filter for potential targets matching name/ID, defeated, and not looted
                const lootPotentialTargets = nearbyMobs.filter(mob =>
                    (mob.id === targetNameOrId || mob.name.toLowerCase() === targetNameOrId.toLowerCase()) &&
                    mob.defeated &&
                    !mob.looted
                );

                if (lootPotentialTargets.length === 0) {
                    // Check if *any* mob with that name/ID exists nearby, even if not lootable, for better feedback
                    const anyMatchingMob = nearbyMobs.find(mob => mob.id === targetNameOrId || mob.name.toLowerCase() === targetNameOrId.toLowerCase());
                    if (anyMatchingMob) {
                        if (!anyMatchingMob.defeated) {
                            this.addMessage(`The ${anyMatchingMob.name} is still alive!`, 'warning', true); // Force scroll warning
                        } else if (anyMatchingMob.looted) {
                            this.addMessage(`The ${anyMatchingMob.name} has already been looted.`, 'info', true); // Force scroll info
                        } else {
                             // Should not happen based on filter logic, but as a fallback:
                             this.addMessage(`You cannot loot the ${anyMatchingMob.name} right now.`, 'error', true); // Force scroll error
                        }
                    } else {
                        this.addMessage(`You don't see a "${targetNameOrId}" nearby to loot.`, 'error', true); // Force scroll error
                    }
                    break;
                }

                if (lootPotentialTargets.length > 1) {
                    this.addMessage(`You see multiple defeated ${targetNameOrId}s nearby. Please specify which one by its ID (use 'look' to see IDs).`, 'warning', true); // Force scroll warning
                    break;
                }

                // Exactly one valid target found
                const targetMob = lootPotentialTargets[0];

                // All checks passed, call the loot handler
                Combat.handleLoot(targetMob, targetMob.id, lootPlayerPos.zone.id);
                // Combat.handleLoot will provide feedback messages

                // --- Refresh minimap after successful loot ---
                Minimap.update();
                // --- End Refresh ---
                break;
            }

            // --- NEW Mine Command ---
            case 'mine':
            case 'mineore': {
                if (args.length === 0) {
                    this.addMessage('Mine what? Look around to see nearby ores.', 'error', true); // Force scroll error
                    break;
                }

                const oreNameOrId = args.join(' ');
                const minePlayerPos = Player.getCurrentZoneAndPosition();
                const maxMineDistance = 30; // Define a reasonable mine distance (30m)

                if (!minePlayerPos || !minePlayerPos.zone) {
                    this.addMessage('Cannot determine your location to mine.', 'error', true); // Force scroll error
                    break;
                }

                // Find nearby ores
                const nearbyOres = World.getObjectsInRadius(minePlayerPos.zone.id, minePlayerPos.x, minePlayerPos.y, maxMineDistance, ['ores']);
                // --- DEBUGGING ---
                // console.log(`[Game.processCommand - mine] nearbyOres found by getObjectsInRadius (distance <= ${maxMineDistance}):`, JSON.stringify(nearbyOres));
                // --- END DEBUGGING ---

                // Filter for potential targets matching name/ID
                const minePotentialTargets = nearbyOres.filter(ore =>
                    ore.id === oreNameOrId || ore.name.toLowerCase() === oreNameOrId.toLowerCase()
                );

                if (minePotentialTargets.length === 0) {
                    this.addMessage(`You don't see a "${oreNameOrId}" nearby to mine.`, 'error', true); // Force scroll error
                    break;
                }

                if (minePotentialTargets.length > 1) {
                    this.addMessage(`You see multiple ${oreNameOrId}s nearby. Please specify which one by its ID (use 'look' to see IDs).`, 'warning', true); // Force scroll warning
                    break;
                }

                // Exactly one valid target found
                const targetOre = minePotentialTargets[0];

                // Check for equipped pickaxe in the new slot
                const pickaxe = Player.equipment.pickaxe;
                if (!pickaxe) {
                    this.addMessage("You need to equip a pickaxe in the pickaxe slot to mine.", 'warning', true); // Force scroll warning
                    break;
                }

                // All checks passed, call the mine handler
                Game.mineOre(minePlayerPos.zone.id, targetOre.id); // Call the dedicated function
                break;
            }

            default:
                this.addMessage(`Unknown command: "${command}". Type "help" for commands.`, 'error', true); // Force scroll error
                break;
        }

        // Scroll to the bottom of the output - REMOVED, handled by addMessage with forceScroll flag
        // UI.scrollOutputToBottom();
    },

    /**
     * Start a duel with an NPC or player
     * @param {Object} opponent - The opponent object (NPC or player)
     * @param {string} opponentType - The type of opponent ('npc' or 'player')
     * @param {Object} [rewards] - Optional rewards for winning the duel
     */
    startDuel: function(opponent, opponentType, rewards) {
        if (Combat.inCombat) {
            this.addMessage("You cannot start a duel while in combat!", 'error', true);
            return;
        }

        if (typeof Duel === 'undefined') {
            this.addMessage("The dueling system is not available.", 'error', true);
            return;
        }

        // Initialize the duel with optional rewards
        Duel.initiateDuel(opponent, opponentType, rewards);
    },

    /**
     * Initiates combat with a mob or animal.
     * @param {string} zoneId - The ID of the zone.
     * @param {string} creatureId - The ID of the mob or animal.
     */
    fightMob: function(zoneId, creatureId) {
        const objData = World.findObjectInZone(zoneId, creatureId);
        // Use plural types 'mobs' and 'animals' in the check
        if (!objData || (objData.type !== 'mobs' && objData.type !== 'animals')) {
            this.addMessage('Cannot fight that target.', 'error', true); // Force scroll error
            return;
        }

        if (objData.type === 'mobs' || objData.type === 'animals') {
            // Initiate combat using the Combat module for both mobs and animals
            Combat.initiateCombat(zoneId, creatureId);
            // Combat.initiateCombat will handle messages and UI display
        }
    },

    /**
     * Show the help text
     */
    showHelp: function() {
        const helpText = `
            <div class="help-text">
                <h3>Movement:</h3>
                <ul>
                    <li><strong>WASD Keys</strong> - Move your character</li>
                </ul>
                <h3>Commands:</h3>
                <ul>
                    <li><strong>look (l)</strong> - Look around your current location</li>
                    <li><strong>inventory (i)</strong> - Show your inventory</li>
                    <li><strong>use [item]</strong> - Use an item from your inventory</li>
                    <li><strong>map (m)</strong> - Open the world map</li>
                    <li><strong>name</strong> - Change your character's name</li>
                    <li><strong>clear</strong> - Clear the output</li>
                    <li><strong>playerlist</strong> - Show list of online players</li>
                    <li><strong>duel [npc]</strong> - Challenge an NPC to a duel</li>
                    <li><strong>horse [mount|dismount|pat|whistle|info]</strong> - Manage your horse</li>
                    <li><strong>mount</strong> - Mount your horse if nearby</li>
                    <li><strong>dismount</strong> - Dismount your horse</li>
                    <li><strong>pat horse</strong> - Pat your horse affectionately</li>
                    <li><strong>help (h)</strong> - Show this help text</li>
                </ul>
                 <h3>Admin Commands:</h3>
                 <ul>
                     <li><strong>give [item_id] [qty?]</strong> - Give yourself an item</li>
                     <li><strong>gotozone [zone_id]</strong> - Teleport to a zone</li>
                     <li><strong>givegold [amount]</strong> - Give yourself gold</li>
                     <li><strong>build</strong> - Open the build mode for the current zone</li>
                 </ul>
                 <h3>Interaction:</h3>
                 <p>Use the buttons that appear after using the 'look' command to interact with objects (Harvest, Hunt, Fight, Examine).</p>
            </div>
        `;
        this.addMessage(helpText, 'normal', true); // Force scroll help text
    },

    /**
     * Look around the current location, showing nearby objects.
     */
    lookAround: function() {
        const playerPos = Player.getCurrentZoneAndPosition();
        if (!playerPos || !playerPos.zone) {
            this.addMessage('You seem to be lost in the void.', 'error', true); // Force scroll error
            return;
        }

        // If player is in an indoor place, show the indoor place description
        if (playerPos.indoorPlaceId) {
            const indoorPlace = playerPos.indoorPlace;
            this.updateLocationDisplay(null, indoorPlace);

            // Show exit option
            const exitDiv = document.createElement('div');
            exitDiv.className = 'environment-elements-container';
            const exitElement = document.createElement('div');
            exitElement.className = 'environment-element exit';
            exitElement.innerHTML = `<strong>Exit</strong> <div><button class="exit-btn">Leave</button></div>`;
            exitDiv.appendChild(exitElement);
            this.addMessage(exitDiv.outerHTML, 'normal', true); // Force scroll exit info

            // Add event listener for exit button
            setTimeout(() => {
                const output = document.getElementById('output');
                output.querySelectorAll('.exit-btn').forEach(button => {
                    if (button.dataset.listenerAttached) return;
                    button.dataset.listenerAttached = 'true';
                    button.addEventListener('click', () => {
                        this.exitIndoorPlace();
                    });
                });
            }, 0);

            // Show NPCs if any
            if (indoorPlace.npcs && indoorPlace.npcs.length > 0) {
                this.addMessage('You see:', 'system', true); // Force scroll "You see:"
                const npcsDiv = document.createElement('div');
                npcsDiv.className = 'environment-elements-container';

                indoorPlace.npcs.forEach(npcId => {
                    const npc = World.getNpc(npcId);
                    if (npc) {
                        const npcElement = document.createElement('div');
                        npcElement.className = 'environment-element npcs';
                        let description = npc.description ? ` - ${npc.description}` : '';
                        npcElement.innerHTML = `<strong>${npc.name}</strong>${description} <div><button class="talk-btn" data-id="${npc.id}">Talk</button></div>`;
                        npcsDiv.appendChild(npcElement);
                    }
                });

                if (npcsDiv.children.length > 0) {
                    this.addMessage(npcsDiv.outerHTML, 'normal', true); // Force scroll NPC list

                    // Add event listeners for NPC interactions
                    setTimeout(() => {
                        const output = document.getElementById('output');
                        output.querySelectorAll('.talk-btn').forEach(button => {
                            if (button.dataset.listenerAttached) return;
                            button.dataset.listenerAttached = 'true';
                            button.addEventListener('click', () => {
                                const npcId = button.getAttribute('data-id');
                                this.talkToNpc(playerPos.zoneId, npcId);
                            });
                        });
                    }, 0);
                }
            }

            return;
        }

        // Normal outdoor zone handling
        const { zone, x: playerX, y: playerY } = playerPos;

        // Update main location display first
        this.updateLocationDisplay(zone);

        // Find nearby objects using a reasonable radius for interaction
        const interactionRadius = 50; // World units radius to show interactables
        const nearbyObjects = World.getObjectsInRadius(zone.id, playerX, playerY, interactionRadius, ['ores', 'plants', 'trees', 'animals', 'mobs', 'npcs', 'items', 'structures']);

        if (nearbyObjects.length === 0) {
            this.addMessage('You see nothing of interest nearby.', 'normal', true); // Force scroll "nothing nearby" message
            return;
        }

        this.addMessage('Nearby:', 'system', true); // Force scroll "Nearby:" message
        this.displayEnvironmentElements(nearbyObjects, zone.id); // Pass zoneId (this function will force scroll its content)
    },

    /**
     * Display environment elements (nearby objects) with interaction buttons.
     * @param {Array<Object>} objects - Array of nearby objects from World.getObjectsInRadius.
     * @param {string} zoneId - The ID of the current zone.
     */
    displayEnvironmentElements: function(objects, zoneId) {
        const container = document.createElement('div');
        container.className = 'environment-elements-container';

        objects.sort((a, b) => { // Sort by distance
            const distA = Math.hypot(a.x - Player.x, a.y - Player.y);
            const distB = Math.hypot(b.x - Player.y, b.y - Player.y);
            return distA - distB;
        });

        objects.forEach(obj => {
            // --- DEBUGGING ---
            // console.log(`Displaying object: ID=${obj.id}, Type=${obj.objectType}, Name=${obj.name}`);
            // Use plural types for logging check
            if (obj.objectType === 'plants' || obj.objectType === 'trees') {
                // console.log(`  Harvestable: ${obj.harvestable}, Harvested: ${obj.harvested}`);
            } else if (obj.objectType === 'animals') {
                // console.log(`  Huntable: ${obj.huntable}, Hunted: ${obj.hunted}, Defeated: ${obj.defeated}, Looted: ${obj.looted}`);
            } else if (obj.objectType === 'mobs') {
                // console.log(`  Defeated: ${obj.defeated}, Health: ${obj.health}/${obj.maxHealth}, Looted: ${obj.looted}`);
            } else if (obj.objectType === 'ores') {
                // console.log(`  Mineable: ${obj.mineable}, Harvested: ${obj.harvested}`);
            }
            // --- END DEBUGGING ---

            const element = document.createElement('div');
            element.className = `environment-element ${obj.objectType}`; // Use objectType for class

            // Add defeated class for styling if mob is defeated
            if (obj.objectType === 'mobs' && obj.defeated) {
                 element.classList.add('defeated');
             }

            // Handle missing name for structures
            if (obj.objectType === 'structures' && !obj.name && obj.typeId) {
                obj.name = Utils.capitalize(obj.typeId);
            }

            // Ensure object has a name
            if (!obj.name) {
                obj.name = obj.id || 'Unknown';
                console.warn(`Object missing name, using fallback: ${obj.name}`);
            }

             // Add the base class 'interaction-btn' to all buttons
             let buttons = `<button class="interaction-btn examine-btn" data-type="${obj.objectType}" data-id="${obj.id}">Examine</button>`;
             let status = '';

              // Use plural types in switch statement
              switch (obj.objectType) { // Use found.type
                  case 'horses':
                      // Add Mount button for player's horses
                      if (obj.ownerId === Player.id) {
                          if (!Player.mountedHorse) {
                              buttons += ` <button class="interaction-btn mount-btn" data-zone="${zoneId}" data-id="${obj.id}">Mount</button>`;
                          } else if (Player.mountedHorse.id === obj.id) {
                              buttons += ` <button class="interaction-btn dismount-btn" data-zone="${zoneId}" data-id="${obj.id}">Dismount</button>`;
                          }
                      }
                      break;
                  case 'structures':
                      // Add Enter button for structures
                      buttons += ` <button class="interaction-btn enter-btn" data-zone="${zoneId}" data-id="${obj.id}">Enter</button>`;
                       // Add Use button specifically for refineries, lumbermills, and crafting benches
                       if (obj.typeId === 'refinery') {
                           buttons += ` <button class="interaction-btn use-refinery-btn" data-id="${obj.id}">Use Refinery</button>`;
                       } else if (obj.typeId === 'lumbermill') {
                           buttons += ` <button class="interaction-btn use-lumbermill-btn" data-id="${obj.id}">Use Lumbermill</button>`;
                       } else if (obj.typeId === 'crafting_bench') {
                           buttons += ` <button class="interaction-btn use-crafting-bench-btn" data-id="${obj.id}">Use Crafting Bench</button>`;
                       }
                      break;
                  case 'ores':
                      if (obj.mineable) {
                          buttons += ` <button class="interaction-btn mine-btn harvest-btn" data-zone="${zoneId}" data-id="${obj.id}">Mine</button>`;
                          if (obj.harvested) status = ' (Mined)';
                      }
                      break;
                  case 'plants': // Corrected from 'plant'
                  case 'trees': // Corrected from 'tree'
                     if (obj.harvestable) {
                         buttons += ` <button class="interaction-btn harvest-btn" data-zone="${zoneId}" data-id="${obj.id}">Harvest</button>`;
                        if (obj.harvested) status = ' (Harvested)';
                     }
                     break;
                 case 'animals':
                     if (obj.defeated) {
                         status = ' (Defeated)';
                         // Only show buttons if not already looted
                         if (!obj.looted) {
                             // For animals, show skin button if it has a pelt, otherwise show loot button
                             if (obj.huntItem && obj.huntItem.includes('pelt')) {
                                 buttons += ` <button class="interaction-btn skin-btn" data-zone="${zoneId}" data-id="${obj.id}">Skin</button>`;
                             } else {
                                 buttons += ` <button class="interaction-btn loot-btn" data-zone="${zoneId}" data-id="${obj.id}">Loot</button>`;
                             }
                         } else {
                             status += ' (Looted)';
                         }
                     } else {
                         // Add Hunt button for huntable animals
                         if (obj.huntable && !obj.hunted) {
                             buttons += ` <button class="interaction-btn hunt-btn" data-zone="${zoneId}" data-id="${obj.id}">Hunt</button>`;
                         }
                         // Add Fight button for animals
                         buttons += ` <button class="interaction-btn fight-btn" data-zone="${zoneId}" data-id="${obj.id}">Fight</button>`;
                     }
                     break;
                 case 'mobs':  // Changed from 'mob' to 'mobs'
                     if (obj.defeated) {
                         status = ' (Defeated)';
                         // Only show buttons if not already looted
                         if (!obj.looted) {
                             // For mobs, show skin button if skinnable, otherwise show loot button
                             if (obj.skinnable) {
                                 buttons += ` <button class="interaction-btn skin-btn" data-zone="${zoneId}" data-id="${obj.id}">Skin</button>`;
                             } else {
                                 buttons += ` <button class="interaction-btn loot-btn" data-zone="${zoneId}" data-id="${obj.id}">Loot</button>`;
                             }
                         } else {
                             status += ' (Looted)';
                         }
                     } else {
                         // Only show fight button if mob is not defeated
                         buttons += ` <button class="interaction-btn fight-btn" data-zone="${zoneId}" data-id="${obj.id}">Fight</button>`;
                     }
                     break;
                 case 'npcs': // Corrected from 'npc' to 'npcs'
                     buttons += ` <button class="interaction-btn talk-btn" data-zone="${zoneId}" data-id="${obj.id}">Talk</button>`;
                     break;
                 case 'items': // Corrected from 'item' to 'items'
                      buttons += ` <button class="interaction-btn pickup-btn" data-zone="${zoneId}" data-id="${obj.id}">Pick Up</button>`;
                     break;
             } // End switch

            const distance = Math.hypot(obj.x - Player.x, obj.y - Player.y);
            let regenHtml = '';
            if (obj.objectType === 'ores' && obj.harvested) {
                const now = Date.now();
                const harvestTime = obj.harvestTime || 0;
                const respawn = World.environmentSettings.respawnTime;
                const msLeft = Math.max(0, (harvestTime + respawn) - now);
                const secs = (msLeft / 1000).toFixed(1);
                regenHtml = `<div class="regen-msg">Already mined, regenerating in ${secs}s</div>`;
                buttons = ''; // hide mine button to prevent wasted clicks
            }
            element.innerHTML = `
                <strong>${obj.name}</strong>${status} <span class="distance">(${distance.toFixed(0)}m)</span>
                ${regenHtml}
                <div>${buttons}</div>
            `;
            container.appendChild(element);
        });

        this.addMessage(container.outerHTML, 'normal', true); // Force scroll the environment elements list

        // Add event listeners AFTER adding the HTML to the DOM
        setTimeout(() => this.addInteractionListeners(zoneId), 0);
    },

    /**
     * Add event listeners for interaction buttons generated by displayEnvironmentElements.
     * @param {string} zoneId - The current zone ID.
     */
    addInteractionListeners: function(zoneId) {
        const output = document.getElementById('output');

        output.querySelectorAll('.examine-btn').forEach(button => {
            // Prevent adding duplicate listeners if look is called multiple times quickly
            if (button.dataset.listenerAttached) return;
            button.dataset.listenerAttached = 'true';
            button.addEventListener('click', () => {
                const objectId = button.getAttribute('data-id');
                const objectType = button.getAttribute('data-type'); // Need type for examine
                this.examineObject(zoneId, objectId, objectType);
            });
        });

        output.querySelectorAll('.enter-btn').forEach(button => {
            if (button.dataset.listenerAttached) return;
            button.dataset.listenerAttached = 'true';
            button.addEventListener('click', () => {
                const objectId = button.getAttribute('data-id');
                this.enterStructure(objectId);
            });
        });

        output.querySelectorAll('.harvest-btn').forEach(button => {
            if (button.dataset.listenerAttached) return;
            button.dataset.listenerAttached = 'true';
            button.addEventListener('click', () => {
                const objectId = button.getAttribute('data-id');
                // Directly call harvestPlant, which handles both plants and trees
                this.harvestPlant(zoneId, objectId);
            });
        });

         output.querySelectorAll('.hunt-btn').forEach(button => {
            if (button.dataset.listenerAttached) return;
            button.dataset.listenerAttached = 'true';
            button.addEventListener('click', () => {
                const objectId = button.getAttribute('data-id');
                this.huntAnimal(zoneId, objectId);
            });
        });

         output.querySelectorAll('.fight-btn').forEach(button => {
            if (button.dataset.listenerAttached) return;
            button.dataset.listenerAttached = 'true';
            button.addEventListener('click', () => {
                const objectId = button.getAttribute('data-id');
                this.fightMob(zoneId, objectId);
            });
        });

        // Add listeners for talk, pickup etc. similarly
         output.querySelectorAll('.talk-btn').forEach(button => {
             if (button.dataset.listenerAttached) return;
             button.dataset.listenerAttached = 'true';
             button.addEventListener('click', () => {
                 const objectId = button.getAttribute('data-id');
                 Game.talkToNpc(zoneId, objectId);
             });
         });

         output.querySelectorAll('.pickup-btn').forEach(button => {
             if (button.dataset.listenerAttached) return;
             button.dataset.listenerAttached = 'true';
             button.addEventListener('click', () => {
                 const objectId = button.getAttribute('data-id');
                 this.pickupItem(zoneId, objectId); // Call the implemented function
             });
         });

         output.querySelectorAll('.mine-btn').forEach(button => {
            // Remove existing listener if any (to allow re-adding for debug)
            button.replaceWith(button.cloneNode(true));
        });
        // Re-query buttons after replacement
        output.querySelectorAll('.mine-btn').forEach(button => {
            button.addEventListener('click', (_event) => {
                const objectId = button.getAttribute('data-id');
                const zoneId = button.getAttribute('data-zone');

                 Game.mineOre(zoneId, objectId);
             });
         });

         // Add listener for the new refinery "Use" button
         output.querySelectorAll('.use-refinery-btn').forEach(button => {
             if (button.dataset.listenerAttached) return;
             button.dataset.listenerAttached = 'true';
             button.addEventListener('click', () => {
                 const objectId = button.getAttribute('data-id');
                 UI.openRefineryPopup(objectId); // Call the refinery function
             });
         });

         // Add listener for the new lumbermill "Use" button
         output.querySelectorAll('.use-lumbermill-btn').forEach(button => {
             if (button.dataset.listenerAttached) return;
             button.dataset.listenerAttached = 'true';
             button.addEventListener('click', () => {
                 const objectId = button.getAttribute('data-id');
                 UI.openLumbermillPopup(objectId); // Call the new lumbermill function
             });
         });

         // Add listener for the new crafting bench "Use" button
         output.querySelectorAll('.use-crafting-bench-btn').forEach(button => {
             if (button.dataset.listenerAttached) return;
             button.dataset.listenerAttached = 'true';
             button.addEventListener('click', () => {
                 const objectId = button.getAttribute('data-id');
                 UI.openCraftingBenchPopup(objectId); // Call the crafting bench function
             });
         });

         // Add listener for the loot button
         output.querySelectorAll('.loot-btn').forEach(button => {
             if (button.dataset.listenerAttached) return;
             button.dataset.listenerAttached = 'true';
             button.addEventListener('click', () => {
                 const objectId = button.getAttribute('data-id');
                 const zoneId = button.getAttribute('data-zone');
                 this.lootObject(zoneId, objectId);
             });
         });

         // Add listener for the skin button
         output.querySelectorAll('.skin-btn').forEach(button => {
             if (button.dataset.listenerAttached) return;
             button.dataset.listenerAttached = 'true';
             button.addEventListener('click', () => {
                 const objectId = button.getAttribute('data-id');
                 const zoneId = button.getAttribute('data-zone');
                 this.skinObject(zoneId, objectId);
             });
         });

         // Add listener for the mount button
         output.querySelectorAll('.mount-btn').forEach(button => {
             if (button.dataset.listenerAttached) return;
             button.dataset.listenerAttached = 'true';
             button.addEventListener('click', () => {
                 const objectId = button.getAttribute('data-id');
                 if (typeof HorseSystem !== 'undefined') {
                     HorseSystem.mountHorse(objectId);
                 } else {
                     this.addMessage('Horse system not available.', 'error', true);
                 }
             });
         });

         // Add listener for the dismount button
         output.querySelectorAll('.dismount-btn').forEach(button => {
             if (button.dataset.listenerAttached) return;
             button.dataset.listenerAttached = 'true';
             button.addEventListener('click', () => {
                 if (typeof HorseSystem !== 'undefined') {
                     HorseSystem.dismountHorse();
                 } else {
                     this.addMessage('Horse system not available.', 'error', true);
                 }
             });
         });
     },

    /**
     * Examine an object (plant, tree, animal, mob, npc, item).
     * @param {string} zoneId - The ID of the zone the object is in.
     * @param {string} objectId - The ID of the object to examine.
     * @param {string} objectType - The type of the object ('plant', 'tree', etc.).
     */
    examineObject: function(zoneId, objectId, _objectType) {
        const found = World.findObjectInZone(zoneId, objectId);
        if (!found) { // Simplified check as type is already known/checked by caller potentially
            this.addMessage('It seems to be gone.', 'error', true); // Force scroll error
            return;
        }
        const obj = found.object;

        // Check if object has required properties
        if (!obj) {
            console.error(`[examineObject] Object found but data is null or undefined for ID: ${objectId}`);
            this.addMessage('Unable to examine this object.', 'error', true);
            return;
        }

        // Handle missing name or description
        let name = obj.name;
        let description = obj.description;

        // For structures, try to get name and description from building_types.json
        if (found.type === 'structures' && obj.typeId && (!name || !description)) {
            if (typeof World !== 'undefined' && World.buildingTypes && World.buildingTypes[obj.typeId]) {
                const buildingType = World.buildingTypes[obj.typeId];
                if (!name) name = buildingType.name || Utils.capitalize(obj.typeId);
                if (!description) description = buildingType.description || `A ${name.toLowerCase()}.`;
            }
        }

        // Final fallbacks if still missing
        if (!name) name = (obj.typeId ? Utils.capitalize(obj.typeId) : 'Unknown');
        if (!description) description = `A ${name.toLowerCase()}.`;

        // Use found.type which is guaranteed by findObjectInZone
        let details = `<div class="environment-element ${found.type}"><h4>${name}</h4><p>${description}</p>`;

        switch(found.type) { // Use found.type
            case 'horses':
                // Add horse-specific details
                details += `<p>Color: ${obj.color}</p>`;
                details += `<p>Level: ${obj.level}</p>`;
                details += `<p>Speed: ${obj.speed}x</p>`;
                details += `<p>Stamina: ${obj.stamina}</p>`;
                if (obj.ownerId === Player.id) {
                    details += '<p>This is your horse.</p>';
                } else if (obj.ownerName) {
                    details += `<p>This horse belongs to ${obj.ownerName}.</p>`;
                }
                break;
            case 'structures':
                // Add structure-specific details
                if (obj.typeId) {
                    details += `<p>Type: ${Utils.capitalize(obj.typeId)}</p>`;
                }
                // Add walkable info for dirt paths
                if (obj.typeId === 'dirt_path') {
                    details += '<p>This is a walkable path.</p>';
                }
                break;
            case 'plants': // Corrected from 'plant'
            case 'trees': // Corrected from 'tree'
                details += obj.harvestable ? `<p>Can be harvested${obj.harvested ? ' (already harvested)' : ''}.</p>` : '<p>Cannot be harvested.</p>';
                break;
            case 'animals':
                details += obj.huntable ? `<p>Can be hunted${obj.hunted ? ' (already hunted)' : ''}.</p>` : '<p>Cannot be hunted.</p>';
                break;
            case 'mobs':
                details += `<p>Health: ${obj.health}/${obj.maxHealth}, Damage: ${obj.damage}</p>`;
                details += obj.defeated ? '<p>Defeated.</p>' : (obj.hostile ? '<p>Hostile!</p>' : '<p>Looks peaceful.</p>');
                break;
            case 'npcs':
                // Add more NPC details if available
                break;
            case 'items':
                details += `<p>Category: ${Utils.capitalize(obj.category || 'misc')}</p>`;
                details += obj.usable ? '<p>Can be used.</p>' : '';
                details += obj.consumable ? '<p>Can be consumed.</p>' : '';
                break;
            case 'ores':
                details += obj.mineable ? `<p>Can be mined${obj.harvested ? ' (already mined)' : ''}.</p>` : '<p>Cannot be mined.</p>';
                break;
        }
        details += `</div>`;
        this.addMessage(details, 'normal', true); // Force scroll examine details
    },

    /**
     * Harvest a plant or tree.
     * @param {string} zoneId - The ID of the zone.
     * @param {string} objectId - The ID of the plant/tree.
     */
    harvestPlant: function(zoneId, objectId) {
        const objData = World.findObjectInZone(zoneId, objectId);

        // Check if object exists and is harvestable type
        if (!objData || (objData.type !== 'plants' && objData.type !== 'trees')) {
            this.addMessage('Cannot harvest that.', 'error', true); // Force scroll error
            return;
        }

        const targetObject = objData.object;
        const objectType = objData.type; // 'plants' or 'trees'

        // --- Handle Plants (Instant Harvest) ---
        if (objectType === 'plants') {
            const item = World.harvestPlant(zoneId, objectId); // Use specific World function
            if (item) {
                this.addMessage(`You harvested the ${targetObject.name} and obtained ${item.name}.`, 'success', true); // Force scroll success
                Player.addItem(item);
            } else {
                this.addMessage(`You cannot harvest the ${targetObject.name} right now.`, 'error', true); // Force scroll error
            }
            return; // Done with plants
        }

        // --- Handle Trees (Timed Action) ---
        if (objectType === 'trees') {
            console.log(`[Game.harvestPlant] Attempting to chop tree: ${objectId}`);

            // 1. Check if already harvested
            if (targetObject.harvested) {
                console.warn('[DEBUG] Tree has already been harvested:', targetObject);
                this.addMessage('This tree has already been harvested.', 'error', true); // Force scroll error
                return;
            }

            // 2. Check for equipped axe (assuming 'axe' slot)
            const axe = Player.equipment.axe; // Check the 'axe' slot
            if (!axe) {
                this.addMessage("You need to equip an axe in the axe slot to chop trees.", 'warning', true); // Force scroll warning
                return;
            }

            // 3. Check distance
            const distance = Math.hypot(targetObject.x - Player.x, targetObject.y - Player.y);
            const maxChopDistance = 30; // Consistent with mining
            if (distance > maxChopDistance) {
                this.addMessage(`You are too far away from the ${targetObject.name} to chop it (must be within ${maxChopDistance}m).`, 'error', true); // Force scroll error
                return;
            }

            // 4. Check level requirement
            const axeLevel = axe.level || 0; // Default to 0 if axe has no level
            const treeLevel = targetObject.level || 1; // Default to 1 if tree has no level
            const maxLevelDifference = 2;
            if (axeLevel < treeLevel - maxLevelDifference) {
                 this.addMessage(`Your axe (Lvl ${axeLevel}) is not strong enough for this ${targetObject.name} (Lvl ${treeLevel}). You need at least Level ${treeLevel - maxLevelDifference}.`, 'warning', true); // Force scroll warning
                 return;
            }

            // 5. Calculate chopping time
            const baseTime = 4000; // Base chopping time in ms (e.g., 4 seconds)
            let timeMultiplier = 1.0;
            const levelDiff = treeLevel - axeLevel;

            if (levelDiff > 0) { // Tree is higher level than axe (but within allowed range)
                timeMultiplier += levelDiff * 0.25; // Add 25% time per level difference
            } else if (levelDiff < 0) { // Axe is higher level than tree
                 timeMultiplier -= Math.abs(levelDiff) * 0.1; // Reduce 10% time per level difference (optional speed up)
            }
            // Add skill modifiers here if needed later
            // if (Player.skills.woodcutting) timeMultiplier -= (Player.skills.woodcutting.level * 0.05);

            const choppingTime = Math.max(1000, Math.round(baseTime * timeMultiplier)); // Ensure minimum time (e.g., 1 second)

            // 6. Show progress bar (using a new UI function)
            // Assuming UI.showChoppingBox exists or will be created, similar to showMiningBox
            UI.showChoppingBox({ // Use a different function name for clarity
                treeName: targetObject.name,
                choppingTime: choppingTime,
                onComplete: () => {
                    // This code runs *after* the progress bar finishes
                    console.log(`[Game.harvestPlant - onComplete] Chopping finished for ${objectId}`);
                    // Re-check axe just in case it was unequipped
                    if (!Player.equipment.axe) {
                        this.addMessage("Chopping cancelled - axe was unequipped.", 'warning', true); // Force scroll warning
                        return;
                    }
                    // Re-check distance in case player moved away
                    const finalDistance = Math.hypot(targetObject.x - Player.x, targetObject.y - Player.y);
                    if (finalDistance > maxChopDistance) {
                         this.addMessage(`You moved too far away from the ${targetObject.name}. Chopping cancelled.`, 'warning', true); // Force scroll warning
                         return;
                    }

                    // Perform the actual harvesting action
                    const item = World.harvestTree(zoneId, objectId); // Use specific World function
                    if (item) {
                        this.addMessage(`You chopped down the ${targetObject.name} and obtained ${item.name}.`, 'success', true); // Force scroll success
                        Player.addItem(item);
                        // Optionally, save game state
                        // Game.saveGame();
                    } else {
                        // World.harvestTree might fail if state changed
                        this.addMessage(`You couldn't chop the ${targetObject.name}. Perhaps it's already gone?`, 'error', true); // Force scroll error
                    }
                }
            });

            // Add an initial message
            this.addMessage(`You start chopping the ${targetObject.name}...`, 'system', true); // Force scroll start message
        }
    },

    /**
     * Mine an ore
     * @param {string} zoneId - The ID of the zone
     * @param {string} oreId - The ID of the ore to mine
     */
    mineOre: function(zoneId, oreId) {
        const objData = World.findObjectInZone(zoneId, oreId);

        if (!objData || objData.type !== 'ores') {
            this.addMessage('Cannot mine that.', 'error', true); // Force scroll error
            return;
        }

        const ore = objData.object;
        if (!ore.mineable) {
            this.addMessage('This ore cannot be mined.', 'error', true); // Force scroll error
            return;
        }
        if (ore.harvested) {
            this.addMessage('This ore has already been mined.', 'error', true); // Force scroll error
            return;
        }

        // Check for equipped pickaxe again (important before starting timer)
        const pickaxe = Player.equipment.pickaxe;
        if (!pickaxe) {
            this.addMessage("You need to equip a pickaxe in the pickaxe slot to mine.", 'warning', true); // Force scroll warning
            return;
        }

        // Add distance check right before starting the mining timer
        const distance = Math.hypot(ore.x - Player.x, ore.y - Player.y);
        const maxMineDistance = 30; // Set the required distance here
        if (distance > maxMineDistance) {
            this.addMessage(`You are too far away from the ${ore.name} to mine it (must be within ${maxMineDistance}m).`, 'error', true); // Force scroll error
            return;
        }

        // TODO: Add check for pickaxe level vs ore requirement if needed

        const baseTime = 3000; // Base mining time in ms (e.g., 3 seconds)
        let timeMultiplier = 1.0; // Placeholder for skill/tool modifiers
        // Example: Adjust multiplier based on pickaxe level or mining skill
        // if (pickaxe.level) timeMultiplier -= (pickaxe.level * 0.1); // 10% faster per level
        // if (Player.skills.mining) timeMultiplier -= (Player.skills.mining.level * 0.05); // 5% faster per skill level

        const miningTime = Math.max(500, Math.round(baseTime * timeMultiplier)); // Ensure minimum time

        // Show the progress bar UI
        UI.showMiningBox({
            oreName: ore.name,
            miningTime: miningTime,
            onComplete: () => {
                // This code runs *after* the progress bar finishes
                console.log(`[Game.mineOre - onComplete] Mining finished for ${oreId}`);
                // Re-check pickaxe just in case it was unequipped during mining
                if (!Player.equipment.pickaxe) {
                    this.addMessage("Mining cancelled - pickaxe was unequipped.", 'warning', true); // Force scroll warning
                    return;
                }
                // Perform the actual mining action
                const item = World.mineOre(zoneId, oreId);
                if (item) {
                    this.addMessage(`You mined the ${ore.name} and obtained ${item.name}.`, 'success', true); // Force scroll success
                    Player.addItem(item);
                    // Optionally, save game state after successful mining
                    // Game.saveGame();
                } else {
                    // World.mineOre might fail if the ore state changed (e.g., someone else mined it)
                    this.addMessage(`You couldn't mine the ${ore.name}. Perhaps it's already gone?`, 'error', true); // Force scroll error
                }
            }
        });

        // Add an initial message to indicate mining has started
        this.addMessage(`You start mining the ${ore.name}...`, 'system', true); // Force scroll start message
    },
     harvestTree: function(zoneId, objectId) { // Keep separate for potential different logic/tools later
        this.harvestPlant(zoneId, objectId); // For now, just call the combined one
     },


    /**
     * Hunt an animal.
     * @param {string} zoneId - The ID of the zone.
     * @param {string} animalId - The ID of the animal.
     */
    huntAnimal: function(zoneId, animalId) {
        console.log(`[Game.huntAnimal] Called for zone=${zoneId}, animalId=${animalId}`); // DEBUG
        const objData = World.findObjectInZone(zoneId, animalId);
        console.log(`[Game.huntAnimal] World.findObjectInZone returned:`, objData); // DEBUG
         if (!objData || objData.type !== 'animals') {
             console.error(`[Game.huntAnimal] Check failed: objData is null or type is not 'animals'.`); // DEBUG
             this.addMessage('Cannot hunt that.', 'error', true); return; // Force scroll error
        }

        const item = World.huntAnimal(zoneId, animalId);
        if (item) {
            this.addMessage(`You hunted the ${objData.object.name} and obtained ${item.name}.`, 'success', true); // Force scroll success
            Player.addItem(item);
            this.lookAround(); // This will force scroll
        }
    },

    /**
     * Loot a defeated mob or animal.
     * @param {string} zoneId - The ID of the zone.
     * @param {string} objectId - The ID of the mob or animal.
     */
    lootObject: function(zoneId, objectId) {
        const objData = World.findObjectInZone(zoneId, objectId);
        if (!objData || (objData.type !== 'mobs' && objData.type !== 'animals')) {
            this.addMessage('Cannot loot that.', 'error', true);
            return;
        }

        const targetObject = objData.object;

        // Check if already looted
        if (targetObject.looted) {
            this.addMessage(`The ${targetObject.name} has already been looted.`, 'info', true);
            return;
        }

        // Check if defeated
        if (!targetObject.defeated) {
            this.addMessage(`The ${targetObject.name} is still alive!`, 'warning', true);
            return;
        }

        // Handle looting based on object type
        if (objData.type === 'mobs') {
            // Use the Combat.handleLoot function for mobs
            Combat.handleLoot(targetObject, objectId, zoneId);
        } else if (objData.type === 'animals') {
            // For animals, use the huntItem property if available
            if (targetObject.huntItem) {
                const item = World.getItem(targetObject.huntItem);
                if (item) {
                    this.addMessage(`You search the ${targetObject.name}'s remains...`, 'system', true);
                    this.addMessage(`You found ${item.name}.`, 'success', true);
                    Player.addItem(item);
                    World.markAnimalAsLooted(zoneId, objectId);
                } else {
                    this.addMessage(`You search the ${targetObject.name}'s remains but find nothing of value.`, 'normal', true);
                    World.markAnimalAsLooted(zoneId, objectId);
                }
            } else {
                this.addMessage(`You search the ${targetObject.name}'s remains but find nothing of value.`, 'normal', true);
                World.markAnimalAsLooted(zoneId, objectId);
            }
        }

        // Don't automatically refresh the display with lookAround
        // Let the player use the look command if they want to see what's around
    },

    /**
     * Skin a defeated mob or animal.
     * @param {string} zoneId - The ID of the zone.
     * @param {string} objectId - The ID of the mob or animal.
     */
    skinObject: function(zoneId, objectId) {
        const objData = World.findObjectInZone(zoneId, objectId);
        if (!objData || (objData.type !== 'mobs' && objData.type !== 'animals')) {
            this.addMessage('Cannot skin that.', 'error', true);
            return;
        }

        const targetObject = objData.object;

        // Check if already looted
        if (targetObject.looted) {
            this.addMessage(`The ${targetObject.name} has already been skinned.`, 'info', true);
            return;
        }

        // Check if defeated
        if (!targetObject.defeated) {
            this.addMessage(`The ${targetObject.name} is still alive!`, 'warning', true);
            return;
        }

        // Check if player has a skinning knife equipped
        if (!Player.equipment.skinning_knife) {
            this.addMessage("You need to equip a skinning knife to skin this creature.", 'warning', true);
            return;
        }

        // Get the skinning knife level
        const skinningKnife = Player.equipment.skinning_knife;
        const skinningKnifeLevel = skinningKnife.level || 1;

        // Determine the item to drop based on the creature type
        let itemId = null;
        let baseDefinition = null;

        if (objData.type === 'mobs') {
            // For mobs, get the base definition to find the loot table
            const baseId = Combat.determineMobType(targetObject, objectId);
            baseDefinition = World.getMobDefinition(baseId);

            // Check if the mob is skinnable
            if (baseDefinition && baseDefinition.skinnable) {
                // Use the first pelt item from the loot table that has "pelt" in its ID
                if (baseDefinition.lootTable && Array.isArray(baseDefinition.lootTable)) {
                    for (const lootEntry of baseDefinition.lootTable) {
                        if (lootEntry.itemId && lootEntry.itemId.includes('pelt')) {
                            itemId = lootEntry.itemId;
                            break;
                        }
                    }
                }

                // If no pelt found in loot table, use the dropItem if it exists
                if (!itemId && baseDefinition.dropItem && baseDefinition.dropItem.includes('pelt')) {
                    itemId = baseDefinition.dropItem;
                }
            }
        } else if (objData.type === 'animals') {
            // For animals, use the huntItem if it exists and is a pelt
            if (targetObject.huntItem && targetObject.huntItem.includes('pelt')) {
                itemId = targetObject.huntItem;
            } else {
                // Get the animal definition to check for additional properties
                const animalDef = World.animalTypes[targetObject.id];
                if (animalDef && animalDef.skinItem) {
                    itemId = animalDef.skinItem;
                } else if (animalDef && animalDef.dropItem && animalDef.dropItem.includes('pelt')) {
                    itemId = animalDef.dropItem;
                }
            }
        }

        // If no specific pelt item was found, use a default based on creature level
        if (!itemId) {
            const creatureLevel = targetObject.level || 1;
            switch (creatureLevel) {
                case 1: itemId = 'small_pelt'; break;
                case 2: itemId = 'medium_pelt'; break;
                case 3: itemId = 'large_pelt'; break;
                default: itemId = 'small_pelt';
            }
        }

        // Get the item definition
        const item = World.getItem(itemId);

        if (item) {
            // Determine quantity based on skinning knife level
            // Higher level knives can get more pelts
            const baseQuantity = 1;
            const bonusQuantity = Math.floor(Math.random() * (skinningKnifeLevel > 3 ? 2 : 1));
            const quantity = baseQuantity + bonusQuantity;

            this.addMessage(`You carefully skin the ${targetObject.name}...`, 'system', true);

            // Add the item to the player's inventory - passing true to suppress the automatic message
            // since we'll show our own message
            Player.addItem(item, quantity, true);

            // Show our custom message
            this.addMessage(`You obtained ${quantity} x ${item.name}.`, 'success', true);

            // Mark the creature as looted
            if (objData.type === 'mobs') {
                World.markMobAsLooted(zoneId, objectId);
            } else {
                World.markAnimalAsLooted(zoneId, objectId);
            }
        } else {
            this.addMessage(`You try to skin the ${targetObject.name} but fail to get anything useful.`, 'warning', true);
        }

        // Don't automatically refresh the display with lookAround
        // Let the player use the look command if they want to see what's around
    },

    /**
     * Talk to an NPC (used by Talk button and commands).
     * @param {string} zoneId - The ID of the zone.
     * @param {string} npcId - The ID of the NPC.
     */
    talkToNpc: function(zoneId, npcId) {
        // Check if player is in an indoor place
        const playerPos = Player.getCurrentZoneAndPosition();
        let npc;
        if (playerPos && playerPos.indoorPlaceId) {
            // Get NPC from global NPC list (for indoor places)
            npc = World.getNpc(npcId);
            if (!npc) {
                console.error(`[talkToNpc] NPC ${npcId} not found in indoor place`);
                this.addMessage("There's no one there to talk to.", 'error', true); // Force scroll error
                return;
            }
            console.log(`[talkToNpc] Found NPC in indoor place: ${npc.name}`);
        } else {
            // Normal outdoor NPC handling
            const npcData = World.findObjectInZone(zoneId, npcId);
            if (!(npcData && (npcData.type === 'npc' || npcData.type === 'npcs'))) {
                console.error(`[talkToNpc] NPC ${npcId} not found in zone ${zoneId}`);
                this.addMessage("There's no one there to talk to.", 'error', true); // Force scroll error
                return;
            }
            npc = npcData.object;
            console.log(`[talkToNpc] Found NPC in zone: ${npc.name}`);
        }

        // Prevent starting a new dialogue if one is already active
        if (window.DialogueEngine && DialogueEngine.active) {
            this.addMessage("Finish your current conversation before starting a new one.", "warning", true); // Force scroll warning
            return;
        }

        // NEW: Use DialogueEngine if NPC has a dialogueTree
        if (npc.dialogueTree && window.DialogueEngine) {
            DialogueEngine.startDialogue(npc, () => {
                // Callback when dialogue ends
                this.merchantChatState.active = false;
                this.merchantChatState.merchantId = null;
            });
            return;
        }

        // Legacy fallback: old merchant/one-line dialogue
        this.merchantChatState.active = true;
        this.merchantChatState.merchantId = npcId;
        if (!this.merchantConversations[npcId]) this.merchantConversations[npcId] = [];
        const greeting = npc.dialogue?.greeting || `${npc.name} looks at you expectantly.`;
        this.addMessage(`You start a conversation with ${npc.name}. Type /exit to stop talking.`, 'system', true); // Force scroll start message
        this.addMessage(`${npc.name}: "${greeting}"`, 'npc-dialogue', true); // Force scroll greeting
    },

     showMerchantInventory: function(merchant) {
        if (!merchant.merchantInventory || merchant.merchantInventory.length === 0) {
            this.addMessage(`${merchant.name} has nothing for sale.`, 'npc-dialogue', true); // Force scroll response
            return;
        }
        let list = `<strong>${merchant.name}'s wares:</strong><ul>`;
        for (const entry of merchant.merchantInventory) {
            const item = World.getItem(entry.id);
            if (item) {
                list += `<li>${item.name} (${entry.price} gold) - ${item.description}</li>`;
            } else {
                list += `<li>${entry.id} (${entry.price} gold)</li>`;
            }
        }
        list += '</ul>';
        this.addMessage(list, 'npc-dialogue', true); // Force scroll inventory list
    },

    buyFromMerchant: function(merchant, itemName, quantity = 1) {
        const entry = merchant.merchantInventory.find(e => {
            const item = World.getItem(e.id);
            return item && item.name.toLowerCase() === itemName.toLowerCase();
        });
        if (!entry) {
            this.addMessage(`${merchant.name} says: "I don't have that item."`, 'npc-dialogue', true); // Force scroll response
            return;
        }
        const item = World.getItem(entry.id);
        // Use nullish coalescing to properly handle price of 0
        const price = entry.price !== undefined ? entry.price : 1;
        const totalPrice = price * quantity;

        // Handle confirmation for large purchases
        if (quantity > 5) {
            if (!confirm(`Are you sure you want to buy ${quantity} ${item.name} for ${totalPrice} gold?`)) {
                this.addMessage("Transaction cancelled.", 'system', true); // Force scroll cancel message
                return;
            }
        }

        // Special handling for free items (price = 0)
        if (totalPrice === 0) {
            Player.addItem(item, quantity);
            this.addMessage(`${merchant.name} gives you ${quantity} ${item.name} for free.`, 'success', true); // Force scroll success
            return;
        }

        // Normal handling for paid items
        if (!Player.removeGold(totalPrice)) {
            this.addMessage(`${merchant.name} says: "You don't have enough gold. It costs ${totalPrice}."`, 'npc-dialogue', true); // Force scroll response
            return;
        }

        Player.addItem(item, quantity);
        this.addMessage(`You bought ${quantity} ${item.name} for ${totalPrice} gold.`, 'success', true); // Force scroll success
    },

    sellToMerchant: function(merchant, itemName, quantity = 1) {
        const inventoryEntry = Player.inventory.find(entry => {
            const item = World.getItem(entry.id);
            return item && item.name.toLowerCase() === itemName.toLowerCase() && entry.quantity > 0;
        });
        if (!inventoryEntry) {
            this.addMessage(`You don't have any ${itemName} to sell.`, 'error', true); // Force scroll error
            return;
        }
        // Find merchant's price for this item, or default to 1
        const merchantEntry = merchant.merchantInventory.find(e => {
            const item = World.getItem(e.id);
            return item && item.name.toLowerCase() === itemName.toLowerCase();
        });
        // Use nullish coalescing to properly handle price of 0
        const basePrice = merchantEntry && merchantEntry.price !== undefined ? merchantEntry.price : 1;
        const sellPrice = Math.floor(basePrice * 0.5); // 50% of buy price
        if (quantity > inventoryEntry.quantity) {
            this.addMessage(`You only have ${inventoryEntry.quantity} ${itemName} to sell.`, 'error', true); // Force scroll error
            return;
        }
        const totalSellPrice = sellPrice * quantity;
        if (quantity > 5) {
            if (!confirm(`Are you sure you want to sell ${quantity} ${itemName} for ${totalSellPrice} gold?`)) {
                this.addMessage("Transaction cancelled.", 'system', true); // Force scroll cancel message
                return;
            }
        }
        Player.removeItem(inventoryEntry.id, quantity);
        Player.addGold(totalSellPrice);
        this.addMessage(`You sold ${quantity} ${itemName} for ${totalSellPrice} gold.`, 'success', true); // Force scroll success
    },

    /**
      * Pick up an item from the ground.
      * @param {string} zoneId - The ID of the zone.
      * @param {string} groundItemId - The ID of the item object on the ground.
      */
     pickupItem: function(zoneId, groundItemId) {
         const groundItemData = World.findObjectInZone(zoneId, groundItemId);

         // Correct check to use plural 'items'
         if (!groundItemData || groundItemData.type !== 'items') {
             console.error(`[Game.pickupItem] Check failed: groundItemData is null or type is not 'items'. Type was: ${groundItemData?.type}`); // DEBUG
             this.addMessage("It's no longer there.", 'error', true); // Force scroll error
             return;
         }

         const groundItemObject = groundItemData.object;
         // Get the original item definition using the stored itemId
         const itemDefinition = World.getItem(groundItemObject.itemId);

         if (!itemDefinition) {
              this.addMessage(`Error: Cannot pick up item - definition missing for ID ${groundItemObject.itemId}.`, 'error', true); // Force scroll error
              return;
         }

         // Add the item to player inventory (using the definition)
         // Assuming dropped items are always quantity 1 for now
         const added = Player.addItem(itemDefinition, 1);

         if (added) {
             // Remove item from world zone.objects.items
             const zone = World.getZone(zoneId);
             if (zone && zone.objects.items) {
                 zone.objects.items = zone.objects.items.filter(i => i.id !== groundItemId);
                 World.saveState(); // Save world changes
                 this.addMessage(`You pick up the ${itemDefinition.name}.`, 'success', true); // Force scroll success
                 this.lookAround(); // Refresh nearby items display (will force scroll)
             } else {
                  this.addMessage(`Error removing item ${groundItemId} from zone ${zoneId}.`, 'error', true); // Force scroll error
             }
         } else {
              // This might happen if addItem fails (e.g., inventory full if limits implemented)
              this.addMessage(`You couldn't pick up the ${itemDefinition.name}.`, 'error', true); // Force scroll error
         }
    },


    /**
     * Show the player's inventory (using Player module directly now)
     */
    showInventory: function() {
        const inventoryItems = Player.inventory; // Get inventory array directly

        if (inventoryItems.length === 0) {
            this.addMessage('Your inventory is empty.', 'system', true); // Force scroll empty message
            return;
        }

        let inventoryText = '<div class="inventory-list"><h3>Inventory:</h3><div class="environment-elements-container">';

        inventoryItems.forEach(inventoryEntry => {
            // Get full item details from World using the ID
            const item = World.getItem(inventoryEntry.id);
            if (!item) {
                 console.error(`Inventory item with ID ${inventoryEntry.id} not found in World.items`);
                 return; // Skip this item if definition is missing
            }

            // --- DEBUGGING ---
            console.log(`[showInventory] Processing: ${item.name} (ID: ${item.id})`);
            console.log(`  Category: ${item.category}, Level: ${item.level} (Type: ${typeof item.level})`);
            const isEquippableTool = (item.category === 'tool' && typeof item.level !== 'undefined');
            console.log(`  Is Equippable Tool? ${isEquippableTool}`);
            // --- END DEBUGGING ---

            // Generate buttons: Always show Examine, Use, Drop. Add Eat/Equip conditionally.
            let buttonsHtml = `
                <button class="examine-btn" data-type="item" data-id="${item.id}">Examine</button>
                <button class="use-btn" data-id="${item.id}">Use</button>
            `;
            if (item.category === 'consumables') {
                 // Add "Eat" button for consumables (calls useItem)
                 buttonsHtml += ` <button class="eat-btn" data-id="${item.id}">Eat</button>`;
            }
            // Add Equip button for weapons, armor, and tools with a level (pickaxes, axes)
            if (item.category === 'weapon' || item.category === 'armor' || (item.category === 'tool' && typeof item.level !== 'undefined')) {
                 buttonsHtml += ` <button class="equip-btn" data-id="${item.id}">Equip</button>`;
            }
             buttonsHtml += ` <button class="drop-btn" data-id="${item.id}">Drop</button>`;


             inventoryText += `
                <div class="environment-element item">
                    <strong>${item.name}</strong> (${inventoryEntry.quantity})
                    <div>
                        ${buttonsHtml}
                    </div>
                </div>
            `;
        });

        inventoryText += '</div></div>';
        this.addMessage(inventoryText, 'normal', true); // Force scroll inventory list

        // Add event listeners (similar structure, but target different buttons)
        setTimeout(() => {
            const output = document.getElementById('output');
             output.querySelectorAll('.examine-btn[data-type="item"]').forEach(button => {
                 if (button.dataset.listenerAttached) return; button.dataset.listenerAttached = 'true';
                 button.addEventListener('click', () => this.examineInventoryItem(button.getAttribute('data-id')));
             });
             output.querySelectorAll('.use-btn').forEach(button => { // Handles general "Use"
                 if (button.dataset.listenerAttached) return; button.dataset.listenerAttached = 'true';
                 button.addEventListener('click', () => this.useItem(button.getAttribute('data-id')));
             });
             output.querySelectorAll('.eat-btn').forEach(button => { // Handles specific "Eat" for consumables
                 if (button.dataset.listenerAttached) return; button.dataset.listenerAttached = 'true';
                 button.addEventListener('click', () => this.useItem(button.getAttribute('data-id'))); // Still calls useItem
             });
             output.querySelectorAll('.equip-btn').forEach(button => {
                 if (button.dataset.listenerAttached) return; button.dataset.listenerAttached = 'true';
                 button.addEventListener('click', () => {
                     const itemId = button.getAttribute('data-id');
                     Player.equipItem(itemId); // Directly call Player.equipItem
                 });
             });
             output.querySelectorAll('.drop-btn').forEach(button => {
                 if (button.dataset.listenerAttached) return; button.dataset.listenerAttached = 'true';
                 button.addEventListener('click', () => this.dropItem(button.getAttribute('data-id')));
             });
        }, 0);
    },

    /**
     * Examine an item from the inventory.
     * @param {string} itemId - The ID of the item to examine.
     */
    examineInventoryItem: function(itemId) {
        const inventoryEntry = Player.inventory.find(i => i.id === itemId); // Find inventory entry
        if (inventoryEntry) {
            // Get full item details from World
            const item = World.getItem(inventoryEntry.id);
            if (!item) {
                 this.addMessage(`Error: Item definition missing for ID ${itemId}.`, 'error', true); // Force scroll error
                 return;
            }
            // Use the examineObject logic, but pass the item object directly
            let details = `<div class="environment-element item"><h4>${item.name}</h4><p>${item.description}</p>`;
            details += `<p>Category: ${Utils.capitalize(item.category || 'misc')}</p>`;
            details += `<p>Quantity: ${inventoryEntry.quantity}</p>`; // Use quantity from inventory entry
            details += item.usable ? '<p>Can be used.</p>' : '';
            details += item.consumable ? '<p>Can be consumed.</p>' : '';
            // Add weapon/armor stats if applicable
            details += `</div>`;
            this.addMessage(details, 'normal', true); // Force scroll details
        } else {
            this.addMessage('You don\'t have that item.', 'error', true); // Force scroll error
        }
    },

    /**
     * Drop an item from inventory.
     * @param {string} itemId - The ID of the item to drop.
     */
    dropItem: function(itemId) {
        const inventoryEntry = Player.inventory.find(i => i.id === itemId);
        if (!inventoryEntry) {
            this.addMessage("You don't have that item to drop.", 'error', true); // Force scroll error
            return;
        }

        const itemDefinition = World.getItem(inventoryEntry.id);
        if (!itemDefinition) {
             this.addMessage(`Error: Cannot drop item - definition missing for ID ${itemId}.`, 'error', true); // Force scroll error
             return;
        }

        const playerPos = Player.getCurrentZoneAndPosition();
        if (!playerPos || !playerPos.zone) {
             this.addMessage("Cannot drop item here.", 'error', true); // Force scroll error
             return;
        }

        // Create a ground item object
        const groundItem = {
            id: `${itemDefinition.id}_${Utils.generateId()}`, // Unique ID for the ground instance
            type: 'item', // Specific type for ground items
            name: itemDefinition.name,
            description: `Dropped: ${itemDefinition.description}`,
            itemId: itemDefinition.id, // Store the original item ID
            x: playerPos.x,
            y: playerPos.y,
            category: itemDefinition.category // Keep category for potential filtering/display
            // Add other relevant properties if needed
        };

        // Add to world zone
        const zone = playerPos.zone;
        if (!zone.objects.items) {
            zone.objects.items = []; // Initialize if it doesn't exist
        }
        zone.objects.items.push(groundItem);

        // Remove one from player inventory
        const removed = Player.removeItem(itemId, 1);

        if (removed) {
            this.addMessage(`You drop ${itemDefinition.name}.`, 'system', true); // Force scroll drop message
            // World.saveState(); // Removed direct save call
            Game.saveGame(); // Call central save
            this.showInventory(); // Refresh inventory display (will force scroll)
            // Optionally call lookAround() if drop should immediately show item on ground
            // this.lookAround();
        } else {
            this.addMessage("You don't have that item to drop.", 'error', true); // Force scroll error (shouldn't happen if check above works)
        }
    },

    /**
     * Use an item (handles text command and button click).
     * @param {string} itemNameOrId - The name or ID of the item to use.
     */
    useItem: function(itemNameOrId) {
        // Find the inventory entry by ID or name (requires getting full definition for name check)
        let inventoryEntry = Player.inventory.find(entry => {
            if (entry.id === itemNameOrId) return true;
            const itemDef = World.getItem(entry.id);
            return itemDef && itemDef.name.toLowerCase() === itemNameOrId.toLowerCase();
        });

        if (inventoryEntry) {
            Player.useItem(inventoryEntry.id); // Delegate to Player.useItem using the ID (Player.useItem should force scroll its messages)
            // Player.useItem handles messages and removing if consumable
        } else {
            this.addMessage(`You don't have "${itemNameOrId}".`, 'error', true); // Force scroll error
        }
    },

    /**
     * Clear the output log.
     */
    clearOutput: function() {
        const output = document.getElementById('output');

        // Save the sticky leave bar if it exists
        const stickyBar = document.getElementById('sticky-leave-bar');
        const hasStickyBar = !!stickyBar;

        output.innerHTML = '';

        // If there was a sticky bar, add it back
        if (hasStickyBar) {
            const playerPos = Player.getCurrentZoneAndPosition();
            if (playerPos && playerPos.indoorPlace) {
                this.addStickyLeaveBar(playerPos.indoorPlace.name);
            }
        }

        this.updateLocationDisplay(); // Show current location again
    },

    /**
     * Update the location display in the UI based on the player's current zone or indoor place.
     * @param {Object} [zone=null] - Optional: The zone object. If null, gets from Player.
     * @param {Object} [indoorPlace=null] - Optional: The indoor place object.
     */
    updateLocationDisplay: function(zone = null, indoorPlace = null) {
        if (!zone && !indoorPlace) {
            const playerPos = Player.getCurrentZoneAndPosition();
            if (playerPos) {
                zone = playerPos.zone;
                indoorPlace = playerPos.indoorPlace;
            }
        }

        if (indoorPlace) {
            // Indoor place takes precedence if provided
            UI.updateLocationDisplay(indoorPlace);

            // Add sticky leave bar if player is in an indoor place
            this.addStickyLeaveBar(indoorPlace.name);
        } else if (zone) {
            UI.updateLocationDisplay(zone); // Pass the zone object to UI
        } else {
             UI.updateLocationDisplay({ name: 'Unknown Area', description: 'Lost in the void...' });
        }
    },

    /**
     * Enter a structure by name or ID
     * @param {string} structureNameOrId - The name or ID of the structure to enter
     */
    enterStructure: function(structureNameOrId) {
        const playerPos = Player.getCurrentZoneAndPosition();
        if (!playerPos || !playerPos.zone) {
            this.addMessage('You cannot enter anything from here.', 'error', true); // Force scroll error
            return;
        }

        // If already in an indoor place, exit first
        if (playerPos.indoorPlaceId) {
            this.addMessage('You are already inside. Type "exit" to leave first.', 'error', true); // Force scroll error
            return;
        }

        const { zone, x: playerX, y: playerY } = playerPos;

        // Find nearby structures
        const interactionRadius = 50;
        const nearbyObjects = World.getObjectsInRadius(zone.id, playerX, playerY, interactionRadius, ['structures']);

        // Find the structure by name or ID
        const structure = nearbyObjects.find(obj =>
            obj.id === structureNameOrId ||
            obj.name.toLowerCase() === structureNameOrId.toLowerCase()
        );

        if (!structure) {
            this.addMessage(`You don't see ${structureNameOrId} nearby.`, 'error', true); // Force scroll error
            return;
        }

        // Check if this structure has an associated indoor place
        const indoorPlace = World.findIndoorPlaceByStructure(structure.id);
        if (!indoorPlace) {
            this.addMessage(`You cannot enter ${structure.name}.`, 'error', true); // Force scroll error
            return;
        }

        // --- Audio Handling ---
        // Get audio path directly from the indoor place definition
        const indoorAudioPath = indoorPlace.indoorAudio || null;
        console.log(`Entering indoor place ${indoorPlace.id}. Audio path: ${indoorAudioPath}`);
        console.log(`Calling AudioManager.playIndoorMusic with path: ${indoorAudioPath}`);
        AudioManager.playIndoorMusic(indoorAudioPath);
        // --- End Audio Handling ---

        Player.setPosition(0, 0, zone.id, indoorPlace.id);

        // Add sticky leave bar at the top of the console
        this.addStickyLeaveBar(indoorPlace.name);

        this.addMessage(`You enter ${indoorPlace.name}.`, 'system', true); // Force scroll enter message
        this.lookAround(); // Show the indoor place description (will force scroll)
    },

    /**
     * Enter the nearest enterable structure
     */
    enterNearestStructure: function() {
        const playerPos = Player.getCurrentZoneAndPosition();
        if (!playerPos || !playerPos.zone) {
            this.addMessage('You cannot enter anything from here.', 'error', true); // Force scroll error
            return;
        }

        // If already in an indoor place, exit first
        if (playerPos.indoorPlaceId) {
            this.addMessage('You are already inside. Type "exit" to leave first.', 'error', true); // Force scroll error
            return;
        }

        const { zone, x: playerX, y: playerY } = playerPos;

        // Find nearby structures
        const interactionRadius = 50;
        const nearbyStructures = World.getObjectsInRadius(zone.id, playerX, playerY, interactionRadius, ['structures']);

        if (nearbyStructures.length === 0) {
            this.addMessage('There is nothing to enter nearby.', 'error', true); // Force scroll error
            return;
        }

        // Sort by distance
        nearbyStructures.sort((a, b) => {
            const distA = Math.hypot(a.x - playerX, a.y - playerY);
            const distB = Math.hypot(b.x - playerX, b.y - playerY);
            return distA - distB;
        });

        // Find the first structure that has an associated indoor place
        for (const structure of nearbyStructures) {
            const indoorPlace = World.findIndoorPlaceByStructure(structure.id);
            if (indoorPlace) {
                 // --- Audio Handling ---
                 // Get audio path directly from the indoor place definition
                 const indoorAudioPath = indoorPlace.indoorAudio || null;
                 console.log(`Entering nearest indoor place ${indoorPlace.id}. Audio path: ${indoorAudioPath}`);
                 console.log(`Calling AudioManager.playIndoorMusic for nearest structure with path: ${indoorAudioPath}`);
                 AudioManager.playIndoorMusic(indoorAudioPath);
                 // --- End Audio Handling ---

                Player.setPosition(0, 0, zone.id, indoorPlace.id);

                // Add sticky leave bar at the top of the console
                this.addStickyLeaveBar(indoorPlace.name);

                this.addMessage(`You enter ${indoorPlace.name}.`, 'system', true); // Force scroll enter message
                this.lookAround(); // Show the indoor place description (will force scroll)
                return;
            }
        }

        this.addMessage('There is nothing to enter nearby.', 'error', true); // Force scroll error
    },

    /**
     * Exit the current indoor place
     */
    exitIndoorPlace: function() {
        const playerPos = Player.getCurrentZoneAndPosition();
        if (!playerPos || !playerPos.indoorPlaceId) {
            this.addMessage('You are not inside any place.', 'error', true); // Force scroll error
            return;
        }

        const indoorPlace = playerPos.indoorPlace;
        if (!indoorPlace) {
            this.addMessage('Error: Indoor place not found.', 'error', true); // Force scroll error
            return;
        }

        // Get the exit coordinates from the indoor place definition
        const exitX = indoorPlace.exitX || 0;
        const exitY = indoorPlace.exitY || 0;
        const parentZone = indoorPlace.parentZone || playerPos.zoneId;

        // Exit to the parent zone at the specified coordinates
        Player.setPosition(exitX, exitY, parentZone, null);
        // Switch back to outdoor music
        AudioManager.playOutdoorMusic();

        // Remove the sticky leave bar
        this.removeStickyLeaveBar();

        // End any active NPC conversations
        if (this.merchantChatState.active) {
            this.merchantChatState.active = false;
            this.merchantChatState.merchantId = null;
        }

        // End any active dialogue
        if (window.DialogueEngine && DialogueEngine.active) {
            DialogueEngine.endDialogue();
        }

        this.addMessage(`You exit ${indoorPlace.name}.`, 'system', true); // Force scroll exit message
        this.lookAround(); // Show the outdoor location (will force scroll)
    },

    /**
     * Add a sticky leave bar at the top of the game console
     * @param {string} locationName - The name of the indoor place
     */
    addStickyLeaveBar: function(locationName) {
        // Remove any existing sticky leave bar first
        this.removeStickyLeaveBar();

        // Create the sticky leave bar
        const output = document.getElementById('output');
        if (!output) return;

        const stickyBar = document.createElement('div');
        stickyBar.className = 'sticky-leave-bar';
        stickyBar.id = 'sticky-leave-bar';

        // Add location name
        const locationSpan = document.createElement('span');
        locationSpan.className = 'location-name';
        locationSpan.textContent = locationName;
        stickyBar.appendChild(locationSpan);

        // Add leave button
        const leaveBtn = document.createElement('button');
        leaveBtn.className = 'leave-btn';
        leaveBtn.textContent = 'Leave';
        leaveBtn.addEventListener('click', () => {
            this.exitIndoorPlace();
        });
        stickyBar.appendChild(leaveBtn);

        // Insert at the top of the output
        if (output.firstChild) {
            output.insertBefore(stickyBar, output.firstChild);
        } else {
            output.appendChild(stickyBar);
        }
    },

    /**
     * Remove the sticky leave bar from the game console
     */
    removeStickyLeaveBar: function() {
        const stickyBar = document.getElementById('sticky-leave-bar');
        if (stickyBar) {
            stickyBar.remove();
        }
    },

    /**
     * Unstuck the player if they're stuck in a structure or other obstacle
     */
    unstuckPlayer: function() {
        const playerPos = Player.getCurrentZoneAndPosition();
        if (!playerPos) {
            this.addMessage('Cannot determine your position.', 'error', true);
            return;
        }

        // If player is in an indoor place, exit it
        if (playerPos.indoorPlaceId) {
            this.exitIndoorPlace();
            return;
        }

        const { zone, x: playerX, y: playerY } = playerPos;
        if (!zone) {
            this.addMessage('Cannot determine your current zone.', 'error', true);
            return;
        }

        // Find a safe position to teleport to
        // First, try to find a nearby path or road
        const interactionRadius = 100; // Larger radius to find a safe spot
        const nearbyStructures = World.getObjectsInRadius(zone.id, playerX, playerY, interactionRadius, ['structures']);

        // Look for a dirt path or road
        const path = nearbyStructures.find(obj =>
            obj.typeId === 'dirt_path' ||
            obj.typeId === 'road' ||
            obj.typeId === 'path'
        );

        if (path) {
            // Teleport to the path
            Player.setPosition(path.x, path.y, zone.id);
            this.addMessage('You have been moved to a nearby path.', 'success', true);
            this.lookAround();
            return;
        }

        // If no path found, move to the center of the zone
        const centerX = (zone.boundaries.minX + zone.boundaries.maxX) / 2;
        const centerY = (zone.boundaries.minY + zone.boundaries.maxY) / 2;

        const wasMounted = Player.mountedHorse !== null; // Store mounted state before teleport
        let mountedHorseData = null;

        // If player is mounted, store the horse data before teleport
        if (wasMounted && Player.mountedHorse) {
            mountedHorseData = { ...Player.mountedHorse };
        }

        Player.setPosition(centerX, centerY, zone.id);

        // If player was mounted before teleport, ensure the horse is respawned and player remains mounted
        if (wasMounted && mountedHorseData && typeof HorseSystem !== 'undefined') {
            // First make sure the horse is spawned in the new zone
            const spawnedHorse = HorseSystem.spawnPlayerHorse();

            // If horse was successfully spawned, ensure player remains mounted
            if (spawnedHorse) {
                // Set the player as mounted on the horse
                Player.mountedHorse = spawnedHorse;

                // Update the horse's position to match the player's
                World.updateHorsePosition(spawnedHorse.id, Player.x, Player.y);

                // Send horse position update to server
                HorseSystem.sendHorsePositionUpdate(spawnedHorse.id, Player.x, Player.y, true);
            }
        }

        this.addMessage('You have been moved to the center of the zone.', 'success', true);
        this.lookAround();
    },


    /**
     * Add a message to the output log.
     * @param {string} message - The HTML or text message.
     * @param {string} [type='normal'] - Message type ('normal', 'system', 'error', 'success', 'warning', 'player-command', 'npc-dialogue').
     * @param {boolean} [forceScroll=false] - Whether to force scrolling the output to the bottom.
     */
    addMessage: function(message, type = 'normal', forceScroll = false) {
        const output = document.getElementById('output');
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', `message-${type}`);
        messageDiv.innerHTML = message; // Allow HTML content
        output.appendChild(messageDiv);
        if (forceScroll) {
            UI.scrollMainOutputToBottom(); // Only scroll if forceScroll is true
        }
    },

     /**
     * Sends a chat message to the server with its type.
     * @param {string} messageText - The text content of the message.
     * @param {string} messageType - The type of the chat (e.g., 'world', 'recruit').
     */
    sendChatMessage: function(messageText, messageType) { // <--- MODIFIED: Now accepts two arguments
        // Basic validation for the inputs
        if (!messageText || typeof messageText !== 'string' || messageText.trim() === '') {
            console.warn('Game.sendChatMessage: Attempted to send empty or invalid message text.');
            // Optionally, notify UI or just return
            // if (typeof UI !== 'undefined' && UI.addSystemMessage) UI.addSystemMessage("Cannot send empty message.", "error");
            return;
        }
        if (!messageType || typeof messageType !== 'string' || messageType.trim() === '') {
            console.warn('Game.sendChatMessage: Message type is missing or invalid. Defaulting to "world".');
            messageType = 'world'; // A failsafe, though UI should always provide a valid one
        }

        if (this.socket && this.socket.connected) {
            // --- Prepare the payload object ---
            const messagePayload = {
                text: messageText,
                type: messageType
            };
            // --- End Prepare the payload object ---

            console.log(`Game: Sending chat message to server:`, messagePayload); // Log the object
            this.socket.emit('send_chat_message', messagePayload); // <--- MODIFIED: Send the object

            // The part about displaying own message immediately can be removed if
            // the server reliably broadcasts the message back to the sender as well.
            // If you keep it, it would also need to handle the 'type' for filtering.
            // For now, let's rely on server broadcast.
            // const username = localStorage.getItem('username') || 'You';
            // UI.displayChatMessage(username, messageText, false, null, null, messageType); // If you were to do it

        } else {
            console.error("Game.sendChatMessage: Cannot send chat message. Socket not connected or not available.");
            // Your existing error handling:
            if (typeof this.addMessage === 'function') { // Assuming Game.addMessage adds to some general log
                this.addMessage("Cannot send chat message. Not connected.", "error", true);
            } else if (typeof UI !== 'undefined' && UI.addSystemMessage === 'function') {
                UI.addSystemMessage("Cannot send chat message. Not connected.", "error");
            }
        }
    },

    /**
     * Handle interaction requests originating from minimap clicks.
     * @param {string} entityType - The type of the entity clicked (e.g., 'trees', 'npcs', 'mobs', 'otherPlayer').
     * @param {string} entityId - The unique ID of the entity clicked.
     */
    handleMinimapInteraction: function(entityType, entityId) {
        console.log(`[handleMinimapInteraction] Received click for type: ${entityType}, ID: ${entityId}`);
        const playerPos = Player.getCurrentZoneAndPosition();
        if (!playerPos || !playerPos.zone) {
            this.addMessage('Cannot interact: Player position unknown.', 'error', true); // Force scroll error
            return;
        }

        // Special handling for other players
        if (entityType === 'otherPlayer') {
            console.log(`[handleMinimapInteraction] Handling other player interaction: ${entityId}`);
            const otherPlayer = this.otherPlayers[entityId];
            if (!otherPlayer) {
                this.addMessage('That player is no longer here.', 'error', true); // Force scroll error
                return;
            }
            this.displayPlayerInfo(otherPlayer);
            return;
        }

        // Normal handling for world objects
        const zone = playerPos.zone;
        const found = World.findObjectInZone(zone.id, entityId);
        if (!found) {
            this.addMessage('That object is no longer here.', 'error', true); // Force scroll error
            return;
        }

        // If it's a horse, call HorseSystem.handleHorseClick if it's the player's horse
        if (found.type === 'horses') {
            const horse = found.object;
            if (horse.ownerId === Player.id && typeof HorseSystem !== 'undefined') {
                HorseSystem.handleHorseClick(horse);
            }
        }

        const obj = { ...found.object, objectType: found.type };
        this.displayEnvironmentElements([obj], zone.id);
    },

    /**
     * Display information about another player in a UI box.
     * @param {Object} playerData - Data for the other player.
     */
    displayPlayerInfo: function(playerData) {
        if (!playerData || !playerData.username) {
            console.error('Invalid player data provided to displayPlayerInfo');
            return;
        }

        // Create a player info box
        const infoBox = document.createElement('div');
        infoBox.className = 'player-info-box';

        // Get player level (default to 1 if not available)
        const playerLevel = playerData.level || 1;

        // Track if we're waiting for player data to avoid duplicate boxes
        if (playerData._requestingData) {
            return; // Don't show a box yet, wait for the data
        }

        // Request full player data from the server if we don't have equipment info
        if (!playerData.equipment && this.socket && this.socket.connected) {
            // Mark that we're requesting data for this player
            playerData._requestingData = true;

            this.socket.emit('request_player_data', { playerId: playerData.id });

            // Set up a one-time listener for the response
            this.socket.once('player_data_response', (data) => {
                if (data && data.playerId === playerData.id) {
                    // Update our stored player data
                    if (this.otherPlayers[playerData.id]) {
                        this.otherPlayers[playerData.id] = {
                            ...this.otherPlayers[playerData.id],
                            ...data.playerData,
                            _requestingData: false // Clear the flag
                        };
                        // Re-display the player info with the updated data
                        this.displayPlayerInfo(this.otherPlayers[playerData.id]);
                    }
                }
            });

            // Show a temporary message
            this.addMessage(`Loading information about ${playerData.username}...`, 'system');
            return; // Don't show the box yet
        }

        // Create description of player's equipment
        let equipmentDescription = 'They aren\'t wearing any special equipment.';

        // Helper function to get item name safely
        const getItemName = (item) => {
            if (!item) return null;

            // If item is an object with a name property, use that
            if (typeof item === 'object' && item.name) {
                return item.name;
            }

            // If item is a string (item ID), try to get the item from World.items
            if (typeof item === 'string') {
                const itemObj = World.getItem(item);
                return itemObj ? itemObj.name : item;
            }

            // Fallback
            return 'Unknown Item';
        };

        // If we have equipment data, build a natural language description
        if (playerData.equipment) {
            // Collect all equipment pieces
            const weapon = playerData.equipment.weapon ? getItemName(playerData.equipment.weapon) : null;
            const shield = playerData.equipment.shield ? getItemName(playerData.equipment.shield) : null;
            const head = playerData.equipment.head ? getItemName(playerData.equipment.head) : null;
            const shirt = playerData.equipment.shirt ? getItemName(playerData.equipment.shirt) : null;
            const chestArmor = playerData.equipment.chest_armor ? getItemName(playerData.equipment.chest_armor) : null;
            const legs = playerData.equipment.legs ? getItemName(playerData.equipment.legs) : null;
            const legArmor = playerData.equipment.leg_armor ? getItemName(playerData.equipment.leg_armor) : null;
            const feet = playerData.equipment.feet ? getItemName(playerData.equipment.feet) : null;
            const cloak = playerData.equipment.cloak ? getItemName(playerData.equipment.cloak) : null;

            // Build a more natural description
            let description = '';

            // Weapon description
            if (weapon) {
                description += `${playerData.username} is armed with a ${weapon}`;
                if (shield) {
                    description += ` and carries a ${shield}`;
                }
                description += '. ';
            }

            // Clothing and armor description
            const clothingPieces = [];

            if (head) clothingPieces.push(`a ${head} on their head`);
            if (shirt) clothingPieces.push(`a ${shirt}`);
            if (chestArmor) clothingPieces.push(`${chestArmor} armor`);
            if (legs) clothingPieces.push(`${legs} pants`);
            if (legArmor) clothingPieces.push(`${legArmor} leg protection`);
            if (feet) clothingPieces.push(`${feet} on their feet`);
            if (cloak) clothingPieces.push(`a ${cloak} draped around their shoulders`);

            if (clothingPieces.length > 0) {
                if (description) {
                    description += 'They are wearing ';
                } else {
                    description += `${playerData.username} is wearing `;
                }

                if (clothingPieces.length === 1) {
                    description += clothingPieces[0] + '.';
                } else if (clothingPieces.length === 2) {
                    description += `${clothingPieces[0]} and ${clothingPieces[1]}.`;
                } else {
                    const lastPiece = clothingPieces.pop();
                    description += `${clothingPieces.join(', ')}, and ${lastPiece}.`;
                }
            } else if (!description) {
                // If no weapon and no clothing
                if (weapon || shield) {
                    // Already described weapon/shield
                } else {
                    description = `${playerData.username} doesn't appear to be wearing any special equipment.`;
                }
            }

            if (description) {
                equipmentDescription = description;
            }
        }

        // Build the HTML content
        infoBox.innerHTML = `
            <div class="player-info-header">
                <div class="player-info-name">${playerData.username}</div>
                <div class="player-info-level">Level ${playerLevel}</div>
            </div>
            <div class="player-info-description">
                <p>${equipmentDescription}</p>
            </div>
            <div class="player-info-actions">
                <button class="player-action-btn add-friend-btn" data-id="${playerData.id}">Add Friend</button>
                <button class="player-action-btn invite-party-btn" data-id="${playerData.id}">Invite to Party</button>
                <button class="player-action-btn trade-btn" data-id="${playerData.id}">Trade</button>
                <button class="player-action-btn whisper-btn" data-id="${playerData.id}">Whisper</button>
            </div>
        `;

        // Add to the game output
        const output = document.getElementById('output');
        if (output) {
            this.addMessage(infoBox.outerHTML, 'normal', true); // Force scroll

            // Add event listeners for the buttons
            setTimeout(() => {
                const addFriendBtn = document.querySelector(`.add-friend-btn[data-id="${playerData.id}"]`);
                const invitePartyBtn = document.querySelector(`.invite-party-btn[data-id="${playerData.id}"]`);
                const tradeBtn = document.querySelector(`.trade-btn[data-id="${playerData.id}"]`);
                const whisperBtn = document.querySelector(`.whisper-btn[data-id="${playerData.id}"]`);

                if (addFriendBtn) {
                    addFriendBtn.addEventListener('click', () => {
                        // Placeholder for friend functionality
                        this.addMessage(`You sent a friend request to ${playerData.username}.`, 'system');
                    });
                }

                if (invitePartyBtn) {
                    invitePartyBtn.addEventListener('click', () => {
                        // Placeholder for party functionality
                        this.addMessage(`You invited ${playerData.username} to join your party.`, 'system');
                    });
                }

                if (tradeBtn) {
                    tradeBtn.addEventListener('click', () => {
                        // Placeholder for trade functionality
                        this.addMessage(`You sent a trade request to ${playerData.username}.`, 'system');
                    });
                }

                if (whisperBtn) {
                    whisperBtn.addEventListener('click', () => {
                        // Placeholder for whisper functionality
                        // Could focus the command input and prefill with /whisper playername
                        const commandInput = document.getElementById('command-input');
                        if (commandInput) {
                            commandInput.value = `/whisper ${playerData.username} `;
                            commandInput.focus();
                        }
                    });
                }
            }, 0);
        }
    },

    /**
     * Opens the refinery popup modal.
     * @param {string} structureId - The ID of the refinery structure being used.
     */
    openRefineryPopup: function(structureId) {
        console.log(`Opening refinery popup for structure: ${structureId}`);
        const refineryModal = document.getElementById('refinery-modal');
        if (refineryModal) {
            // TODO: Populate modal content based on structureId if needed
            refineryModal.classList.add('visible');
            // Populate the refinery items list when opening the popup
            if (typeof UI.populateRefineryList === 'function') {
                UI.populateRefineryList();
            } else {
                console.error('UI.populateRefineryList function not found!');
            }
            // Optionally focus an element within the modal
        } else {
            console.error("Refinery modal element (#refinery-modal) not found!");
            this.addMessage("Error opening refinery interface.", "error", true); // Force scroll error
        }
    },

    defeatAnimal: function(zoneId, animalId) {
        const found = this.findObjectInZone(zoneId, animalId);
        if (!found || found.type !== 'animals') return;
        const animal = found.object;
        if (animal.defeated) return;
        animal.defeated = true;
        animal.defeatTime = Date.now();
        Game.saveGame();
    },

    markAnimalAsLooted: function(zoneId, animalId) {
        const found = this.findObjectInZone(zoneId, animalId);
        if (!found || found.type !== 'animals') return;
        const animal = found.object;
        animal.looted = true;
        Game.saveGame();
    },

    endCombatWithVictory: function() {
        if (!this.currentEnemy || !this.currentZoneId) {
            console.error("Cannot end combat with victory: missing enemy or zone information");
            this.inCombat = false;
            this.currentEnemy = null;
            this.currentZoneId = null;
            return;
        }

        // Mark the enemy as defeated in the world
        if (this.currentEnemy.objectType === 'mobs') {
            World.defeatMob(this.currentZoneId, this.currentEnemy.id);
        } else if (this.currentEnemy.objectType === 'animals') {
            World.defeatAnimal(this.currentZoneId, this.currentEnemy.id);
        }

        // Rest of the function remains the same...
    }
};

// Initialize the game when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Delay init slightly to ensure other scripts might be loaded
    setTimeout(function() { Game.init(); }, 50);
});
